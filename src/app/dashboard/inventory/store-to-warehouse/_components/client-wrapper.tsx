'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { usePermissionCheck } from '@/lib/check-permissions';

// Define the schema for transfer form validation
const transferSchema = z.object({
    productId: z.string().min(1, { message: 'Product is required' }),
    branchId: z.string().min(1, { message: 'Branch is required' }),
    quantity: z.number().min(1, { message: 'Quantity must be at least 1' }),
    notes: z.string().optional(),
});

// Define the type for form values
type TransferFormValues = z.infer<typeof transferSchema>;

// Define types for products, branches, and inventory
interface Product {
    id: string;
    name: string;
    code: string | null;
    costPrice: number;
    retailPrice: number;
    unit?: string;
    isActive?: boolean;
}

interface Branch {
    id: string;
    name: string;
    code: string;
    isMain?: boolean;
    isActive?: boolean;
}

interface InventoryItem {
    id: string;
    productId: string;
    branchId: string;
    quantity: number;
    warehouseStock: number;
    storeStock: number;
    product: Product;
    branch?: Branch;
}

export default function ClientWrapper() {
    // Check if user has permission to transfer inventory
    const { isLoading: permissionLoading } = usePermissionCheck('/dashboard/inventory', 'edit');
    
    const router = useRouter();
    const [inventory, setInventory] = useState<InventoryItem[]>([]);
    const [branches, setBranches] = useState<Branch[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [submitting, setSubmitting] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);
    const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
    const [selectedInventory, setSelectedInventory] = useState<InventoryItem | null>(null);
    const [userRole, setUserRole] = useState<string | null>(null);
    const [userBranchId, setUserBranchId] = useState<string | null>(null);

    const {
        register,
        handleSubmit,
        formState: { errors },
        watch,
        setValue,
        reset
    } = useForm<TransferFormValues>({
        resolver: zodResolver(transferSchema),
        defaultValues: {
            productId: '',
            branchId: '',
            quantity: 1,
            notes: '',
        },
    });

    // Watch for changes in productId and branchId
    const watchProductId = watch('productId');
    const watchBranchId = watch('branchId');
    const watchQuantity = watch('quantity');

    useEffect(() => {
        fetchInventory();
        fetchBranches();
    }, []);

    // Update selected product and inventory when productId or branchId changes
    useEffect(() => {
        if (watchProductId && watchBranchId && inventory.length > 0) {
            const inventoryItem = inventory.find(
                item => item.productId === watchProductId && item.branchId === watchBranchId
            );

            if (inventoryItem) {
                setSelectedInventory(inventoryItem);
                setSelectedProduct(inventoryItem.product);
            } else {
                setSelectedInventory(null);
                // Find the product info even if no inventory exists for this branch
                const productInfo = inventory.find(item => item.productId === watchProductId)?.product;
                setSelectedProduct(productInfo || null);
            }
        } else {
            setSelectedInventory(null);
            setSelectedProduct(null);
        }
    }, [watchProductId, watchBranchId, inventory]);

    const fetchInventory = async () => {
        try {
            setLoading(true);
            setError(null);

            const response = await fetch('/api/inventory');

            if (!response.ok) {
                throw new Error('Failed to fetch inventory data');
            }

            const data = await response.json();

            if (data.success) {
                setInventory(data.inventory);
            } else {
                setError(data.error || 'Failed to load inventory data');
            }
        } catch (err) {
            setError('An error occurred while fetching inventory data');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    const fetchBranches = async () => {
        try {
            setLoading(true);
            setError(null);

            // First get the user session to check role and branchId
            const sessionResponse = await fetch('/api/auth/session');
            if (!sessionResponse.ok) {
                console.error('Failed to fetch session');
                throw new Error('Failed to fetch user session');
            }

            const sessionData = await sessionResponse.json();
            const userRole = sessionData.user?.role;
            const userBranchId = sessionData.user?.branchId;

            setUserRole(userRole);
            setUserBranchId(userBranchId);

            const response = await fetch('/api/branches');

            if (!response.ok) {
                throw new Error('Failed to fetch branches data');
            }

            const data = await response.json();

            if (data.success) {
                if (userRole === 'tenant_sale' && userBranchId) {
                    const userBranch = data.branches.find((b: Branch) => b.id === userBranchId);
                    if (userBranch) {
                        setBranches([userBranch]);
                        setValue('branchId', userBranchId);
                    } else {
                        setBranches([]);
                        setError('Your assigned branch was not found. Please contact your administrator.');
                    }
                } else {
                    setBranches(data.branches);
                }
            } else {
                setError(data.error || 'Failed to load branches data');
            }
        } catch (err) {
            setError('An error occurred while fetching branches data');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    const onSubmit = async (data: TransferFormValues) => {
        try {
            setSubmitting(true);
            setError(null);
            setSuccess(null);

            if (!selectedInventory) {
                throw new Error('No inventory found for the selected product and branch');
            }

            if (selectedInventory.storeStock < data.quantity) {
                throw new Error(`Not enough stock in store. Available: ${selectedInventory.storeStock}`);
            }

            // Create the transfer data
            const transferData = {
                inventoryId: selectedInventory.id,
                productId: data.productId,
                branchId: data.branchId,
                quantity: data.quantity,
                direction: 'store-to-warehouse',
                notes: data.notes || 'Store to Warehouse Transfer',
            };

            console.log('Submitting transfer data:', transferData);

            const response = await fetch('/api/inventory/store-to-warehouse', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(transferData),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to process transfer');
            }

            const result = await response.json();
            setSuccess('Stock transferred successfully from store to warehouse');

            // Refresh inventory data
            fetchInventory();

            // Reset form
            reset();
        } catch (err: any) {
            setError(err.message || 'An error occurred during transfer');
            console.error('Error transferring stock:', err);
        } finally {
            setSubmitting(false);
        }
    };

    // Get unique products from inventory
    const getUniqueProducts = () => {
        const uniqueProducts = new Map();
        
        inventory.forEach(item => {
            if (!uniqueProducts.has(item.productId) && item.storeStock > 0) {
                uniqueProducts.set(item.productId, item.product);
            }
        });
        
        return Array.from(uniqueProducts.values());
    };

    // Get branches that have the selected product with store stock
    const getBranchesWithProduct = (productId: string) => {
        if (!productId) return [];
        
        return inventory
            .filter(item => item.productId === productId && item.storeStock > 0)
            .map(item => item.branch)
            .filter((branch): branch is Branch => branch !== undefined);
    };

    if (permissionLoading || loading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold">Transfer Stock (Store to Warehouse)</h1>
                <button
                    onClick={() => router.push('/dashboard/inventory')}
                    className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 cursor-pointer"
                >
                    Back to Inventory
                </button>
            </div>

            {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">Error: </strong>
                    <span className="block sm:inline">{error}</span>
                </div>
            )}

            {success && (
                <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <span className="block sm:inline">{success}</span>
                </div>
            )}

            {inventory.length === 0 ? (
                <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">No inventory found! </strong>
                    <span className="block sm:inline">Add inventory before attempting transfers.</span>
                    <button
                        onClick={() => router.push('/dashboard/inventory/new')}
                        className="mt-2 bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-1 px-3 rounded focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50"
                    >
                        Add Inventory
                    </button>
                </div>
            ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <h2 className="text-xl font-semibold mb-4">Transfer Form</h2>
                        <form onSubmit={handleSubmit(onSubmit)}>
                            <div className="mb-4">
                                <label htmlFor="productId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Product *
                                </label>
                                <select
                                    id="productId"
                                    {...register('productId')}
                                    className={`w-full border ${errors.productId ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm px-4 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500`}
                                >
                                    <option value="">Select Product</option>
                                    {getUniqueProducts().map((product) => (
                                        <option key={product.id} value={product.id}>
                                            {product.name} {product.code ? `(${product.code})` : ''}
                                        </option>
                                    ))}
                                </select>
                                {errors.productId && (
                                    <p className="mt-1 text-sm text-red-600">{errors.productId.message}</p>
                                )}
                            </div>

                            <div className="mb-4">
                                <label htmlFor="branchId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Branch *
                                </label>
                                <select
                                    id="branchId"
                                    {...register('branchId')}
                                    className={`w-full border ${errors.branchId ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm px-4 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500`}
                                    disabled={!watchProductId || userRole === 'tenant_sale'}
                                >
                                    <option value="">Select Branch</option>
                                    {watchProductId ? (
                                        userRole === 'tenant_sale' && userBranchId ? (
                                            branches.map((branch) => (
                                                <option key={branch.id} value={branch.id}>
                                                    {branch.name} ({branch.code})
                                                </option>
                                            ))
                                        ) : (
                                            getBranchesWithProduct(watchProductId).map((branch) => (
                                                <option key={branch.id} value={branch.id}>
                                                    {branch.name} ({branch.code})
                                                </option>
                                            ))
                                        )
                                    ) : (
                                        <option value="" disabled>Select a product first</option>
                                    )}
                                </select>
                                {errors.branchId && (
                                    <p className="mt-1 text-sm text-red-600">{errors.branchId.message}</p>
                                )}
                            </div>

                            <div className="mb-4">
                                <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Quantity to Transfer *
                                </label>
                                <input
                                    type="number"
                                    id="quantity"
                                    min="1"
                                    max={selectedInventory?.storeStock || 1}
                                    {...register('quantity', { valueAsNumber: true })}
                                    className={`w-full border ${errors.quantity ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm px-4 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500`}
                                />
                                {errors.quantity && (
                                    <p className="mt-1 text-sm text-red-600">{errors.quantity.message}</p>
                                )}
                                {selectedInventory && (
                                    <p className="mt-1 text-sm text-gray-500">
                                        Available in store: {selectedInventory.storeStock} {selectedProduct?.unit || 'units'}
                                    </p>
                                )}
                                {selectedInventory && watchQuantity > selectedInventory.storeStock && (
                                    <p className="mt-1 text-sm text-red-600">
                                        Not enough stock in store. Maximum available: {selectedInventory.storeStock}
                                    </p>
                                )}
                            </div>

                            <div className="mb-6">
                                <label htmlFor="notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Notes (Optional)
                                </label>
                                <textarea
                                    id="notes"
                                    {...register('notes')}
                                    rows={3}
                                    className="w-full border border-gray-300 rounded-md shadow-sm px-4 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Add any additional notes about this transfer"
                                ></textarea>
                            </div>

                            <div className="flex justify-end">
                                <button
                                    type="submit"
                                    disabled={submitting || !selectedInventory || (selectedInventory && watchQuantity > selectedInventory.storeStock)}
                                    className={`bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 ${
                                        submitting || !selectedInventory || (selectedInventory && watchQuantity > selectedInventory.storeStock)
                                            ? 'opacity-50 cursor-not-allowed'
                                            : 'cursor-pointer'
                                    }`}
                                >
                                    {submitting ? 'Processing...' : 'Transfer Stock'}
                                </button>
                            </div>
                        </form>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <h2 className="text-xl font-semibold mb-4">Current Stock Information</h2>
                        
                        {selectedInventory ? (
                            <div>
                                <div className="mb-4">
                                    <h3 className="text-lg font-medium">{selectedProduct?.name}</h3>
                                    {selectedProduct?.code && (
                                        <p className="text-sm text-gray-500">Code: {selectedProduct.code}</p>
                                    )}
                                </div>
                                
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                    <div className="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
                                        <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">Warehouse Stock</h4>
                                        <p className="text-2xl font-bold text-blue-600 dark:text-blue-300">
                                            {selectedInventory.warehouseStock} {selectedProduct?.unit || 'units'}
                                        </p>
                                    </div>
                                    
                                    <div className="bg-purple-50 dark:bg-purple-900 p-4 rounded-lg">
                                        <h4 className="text-sm font-medium text-purple-800 dark:text-purple-200 mb-2">Store Stock</h4>
                                        <p className="text-2xl font-bold text-purple-600 dark:text-purple-300">
                                            {selectedInventory.storeStock} {selectedProduct?.unit || 'units'}
                                        </p>
                                    </div>
                                </div>
                                
                                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4">
                                    <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-2">Total Quantity</h4>
                                    <p className="text-2xl font-bold text-gray-700 dark:text-gray-300">
                                        {selectedInventory.quantity} {selectedProduct?.unit || 'units'}
                                    </p>
                                </div>
                                
                                <div className="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
                                    <h4 className="text-sm font-medium text-green-800 dark:text-green-200 mb-2">After Transfer</h4>
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <p className="text-sm text-green-700 dark:text-green-300">Warehouse:</p>
                                            <p className="text-xl font-bold text-green-600 dark:text-green-300">
                                                {selectedInventory.warehouseStock + watchQuantity} {selectedProduct?.unit || 'units'}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-green-700 dark:text-green-300">Store:</p>
                                            <p className="text-xl font-bold text-green-600 dark:text-green-300">
                                                {Math.max(0, selectedInventory.storeStock - watchQuantity)} {selectedProduct?.unit || 'units'}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <p className="text-gray-500 dark:text-gray-400">
                                    Select a product and branch to view stock information
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
}
