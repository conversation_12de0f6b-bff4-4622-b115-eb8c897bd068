import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { orders, payments, inventoryTransactions, userMenuPermissions } from "@/db/schema";
import { auth } from "../../../../../auth";
import { getTenantId } from "@/lib/auth-utils";
import { eq, and } from "drizzle-orm";
import { withMenuPermission, checkMenuPermission } from "@/middleware/menuPermissions";

interface RouteParams {
  params: Promise<{ id: string }>;
}

async function getHandler(request: NextRequest, { params }: RouteParams, permissionCheck: any) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Check if user has view permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      const menuPermission = permissionCheck.permissions.find(
        (p: any) => p.menu.path === '/dashboard/sales'
      );

      if (!menuPermission?.canView) {
        return NextResponse.json(
          { success: false, error: "You do not have permission to view sales" },
          { status: 403 }
        );
      }
    }

    const tenantId = await getTenantId();
    if (!tenantId) {
      return NextResponse.json(
        { message: "Tenant not found" },
        { status: 404 }
      );
    }

    const { id } = await params;
    if (!id) {
      return NextResponse.json(
        { message: "Order ID is required" },
        { status: 400 }
      );
    }

    const order = await db.query.orders.findFirst({
      where: (orders, { eq, and }) =>
        and(eq(orders.id, id), eq(orders.tenantId, tenantId)),
      with: {
        customer: true,
        items: {
          with: {
            product: {
              with: {
                category: true,
                vendor: true,
              },
            },
          },
        },
        performer: {
          columns: {
            id: true,
            fullName: true,
            username: true,
          },
        },
      },
    });

    if (!order) {
      return NextResponse.json({ message: "Order not found" }, { status: 404 });
    }

    return NextResponse.json(order);
  } catch (error) {
    console.error("Error fetching order:", error);
    return NextResponse.json(
      { message: "Failed to fetch order", error: (error as Error).message },
      { status: 500 }
    );
  }
}

async function deleteHandler(request: NextRequest, { params }: RouteParams, permissionCheck: any) {
  console.log('DELETE handler called for sale');
  try {
    const session = await auth();
    console.log('Session:', session?.user?.id ? 'Authenticated' : 'Not authenticated', 'Role:', session?.user?.role);

    if (!session?.user?.id) {
      console.log('Unauthorized: No user ID in session');
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Check if user has delete permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      console.log('Checking permissions for tenant_sale user');
      const menuPermission = permissionCheck.permissions.find(
        (p: any) => p.menu.path === '/dashboard/sales'
      );
      console.log('Menu permission found:', menuPermission ? 'Yes' : 'No', 'Can delete:', menuPermission?.canDelete);

      if (!menuPermission?.canDelete) {
        console.log('Permission denied: User does not have delete permission');
        return NextResponse.json(
          { success: false, error: "You do not have permission to delete sales" },
          { status: 403 }
        );
      }
    }

    const tenantId = session.user.tenantId || session.user.id;
    console.log('Tenant ID:', tenantId);

    if (!tenantId) {
      console.log('Tenant not found');
      return NextResponse.json(
        { message: "Tenant not found" },
        { status: 404 }
      );
    }

    const { id } = await params;
    console.log('Sale ID to delete:', id);

    if (!id) {
      console.log('Order ID is required but not provided');
      return NextResponse.json(
        { message: "Order ID is required" },
        { status: 400 }
      );
    }

    // Check if order exists and belongs to this tenant
    console.log('Checking if order exists for tenant:', tenantId);
    const order = await db.query.orders.findFirst({
      where: (orders, { eq, and }) =>
        and(eq(orders.id, id), eq(orders.tenantId, tenantId)),
    });

    if (!order) {
      console.log('Order not found for this tenant');
      return NextResponse.json({ message: "Order not found" }, { status: 404 });
    }

    console.log('Order found, proceeding with deletion');

    // Use a transaction to ensure all related records are deleted
    await db.transaction(async (tx) => {
      console.log('Starting transaction for deletion');

      // 1. First, delete any payment records associated with this order
      console.log('Deleting payment records for order:', id);
      const paymentResult = await tx.delete(payments).where(eq(payments.orderId, id));
      console.log('Payment deletion result:', paymentResult);

      // 2. Delete any inventory transactions related to this sale
      console.log('Deleting inventory transactions for order:', id);
      const inventoryResult = await tx.delete(inventoryTransactions)
        .where(
          and(
            eq(inventoryTransactions.type, 'sale'),
            eq(inventoryTransactions.notes, `Sale: ${order.memoNo}`)
          )
        );
      console.log('Inventory transaction deletion result:', inventoryResult);

      // 3. Finally, delete the order (cascade will delete order items)
      console.log('Deleting order:', id);
      const orderResult = await tx.delete(orders).where(eq(orders.id, id));
      console.log('Order deletion result:', orderResult);
    });

    console.log('Order and all related records deleted successfully');

    return NextResponse.json({ message: "Order deleted successfully" });
  } catch (error) {
    console.error("Error deleting order:", error);
    return NextResponse.json(
      { message: "Failed to delete order", error: (error as Error).message },
      { status: 500 }
    );
  }
}

// Export the route handlers with permission middleware
export const GET = withMenuPermission(getHandler, '/dashboard/sales');
export const DELETE = withMenuPermission(deleteHandler, '/dashboard/sales');

// For debugging purposes, log when the module is loaded
console.log('Sales [id] route handlers registered - GET and DELETE');