"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { toast } from "sonner";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface DeleteSaleButtonProps {
    id: string;
}

export function DeleteSaleButton({ id }: DeleteSaleButtonProps) {
    const router = useRouter();
    const [open, setOpen] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);

    // Use a simple form submission approach instead of fetch
    const handleFormSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setIsDeleting(true);

        // Create a form element
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/api/sales/${id}/delete`;
        form.style.display = 'none';
        document.body.appendChild(form);

        // Submit the form
        form.submit();

        // Clean up
        setTimeout(() => {
            document.body.removeChild(form);
            setIsDeleting(false);
            setOpen(false);

            // Show success message and redirect
            toast.success("Sale deletion in progress...");
            setTimeout(() => {
                router.push('/dashboard/sales');
                router.refresh();
            }, 500);
        }, 100);
    };

    return (
        <>
            <Button
                variant="outline"
                className="text-red-500 hover:text-red-700 hover:bg-red-50 cursor-pointer"
                onClick={() => setOpen(true)}
            >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Sale
            </Button>

            <AlertDialog open={open} onOpenChange={setOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action cannot be undone. This will permanently delete the sale
                            and all associated records.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={(e) => {
                                e.preventDefault();
                                handleFormSubmit(e);
                            }}
                            disabled={isDeleting}
                            className="bg-red-500 hover:bg-red-600"
                        >
                            {isDeleting ? "Deleting..." : "Delete"}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
}