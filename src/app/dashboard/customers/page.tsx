import React from "react";
import { db } from "@/lib/db";
import { customers } from "@/db/schema";
import { desc } from "drizzle-orm";
import Link from "next/link";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { auth } from "~/auth";
import { redirect } from "next/navigation";
import { Metadata } from "next";
import { formatCurrency } from "@/lib/utils";
import { CustomersSearchForm } from "./components/search-form";
import ClientWrapper from "./components/client-wrapper";

export const metadata: Metadata = {
    title: "Customers",
    description: "Manage your customers",
};

export default async function CustomersPage({
    searchParams,
}: {
    searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
    const session = await auth();
    if (!session?.user) {
        redirect("/api/auth/signin");
    }

    const tenantId = session.user.tenantId || session.user.id;
    if (!tenantId) {
        redirect("/dashboard");
    }

    const params = await searchParams;
    const filter = params.filter;
    const filterDue = filter === 'due';
    const searchQuery = params.search as string | undefined;

    // Get all customers for this tenant with optional due filter and search
    let queryParams = new URLSearchParams();
    if (searchQuery) {
        queryParams.set('search', searchQuery);
    }

    const queryString = queryParams.toString();
    const apiEndpoint = queryString ?
        `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/customers?${queryString}` :
        `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/customers`;

    // Get all active customers data
    const allCustomers = await db.query.customers.findMany({
        where: (customers, { eq, and }) => and(
            eq(customers.tenantId, tenantId),
            eq(customers.isActive, true)
        ),
        orderBy: [desc(customers.createdAt)],
    });

    // Apply client-side filtering
    let filteredCustomers = [...allCustomers];

    if (searchQuery) {
        const searchTermLower = searchQuery.toLowerCase();
        filteredCustomers = filteredCustomers.filter(customer =>
            (customer.name && customer.name.toLowerCase().includes(searchTermLower)) ||
            (customer.code && customer.code.toLowerCase().includes(searchTermLower)) ||
            (customer.phone && customer.phone.toLowerCase().includes(searchTermLower))
        );
    }

    if (filterDue) {
        filteredCustomers = filteredCustomers.filter(customer => customer.currentBalance && customer.currentBalance > 0);
        // Sort by balance for due view
        filteredCustomers.sort((a, b) => (b.currentBalance || 0) - (a.currentBalance || 0));
    }

    return (
        <div className="container mx-auto py-10">
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h1 className="text-3xl font-bold">
                        {filterDue ? "Customers with Outstanding Balance" : "Customers Management"}
                    </h1>
                    {filterDue && (
                        <p className="text-muted-foreground mt-1">
                            Showing customers with pending dues
                        </p>
                    )}
                </div>
            </div>

            <Card>
                <CardHeader>
                    <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
                        <div>
                            <CardTitle>
                                {filterDue ? "Customers with Outstanding Balance" : "All Customers"}
                            </CardTitle>
                            <CardDescription>
                                {filterDue
                                    ? "Customers with pending due payments"
                                    : "Manage your customers and their details"}
                            </CardDescription>
                        </div>
                        <CustomersSearchForm defaultValue={searchQuery} />
                    </div>
                </CardHeader>
                <CardContent>
                    <ClientWrapper
                        customers={filteredCustomers.map(customer => ({
                            ...customer,
                            type: customer.type || 'retail' // Ensure type is never null
                        }))}
                        filterDue={filterDue}
                        searchQuery={searchQuery}
                    />
                </CardContent>
            </Card>
        </div>
    );
}
