"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { toast } from "sonner";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { deleteSale } from "../_actions/delete-sale";

interface ServerActionDeleteButtonProps {
    id: string;
}

export function ServerActionDeleteButton({ id }: ServerActionDeleteButtonProps) {
    const router = useRouter();
    const [open, setOpen] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);

    const handleDelete = async () => {
        setIsDeleting(true);

        try {
            console.log(`Attempting to delete sale with ID: ${id} using server action`);
            const result = await deleteSale(id);
            console.log('Delete result:', result);

            if (!result.success) {
                throw new Error(result.error || "Failed to delete sale");
            }

            toast.success("Sale deleted successfully and product stock restored");
            router.push('/dashboard/sales');
            router.refresh();
        } catch (error) {
            console.error("Error deleting sale:", error);
            toast.error(error instanceof Error ? error.message : "Failed to delete sale");
        } finally {
            setIsDeleting(false);
            setOpen(false);
        }
    };

    return (
        <>
            <Button
                variant="outline"
                className="text-red-500 hover:text-red-700 hover:bg-red-50 cursor-pointer"
                onClick={() => setOpen(true)}
            >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Sale
            </Button>

            <AlertDialog open={open} onOpenChange={setOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action cannot be undone. This will permanently delete the sale
                            and all associated records.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={(e) => {
                                e.preventDefault();
                                handleDelete();
                            }}
                            disabled={isDeleting}
                            className="bg-red-500 hover:bg-red-600"
                        >
                            {isDeleting ? "Deleting..." : "Delete"}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
}
