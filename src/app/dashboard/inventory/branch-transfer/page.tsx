'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';

interface Branch {
    id: string;
    name: string;
    code: string;
    isActive: boolean;
}

interface Product {
    id: string;
    name: string;
    code: string;
    unit: string;
}

interface InventoryItem {
    productId: string;
    branchId: string;
    quantity: number;
}

const formSchema = z.object({
    productId: z.string().min(1, { message: 'Product is required' }),
    quantity: z.coerce.number().int().min(1, { message: 'Quantity must be at least 1' }),
    branchId: z.string().min(1, { message: 'Branch is required' }),
    notes: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

export default function BranchTransferPage() {
    const router = useRouter();
    const [branches, setBranches] = useState<Branch[]>([]);
    const [products, setProducts] = useState<Product[]>([]);
    const [inventory, setInventory] = useState<InventoryItem[]>([]);
    const [loading, setLoading] = useState(true);
    const [submitting, setSubmitting] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
    const [selectedBranch, setSelectedBranch] = useState<Branch | null>(null);
    const [availableQuantity, setAvailableQuantity] = useState<number>(0);

    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            productId: '',
            quantity: 1,
            branchId: '',
            notes: '',
        },
    });

    // Watch for form value changes
    const watchProductId = form.watch('productId');
    const watchBranchId = form.watch('branchId');
    const watchQuantity = form.watch('quantity');

    // Fetch branches
    const fetchBranches = async () => {
        try {
            // The API will determine tenantId from the session
            const response = await fetch('/api/branches');
            const data = await response.json();

            if (data.success) {
                setBranches(data.branches);

                // If branches are available, set the first one as default branch
                if (data.branches.length > 0) {
                    const defaultBranch = data.branches[0];
                    form.setValue('branchId', defaultBranch.id);
                    setSelectedBranch(defaultBranch);
                }
            } else {
                toast.error(data.error || 'Failed to load branches');
            }
        } catch (error) {
            console.error('Error fetching branches:', error);
            toast.error('An error occurred while fetching branches');
        }
    };

    // Fetch products
    const fetchProducts = async () => {
        try {
            const response = await fetch('/api/products');
            const data = await response.json();

            if (data.success) {
                setProducts(data.products);
            } else {
                toast.error(data.error || 'Failed to load products');
            }
        } catch (error) {
            console.error('Error fetching products:', error);
            toast.error('An error occurred while fetching products');
        }
    };

    // Fetch inventory for the selected branch and product
    const fetchInventory = async () => {
        if (!watchBranchId || !watchProductId) return;

        try {
            const response = await fetch(`/api/inventory?branchId=${watchBranchId}&productId=${watchProductId}`);
            const data = await response.json();

            if (data.success) {
                // Find the inventory item
                const inventoryItem = data.inventory.find(
                    (item: InventoryItem) =>
                        item.productId === watchProductId &&
                        item.branchId === watchBranchId
                );
                console.log('Inventory item:', inventoryItem);

                if (inventoryItem) {
                    setAvailableQuantity(inventoryItem.quantity);
                } else {
                    setAvailableQuantity(0);
                }
            } else {
                setAvailableQuantity(0);
                toast.error(data.error || 'Failed to load inventory');
            }
        } catch (error) {
            console.error('Error fetching inventory:', error);
            toast.error('An error occurred while fetching inventory');
            setAvailableQuantity(0);
        }
    };

    // Initial data loading
    useEffect(() => {
        const loadData = async () => {
            setLoading(true);
            await Promise.all([fetchBranches(), fetchProducts()]);
            setLoading(false);
        };

        loadData();
    }, []);

    // We're now handling product selection directly in the Select component's onValueChange

    // We're now handling branch selection directly in the Select component's onValueChange

    // Fetch inventory when product or branch changes
    useEffect(() => {
        fetchInventory();
    }, [watchProductId, watchBranchId]);

    const onSubmit = async (values: FormValues) => {
        if (values.quantity > availableQuantity) {
            form.setError('quantity', {
                type: 'manual',
                message: `Cannot transfer more than available quantity (${availableQuantity})`,
            });
            return;
        }

        setSubmitting(true);

        try {
            const response = await fetch('/api/inventory/transfer', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(values),
            });

            const data = await response.json();

            if (data.success) {
                const branchName = selectedBranch ? selectedBranch.name : 'selected branch';
                toast.success(`Inventory transferred successfully to ${branchName}`);
                // Reset form and refetch data
                form.reset({
                    productId: '',
                    quantity: 1,
                    branchId: values.branchId, // Keep the same branch
                    notes: '',
                });
                setAvailableQuantity(0);
            } else {
                toast.error(data.error || 'Failed to transfer inventory');
            }
        } catch (error) {
            console.error('Error transferring inventory:', error);
            toast.error('An error occurred while transferring inventory');
        } finally {
            setSubmitting(false);
        }
    };

    return (
        <div className="container mx-auto py-6">
            <div className="mb-6">
                <h1 className="text-2xl font-bold">Inventory Transfer</h1>
                <p className="text-gray-500">Transfer inventory within a branch</p>
            </div>

            {loading ? (
                <div className="flex justify-center items-center h-64">
                    <p>Loading...</p>
                </div>
            ) : branches.length < 1 ? (
                <Card>
                    <CardHeader>
                        <CardTitle>No Branches Available</CardTitle>
                        <CardDescription>
                            You need at least one branch to perform inventory transfers.
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Button onClick={() => router.push('/dashboard/branches')}>
                            Manage Branches
                        </Button>
                    </CardContent>
                </Card>
            ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Transfer Details</CardTitle>
                            <CardDescription>
                                Fill the form to transfer inventory within a branch
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Form {...form}>
                                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                                    <FormField
                                        control={form.control}
                                        name="branchId"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Select Branch</FormLabel>
                                                <Select
                                                    onValueChange={(value) => {
                                                        field.onChange(value);
                                                        // Update selected branch for display
                                                        const branch = branches.find(b => b.id === value);
                                                        setSelectedBranch(branch || null);
                                                    }}
                                                    defaultValue={field.value}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Select a branch by name">
                                                                {selectedBranch ? `${selectedBranch.name} (${selectedBranch.code})` : "Select a branch"}
                                                            </SelectValue>
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {branches
                                                            .filter(branch => branch.isActive === true)
                                                            .map((branch) => (
                                                                <SelectItem key={branch.id} value={branch.id}>
                                                                    {branch.name} ({branch.code})
                                                                </SelectItem>
                                                            ))
                                                        }
                                                    </SelectContent>
                                                </Select>
                                                <p className="text-xs text-gray-500 mt-1">
                                                    Branch ID will be stored in the database
                                                </p>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="productId"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Product</FormLabel>
                                                <Select
                                                    onValueChange={(value) => {
                                                        field.onChange(value);
                                                        const product = products.find(p => p.id === value);
                                                        setSelectedProduct(product || null);
                                                    }}
                                                    defaultValue={field.value}
                                                >
                                                    <FormControl>
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Select a product">
                                                                {selectedProduct ? selectedProduct.name : "Select a product"}
                                                            </SelectValue>
                                                        </SelectTrigger>
                                                    </FormControl>
                                                    <SelectContent>
                                                        {products.map((product) => (
                                                            <SelectItem key={product.id} value={product.id} className="flex justify-between">
                                                                <div className="flex-1">{product.name}</div>
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <FormMessage />
                                                {selectedProduct && (
                                                    <div className="mt-2 text-sm text-gray-500">
                                                        Unit: {selectedProduct.unit || 'units'}
                                                    </div>
                                                )}
                                            </FormItem>
                                        )}
                                    />

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <FormField
                                            control={form.control}
                                            name="quantity"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Quantity</FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            min={1}
                                                            {...field}
                                                            onChange={(e) => {
                                                                const value = parseInt(e.target.value);
                                                                field.onChange(isNaN(value) ? 0 : value);
                                                            }}
                                                        />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <div className="flex flex-col justify-end">
                                            <div className="text-sm font-medium">Available Quantity</div>
                                            <div className="text-3xl font-bold">
                                                <span className={availableQuantity > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                                                    {availableQuantity}
                                                </span>
                                                {selectedProduct && (
                                                    <span className="text-sm font-normal ml-1 text-gray-500">
                                                        {selectedProduct.unit || 'units'}
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                    </div>



                                    <FormField
                                        control={form.control}
                                        name="notes"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Notes</FormLabel>
                                                <FormControl>
                                                    <Textarea rows={3} {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    {selectedProduct && selectedBranch && watchProductId && watchBranchId && (
                                        <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700 mb-4">
                                            <div>
                                                <div className="text-sm text-gray-500 dark:text-gray-400">Transfer Quantity:</div>
                                                <div className="text-xl font-bold text-gray-700 dark:text-gray-300">
                                                    {watchQuantity} <span className="text-sm font-normal">{selectedProduct.unit || 'units'}</span>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <div className="text-sm text-gray-500 dark:text-gray-400">Available:</div>
                                                <div className="text-xl font-bold text-gray-700 dark:text-gray-300">
                                                    {availableQuantity} <span className="text-sm font-normal">{selectedProduct.unit || 'units'}</span>
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    <Button
                                        type="submit"
                                        disabled={submitting || !watchProductId || !watchBranchId}
                                        className="w-full"
                                    >
                                        {submitting ? 'Processing...' : 'Transfer Inventory'}
                                    </Button>
                                </form>
                            </Form>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Transfer Information</CardTitle>
                            <CardDescription>
                                How inventory transfers work
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h3 className="font-semibold text-lg">What is an Inventory Transfer?</h3>
                                <p className="text-gray-500">
                                    Inventory transfers allow you to move inventory within a branch.
                                    This is useful for tracking inventory movements and adjustments.
                                </p>
                            </div>

                            <div>
                                <h3 className="font-semibold text-lg">Transfer Process</h3>
                                <ol className="list-decimal list-inside space-y-1 text-gray-500">
                                    <li>Select the branch where inventory is located</li>
                                    <li>Choose the product to transfer</li>
                                    <li>Enter the quantity (cannot exceed available quantity)</li>
                                    <li>Add optional notes for record-keeping</li>
                                    <li>Submit the transfer</li>
                                </ol>
                            </div>

                            <div>
                                <h3 className="font-semibold text-lg">Important Notes</h3>
                                <ul className="list-disc list-inside space-y-1 text-gray-500">
                                    <li>Transfers are immediate and cannot be reversed</li>
                                    <li>You can only transfer what's available in the branch</li>
                                    <li>All transfers are logged for audit purposes</li>
                                </ul>
                            </div>

                            <div className="pt-4">
                                <Button variant="outline" onClick={() => router.push('/dashboard/inventory')}>
                                    Back to Inventory
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}
        </div>
    );
}