import { auth } from "../../../../auth";
import { redirect } from "next/navigation";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Settings, Users, RefreshCw, Database } from "lucide-react";

export default async function SettingsPage() {
  const session = await auth();
  
  // Redirect to login if not authenticated
  if (!session?.user) {
    redirect("/");
  }
  
  // Only admin and tenant users can access settings
  if (session.user.role !== 'admin' && session.user.role !== 'tenant') {
    redirect("/dashboard");
  }

  const settingsItems = [
    {
      title: "Refund Reasons",
      description: "Manage refund reasons for processing returns",
      href: "/dashboard/settings/refund-reasons",
      icon: RefreshCw,
      adminOnly: false
    },
    {
      title: "User Management",
      description: "Manage system users and permissions",
      href: "/dashboard/users",
      icon: Users,
      adminOnly: true
    },
    {
      title: "System Configuration",
      description: "Configure system-wide settings",
      href: "/dashboard/settings/system",
      icon: Database,
      adminOnly: true
    }
  ];

  // Filter items based on user role
  const availableItems = settingsItems.filter(item => 
    !item.adminOnly || session.user.role === 'admin'
  );

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Settings className="h-6 w-6" />
            Settings
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage system settings and configurations
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {availableItems.map((item) => {
          const IconComponent = item.icon;
          return (
            <Card key={item.href} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <IconComponent className="h-5 w-5" />
                  {item.title}
                </CardTitle>
                <CardDescription>
                  {item.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href={item.href}>
                  <Button className="w-full">
                    Manage
                  </Button>
                </Link>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {session.user.role === 'admin' && (
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>Admin Tools</CardTitle>
              <CardDescription>
                Additional administrative functions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4">
                <Link href="/dashboard/tenants">
                  <Button variant="outline">
                    Manage Tenants
                  </Button>
                </Link>
                <Link href="/dashboard/vendors">
                  <Button variant="outline">
                    Manage Vendors
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
