import { db } from "@/lib/db";
import { customers } from "@/db/schema";
import { notFound, redirect } from "next/navigation";
import { eq, and } from "drizzle-orm";
import { Metadata } from "next";
import Link from "next/link";
import { auth } from "~/auth";
import ClientWrapper from "./_components/client-wrapper";
import { Button } from "@/components/ui/button";
import { ChevronLeft, UserCog } from "lucide-react";

export const metadata: Metadata = {
    title: "Edit Customer",
    description: "Edit customer details",
};

export default async function EditCustomerPage({
    params,
}: {
    params: Promise<{ id: string }>;
}) {
    const session = await auth();
    if (!session?.user) {
        redirect("/api/auth/signin");
    }

    // Get tenant ID based on user role
    const tenantId = session.user.role === 'tenant_sale' && session.user.tenantId
        ? session.user.tenantId
        : session.user.id;
    if (!tenantId) {
        redirect("/dashboard");
    }

    const { id } = await params;
    const customerId = id;

    // Fetch existing customer data
    const customer = await db.query.customers.findFirst({
        where: (customers, { eq, and }) =>
            and(eq(customers.id, customerId), eq(customers.tenantId, tenantId)),
    });

    // Handle case when customer not found
    if (!customer) {
        notFound();
    }

    // Prepare default values for the form
    const defaultValues = {
        name: customer.name,
        code: customer.code || "",
        phone: customer.phone || "",
        email: customer.email || "",
        address: customer.address || "",
        type: customer.type as "retail" | "wholesale",
        creditLimit: customer.creditLimit || 0,
        extraCommission: customer.extraCommission || 0,
        notes: customer.notes || "",
    };

    return (
        <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white bg-[url('/subtle-pattern.png')] bg-opacity-50">
            <div className="container max-w-6xl mx-auto">
                <div className="flex justify-between items-center mb-4">
                    <h1 className="text-xl font-bold mb-2 flex items-center">
                        <span className="bg-white/20 p-2 rounded-full mr-3 shadow-inner">
                            <UserCog className="h-4 w-4" />
                        </span>
                        Edit Customer
                    </h1>
                    <Link
                        href={`/dashboard/customers/${customerId}`}
                        className="flex items-center gap-2 bg-gray-900 text-white px-4 py-3 rounded-lg transition-all duration-200 backdrop-blur-sm border border-white/20 shadow-sm self-start md:self-center relative z-10"
                    >
                        <ChevronLeft className="h-4 w-4" />
                        Back to Customer
                    </Link>
                </div>

                <ClientWrapper
                    defaultValues={defaultValues}
                    customerId={customerId}
                    tenantId={tenantId}
                />
            </div>
        </div>
    );
}