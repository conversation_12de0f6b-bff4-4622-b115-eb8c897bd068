import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { orders, orderItems } from "@/db/schema";
import { auth } from "~/auth";
import { eq, and, between, gte, lte, inArray } from "drizzle-orm";

// This is a test endpoint without the permission middleware
export async function GET(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const fromDate = url.searchParams.get("fromDate");
    const toDate = url.searchParams.get("toDate");
    const branchId = url.searchParams.get("branchId");
    const productId = url.searchParams.get("productId");

    // Get tenant ID from session based on user role
    let tenantId: string;
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      // For sales users, use their associated tenant's ID
      tenantId = session.user.tenantId;
    } else {
      // For tenant users, use their own ID
      tenantId = session.user.id;
    }

    // Build where conditions
    const whereConditions = [eq(orders.tenantId, tenantId)];

    // Add date range filter if provided
    if (fromDate && toDate) {
      whereConditions.push(
        between(
          orders.date,
          new Date(fromDate),
          new Date(toDate)
        )
      );
    } else if (fromDate) {
      whereConditions.push(gte(orders.date, new Date(fromDate)));
    } else if (toDate) {
      whereConditions.push(lte(orders.date, new Date(toDate)));
    }

    // Add branch filter if provided
    if (branchId) {
      whereConditions.push(eq(orders.branchId, branchId));
    } else if (session.user.role === 'tenant_sale' && session.user.branchId) {
      // For tenant_sale users, restrict to their branch if no specific branch is selected
      whereConditions.push(eq(orders.branchId, session.user.branchId));
    }

    // If product filter is provided, we need to handle it differently
    if (productId) {
      // First get all order IDs that contain this product
      const orderItemsWithProduct = await db.query.orderItems.findMany({
        where: eq(orderItems.productId, productId),
        columns: {
          orderId: true
        }
      });
      
      const orderIds = orderItemsWithProduct.map(item => item.orderId);
      
      if (orderIds.length === 0) {
        // No orders found with this product
        return NextResponse.json({
          success: true,
          orders: []
        });
      }
      
      // Add order IDs to where conditions
      whereConditions.push(inArray(orders.id, orderIds));
    }

    // Get orders with filters
    const filteredOrders = await db.query.orders.findMany({
      where: and(...whereConditions),
      orderBy: (orders, { desc }) => [desc(orders.date)],
      with: {
        customer: {
          columns: {
            name: true
          }
        },
        branch: {
          columns: {
            name: true
          }
        },
        items: {
          with: {
            product: {
              columns: {
                name: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      orders: filteredOrders
    });
  } catch (error) {
    console.error("Error generating order report:", error);
    return NextResponse.json(
      { 
        success: false, 
        error: "Failed to generate order report",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
