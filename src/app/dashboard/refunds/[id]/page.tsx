'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { use } from 'react';
import { formatDate, formatCurrency } from '@/lib/utils';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from 'sonner';

interface RefundItem {
  id: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  total: number;
  isDamaged: boolean;
  product: {
    id: string;
    name: string;
    code: string | null;
  };
}

interface Refund {
  id: string;
  tenantId: string;
  branchId: string;
  orderId: string | null;
  customerId: string | null;
  reasonId: string;
  refundDate: string;
  totalAmount: number;
  notes: string | null;
  isDamaged: boolean;
  performedBy: string;
  createdAt: string;
  updatedAt: string;
  customer: {
    id: string;
    name: string;
  } | null;
  order: {
    id: string;
    memoNo: string;
  } | null;
  reason: {
    id: string;
    name: string;
  };
  branch: {
    id: string;
    name: string;
    code: string;
  };
  performer: {
    id: string;
    fullName: string;
  };
  items: RefundItem[];
}

export default function RefundDetailsPage({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap the params Promise using React.use()
  const resolvedParams = use(params);
  const refundId = resolvedParams.id;

  const router = useRouter();
  const [refund, setRefund] = useState<Refund | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    fetchRefundDetails();
  }, [refundId]);

  const fetchRefundDetails = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/refunds/${refundId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch refund details: ${response.statusText}`);
      }

      try {
        const data = await response.json();

        if (data.success) {
          setRefund(data.refund);
        } else {
          setError(data.error || 'Failed to load refund details');
        }
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError);
        setError('Invalid response format from server');
      }
    } catch (error) {
      console.error('Error fetching refund details:', error);
      setError('An error occurred while fetching refund details');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-8">Loading refund details...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
        <div className="text-center">
          <Link href="/dashboard/refunds">
            <Button variant="outline">Back to Refunds</Button>
          </Link>
        </div>
      </div>
    );
  }

  if (!refund) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-8">Refund not found</div>
        <div className="text-center">
          <Link href="/dashboard/refunds">
            <Button variant="outline">Back to Refunds</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Refund Details</h1>
        <Link href="/dashboard/refunds">
          <Button variant="outline">Back to Refunds</Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Refund Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Refund Summary</CardTitle>
            <CardDescription>
              Basic information about this refund
            </CardDescription>
          </CardHeader>
          <CardContent>
            <dl className="space-y-2">
              <div className="flex justify-between">
                <dt className="font-medium">Refund ID:</dt>
                <dd>{refund.id}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="font-medium">Date:</dt>
                <dd>{formatDate(refund.refundDate)}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="font-medium">Status:</dt>
                <dd>
                  <Badge
                    className={refund.isDamaged ? "bg-red-500" : "bg-green-500"}
                  >
                    {refund.isDamaged ? 'Damaged' : 'Refunded'}
                  </Badge>
                </dd>
              </div>
              <div className="flex justify-between">
                <dt className="font-medium">Total Amount:</dt>
                <dd className="font-bold">{formatCurrency(refund.totalAmount)}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        {/* Customer & Order Info */}
        <Card>
          <CardHeader>
            <CardTitle>Customer & Order</CardTitle>
            <CardDescription>
              Related customer and order information
            </CardDescription>
          </CardHeader>
          <CardContent>
            <dl className="space-y-2">
              <div className="flex justify-between">
                <dt className="font-medium">Customer:</dt>
                <dd>{refund.customer?.name || 'N/A'}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="font-medium">Order #:</dt>
                <dd>{refund.order?.memoNo || 'N/A'}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="font-medium">Branch:</dt>
                <dd>{refund.branch?.name || 'N/A'}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="font-medium">Reason:</dt>
                <dd>{refund.reason?.name || 'N/A'}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        {/* Additional Info */}
        <Card>
          <CardHeader>
            <CardTitle>Additional Information</CardTitle>
            <CardDescription>
              Other details about this refund
            </CardDescription>
          </CardHeader>
          <CardContent>
            <dl className="space-y-2">
              <div className="flex justify-between">
                <dt className="font-medium">Processed By:</dt>
                <dd>{refund.performer?.fullName || 'N/A'}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="font-medium">Created:</dt>
                <dd>{formatDate(refund.createdAt)}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="font-medium">Last Updated:</dt>
                <dd>{formatDate(refund.updatedAt)}</dd>
              </div>
              <div className="pt-2">
                <dt className="font-medium">Notes:</dt>
                <dd className="mt-1 text-sm">{refund.notes || 'No notes provided'}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>
      </div>

      {/* Refunded Items */}
      <Card>
        <CardHeader>
          <CardTitle>Refunded Items</CardTitle>
          <CardDescription>
            Items included in this refund
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Product</TableHead>
                <TableHead>Code</TableHead>
                <TableHead className="text-right">Unit Price</TableHead>
                <TableHead className="text-right">Quantity</TableHead>
                <TableHead className="text-right">Total</TableHead>
                <TableHead className="text-center">Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {refund.items.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>{item.product.name}</TableCell>
                  <TableCell>{item.product.code || 'N/A'}</TableCell>
                  <TableCell className="text-right">{formatCurrency(item.unitPrice)}</TableCell>
                  <TableCell className="text-right">{item.quantity}</TableCell>
                  <TableCell className="text-right">{formatCurrency(item.total)}</TableCell>
                  <TableCell className="text-center">
                    <Badge
                      className={item.isDamaged ? "bg-red-500" : "bg-green-500"}
                    >
                      {item.isDamaged ? 'Damaged' : 'Returned to Stock'}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter className="flex justify-end">
          <div className="text-right">
            <div className="text-sm text-gray-500">Total Refund Amount</div>
            <div className="text-xl font-bold">{formatCurrency(refund.totalAmount)}</div>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
