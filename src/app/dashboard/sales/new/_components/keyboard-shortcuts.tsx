"use client";

import { useEffect, RefObject } from 'react';

interface KeyboardShortcutsProps {
  customerInputRef: RefObject<HTMLInputElement | null>;
  productInputRef: RefObject<HTMLInputElement | null>;
  onCreateMemo: () => void;
}

export function KeyboardShortcuts({
  customerInputRef,
  productInputRef,
  onCreateMemo
}: KeyboardShortcutsProps) {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Check if Ctrl and Shift are pressed
      if (event.ctrlKey && event.shiftKey) {
        // Prevent default browser behavior for these combinations
        if (event.key === '+' || event.key === '-' || event.key === '*') {
          event.preventDefault();
        }

        switch (event.key) {
          case '+': // Ctrl + Shift + +
            // Focus the Customer Input Box
            if (customerInputRef.current) {
              customerInputRef.current.focus();
              console.log('Customer input focused');
            }
            break;
          case '-': // Ctrl + Shift + -
            // Focus the Product Input Box
            if (productInputRef.current) {
              productInputRef.current.focus();
              console.log('Product input focused');
            }
            break;
          case '*': // Ctrl + Shift + *
            // Trigger the Memo Creation Function
            onCreateMemo();
            console.log('Create memo triggered');
            break;
        }
      }
    };

    // Add event listener
    document.addEventListener('keydown', handleKeyDown);

    // Clean up
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [customerInputRef, productInputRef, onCreateMemo]);

  // This component doesn't render anything visible
  return null;
}
