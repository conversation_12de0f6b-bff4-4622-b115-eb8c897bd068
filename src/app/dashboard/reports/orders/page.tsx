import { auth } from "~/auth";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import { branches } from "@/db/schema";
import { eq } from "drizzle-orm";
import ClientWrapper from "./_components/client-wrapper";

export default async function OrderReportsPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/api/auth/signin");
  }

  // Get all branches for the tenant
  let allBranches: { id: string; name: string; isMain?: boolean | null }[] = [];
  let userBranch: { id: string; name: string; isMain?: boolean | null } | null = null;

  try {
    // For tenant_sale users, use their associated tenant's ID
    // For tenant users, use their own ID
    const tenantId = session.user.role === 'tenant_sale'
      ? session.user.tenantId
      : session.user.id;

    // Make sure tenantId is not null or undefined
    if (tenantId) {
      // Get all branches for this tenant
      allBranches = await db.query.branches.findMany({
        where: eq(branches.tenantId, tenantId),
        orderBy: (branches, { asc }) => [asc(branches.name)],
      });
    }

    // For tenant_sale users, find their assigned branch
    if (session.user.role === 'tenant_sale' && session.user.branchId) {
      userBranch = allBranches.find(branch => branch.id === session.user.branchId) || null;
    }
  } catch (error) {
    console.error("Error fetching branches:", error);
    // Continue with empty branches array
  }

  return (
    <div className="container mx-auto py-10">
      <ClientWrapper
        branches={allBranches}
        userRole={session.user.role}
        userBranch={userBranch}
      />
    </div>
  );
}
