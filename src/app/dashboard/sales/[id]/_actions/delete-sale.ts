'use server';

import { db } from "@/lib/db";
import { orders, payments, inventoryTransactions, orderItems, customers } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { auth } from "../../../../../../auth";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { v4 as uuidv4 } from "uuid";

export async function deleteSale(id: string) {
  console.log('Server action: deleteSale called with ID:', id);

  try {
    const session = await auth();
    if (!session?.user?.id) {
      console.log('Unauthorized: No user ID in session');
      throw new Error("Unauthorized");
    }

    const tenantId = session.user.tenantId || session.user.id;
    console.log('Tenant ID:', tenantId);

    if (!tenantId) {
      console.log('Tenant not found');
      throw new Error("Tenant not found");
    }

    // Check if order exists and belongs to this tenant
    console.log('Checking if order exists for tenant:', tenantId);
    const order = await db.query.orders.findFirst({
      where: (orders, { eq, and }) =>
        and(eq(orders.id, id), eq(orders.tenantId, tenantId)),
      with: {
        items: {
          with: {
            product: true
          }
        }
      }
    });

    if (!order) {
      console.log('Order not found for this tenant');
      throw new Error("Order not found");
    }

    console.log('Order found, proceeding with deletion');

    // Use a transaction to ensure all related records are deleted and stock is restored
    await db.transaction(async (tx) => {
      console.log('Starting transaction for deletion');

      // 1. First, delete any payment records associated with this order
      console.log('Deleting payment records for order:', id);
      const paymentResult = await tx.delete(payments).where(eq(payments.orderId, id));
      console.log('Payment deletion result:', paymentResult);

      // 2. Find and delete inventory "out" transactions related to this sale
      console.log('Finding inventory transactions for order:', id);
      const saleTransactions = await tx.query.inventoryTransactions.findMany({
        where: (invTx, { eq, and, or, like }) =>
          and(
            eq(invTx.tenantId, tenantId),
            eq(invTx.type, "out"),
            or(
              like(invTx.notes, `%Sale - Memo #${order?.memoNo}%`),
              like(invTx.notes, `%Sale: ${order?.memoNo}%`)
            )
          )
      });

      console.log(`Found ${saleTransactions.length} inventory transactions for this sale`);

      // 3. Create "in" transactions to restore stock for each item in the order
      console.log('Restoring stock for order items');
      for (const item of order?.items || []) {
        console.log(`Creating "in" transaction to restore stock for product: ${item.productId}, quantity: ${item.quantity}`);

        // Create an "in" transaction to restore the stock
        await tx.insert(inventoryTransactions).values({
          id: uuidv4(),
          tenantId: tenantId,
          branchId: order?.branchId,
          productId: item.productId,
          quantity: item.quantity,
          warehouseQuantity: 0, // Only restore to store, not warehouse
          storeQuantity: item.quantity, // Restore to store
          unitPrice: item.unitPrice,
          type: "in",
          date: new Date(),
          notes: `Stock restored from deleted sale - Memo #${order?.memoNo}`,
          performedBy: session.user.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }

      // 4. Delete the original "out" inventory transactions
      for (const transaction of saleTransactions) {
        console.log(`Deleting inventory transaction: ${transaction.id}`);
        await tx.delete(inventoryTransactions).where(eq(inventoryTransactions.id, transaction.id));
      }

      // 5. If there's a customer with updated balance, restore their balance
      if (order?.customerId && typeof order?.dueAmount === 'number' && order.dueAmount > 0) {
        console.log(`Updating customer balance for customer: ${order.customerId}, reducing by ${order.dueAmount}`);
        const dueAmount = order.dueAmount; // Store in a variable to ensure it's not null
        const customerId = order.customerId;

        // Get the current customer to update their balance
        const customer = await tx.query.customers.findFirst({
          where: eq(customers.id, customerId)
        });

        if (customer) {
          // Calculate new balance by subtracting the due amount
          const newBalance = (customer.currentBalance || 0) - dueAmount;

          // Update the customer's balance
          await tx.update(customers)
            .set({
              currentBalance: newBalance,
              updatedAt: new Date()
            })
            .where(eq(customers.id, customerId));

          console.log(`Updated customer balance from ${customer.currentBalance} to ${newBalance}`);
        }
      }

      // 6. Finally, delete the order (cascade will delete order items)
      console.log('Deleting order:', id);
      const orderResult = await tx.delete(orders).where(eq(orders.id, id));
      console.log('Order deletion result:', orderResult);
    });

    console.log('Order deleted and stock restored successfully');

    // Revalidate the sales page
    revalidatePath('/dashboard/sales');

    // Return success
    return { success: true };
  } catch (error) {
    console.error("Error deleting order:", error);

    // Log detailed error information
    if (error instanceof Error) {
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
    } else {
      console.error("Unknown error type:", error);
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error during sale deletion"
    };
  }
}
