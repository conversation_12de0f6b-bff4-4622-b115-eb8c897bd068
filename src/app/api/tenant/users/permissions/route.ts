import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { userMenuPermissions } from '@/db/schema';
import { auth } from '~/auth';
import { and, eq } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';
import { ALL_MENUS } from '@/lib/menu-list';

// GET /api/tenant/users/permissions?userId=xxx - Get menu permissions for a specific user
export async function GET(request: Request) {
  try {
    // Check authentication and authorization
    const session = await auth();

    // Only tenant users can access this endpoint
    if (!session?.user || session.user.role !== 'tenant') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Only tenant admins can access user permissions.' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get user permissions for the tenant
    const userPermissions = await db.query.userMenuPermissions.findMany({
      where: and(
        eq(userMenuPermissions.userId, userId),
        eq(userMenuPermissions.tenantId, session.user.id)
      )
    });

    // Create a map of permissions by menuId
    const permissionsMap = new Map();
    userPermissions.forEach(permission => {
      permissionsMap.set(permission.menuId, {
        id: permission.id,
        userId: permission.userId,
        menuId: permission.menuId,
        tenantId: permission.tenantId,
        canView: permission.canView,
        canCreate: permission.canCreate,
        canEdit: permission.canEdit,
        canDelete: permission.canDelete
      });
    });

    // Combine file-based menus with database permissions
    const enhancedPermissions = ALL_MENUS.map(menu => {
      const permission = permissionsMap.get(menu.id);

      // If no permission found, this menu is not accessible to the user
      if (!permission) {
        return null;
      }

      return {
        ...permission,
        menu: {
          id: menu.id,
          name: menu.name,
          displayName: menu.displayName,
          description: menu.description,
          path: menu.path,
          icon: menu.icon,
          order: menu.order,
          isActive: menu.isActive,
          requiresPermission: menu.requiresPermission
        },
        order: menu.order
      };
    }).filter(Boolean); // Remove null entries

    // Sort permissions by menu order
    const sortedPermissions = enhancedPermissions.sort((a, b) => a.order - b.order);

    return NextResponse.json({
      success: true,
      permissions: sortedPermissions
    });
  } catch (error) {
    console.error('Error fetching user permissions:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch user permissions' },
      { status: 500 }
    );
  }
}

// POST /api/tenant/users/permissions - Update menu permissions for a user
export async function POST(request: Request) {
  try {
    // Check authentication and authorization
    const session = await auth();

    // Only tenant users can access this endpoint
    if (!session?.user || session.user.role !== 'tenant') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Only tenant admins can update user permissions.' },
        { status: 403 }
      );
    }

    // Parse request body
    const { userId, permissions } = await request.json();

    // Validate required fields
    if (!userId || !permissions || !Array.isArray(permissions)) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data' },
        { status: 400 }
      );
    }

    // Delete existing permissions for this user under this tenant
    await db.delete(userMenuPermissions)
      .where(
        and(
          eq(userMenuPermissions.userId, userId),
          eq(userMenuPermissions.tenantId, session.user.id)
        )
      );

    // Insert new permissions
    if (permissions.length > 0) {
      // First, ensure they have order information from ALL_MENUS
      const enhancedPermissions = permissions.map((permission: any) => {
        const menuFromFile = ALL_MENUS.find(m => m.id === permission.menuId);
        return {
          ...permission,
          order: menuFromFile?.order || 999
        };
      });

      // Sort by order
      const sortedPermissions = enhancedPermissions.sort((a, b) => (a.order || 999) - (b.order || 999));

      // Filter out dashboard permissions before inserting
      const filteredPermissions = sortedPermissions.filter(
        (permission: any) => permission.menuId !== 'dashboard'
      );

      const permissionsToInsert = filteredPermissions.map((permission: any) => ({
        id: permission.id || uuidv4(),
        userId,
        menuId: permission.menuId,
        tenantId: session.user.id,
        canView: permission.canView !== undefined ? permission.canView : true,
        canCreate: permission.canCreate !== undefined ? permission.canCreate : true,
        canEdit: permission.canEdit !== undefined ? permission.canEdit : true,
        canDelete: permission.canDelete !== undefined ? permission.canDelete : true,
        createdAt: new Date(),
        updatedAt: new Date()
      }));

      await db.insert(userMenuPermissions).values(permissionsToInsert);
    } else {
      // If no permissions provided, use ALL_MENUS from file and grant full permissions
      if (ALL_MENUS.length > 0) {
        // Sort menus by order
        const sortedMenus = [...ALL_MENUS].sort((a, b) => (a.order || 999) - (b.order || 999));

        // Filter out dashboard menu before creating permissions
        const filteredMenus = sortedMenus.filter(menu => menu.id !== 'dashboard');

        const allPermissions = filteredMenus.map(menu => ({
          id: uuidv4(),
          userId,
          menuId: menu.id,
          tenantId: session.user.id,
          canView: true,
          canCreate: true,
          canEdit: true,
          canDelete: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }));

        await db.insert(userMenuPermissions).values(allPermissions);
      }
    }

    return NextResponse.json({
      success: true,
      message: 'User permissions updated successfully'
    });
  } catch (error) {
    console.error('Error updating user permissions:', error);

    // Provide a more detailed error message
    let errorMessage = 'Failed to update user permissions';

    // Check if it's a foreign key constraint error
    if (error instanceof Error && error.message.includes('FOREIGN KEY constraint failed')) {
      errorMessage = 'Foreign key constraint error. Please check that the user and tenant IDs are valid.';
    }

    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}
