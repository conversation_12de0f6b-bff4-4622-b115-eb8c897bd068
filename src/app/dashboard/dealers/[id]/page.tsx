'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Edit, Plus, CreditCard, ShoppingCart } from 'lucide-react';
import { toast } from 'sonner';
import { usePermissionCheck } from '@/lib/check-permissions';
import Link from 'next/link';
import { use } from 'react';

interface Dealer {
  id: string;
  name: string;
  dealerCode?: string;
  email?: string;
  phone?: string;
  address?: string;
  contactPerson?: string;
  currentBalance: number;
  creditLimit: number;
  paymentTerms?: string;
  isActive: boolean;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface Purchase {
  id: string;
  purchaseNo: string;
  date: string;
  totalAmount: number;
  paidAmount: number;
  dueAmount: number;
  paymentStatus: 'paid' | 'partial' | 'unpaid';
  status: 'pending' | 'completed' | 'cancelled';
  items: Array<{
    id: string;
    quantity: number;
    unitPrice: number;
    total: number;
    product: {
      id: string;
      name: string;
      code?: string;
    };
  }>;
  performer?: {
    id: string;
    fullName: string;
  };
}

interface Payment {
  id: string;
  amount: number;
  paymentMethod: string;
  paymentType: string;
  date: string;
  notes?: string;
  purchase?: {
    id: string;
    purchaseNo: string;
  };
  performer?: {
    id: string;
    fullName: string;
  };
}

export default function DealerDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const resolvedParams = use(params);
  const router = useRouter();
  const { data: session } = useSession();
  const [dealer, setDealer] = useState<Dealer | null>(null);
  const [purchases, setPurchases] = useState<Purchase[]>([]);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check permissions
  usePermissionCheck('/dashboard/dealers', 'view');

  useEffect(() => {
    if (resolvedParams.id) {
      fetchDealerData();
    }
  }, [resolvedParams.id]);

  const fetchDealerData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch dealer details
      const dealerResponse = await fetch(`/api/dealers?id=${resolvedParams.id}`);
      if (!dealerResponse.ok) {
        throw new Error('Failed to fetch dealer details');
      }
      const dealerData = await dealerResponse.json();
      
      if (!dealerData.success) {
        throw new Error(dealerData.error || 'Failed to fetch dealer');
      }
      
      setDealer(dealerData.dealer);

      // Fetch purchases
      const purchasesResponse = await fetch(`/api/dealers/purchases?dealerId=${resolvedParams.id}`);
      if (purchasesResponse.ok) {
        const purchasesData = await purchasesResponse.json();
        if (purchasesData.success) {
          setPurchases(purchasesData.purchases || []);
        }
      }

      // Fetch payments
      const paymentsResponse = await fetch(`/api/dealers/payments?dealerId=${resolvedParams.id}`);
      if (paymentsResponse.ok) {
        const paymentsData = await paymentsResponse.json();
        if (paymentsData.success) {
          setPayments(paymentsData.payments || []);
        }
      }
    } catch (err) {
      console.error('Error fetching dealer data:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      toast.error('Failed to load dealer data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BDT',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getBalanceColor = (balance: number) => {
    if (balance > 0) return 'text-red-600'; // We owe them money
    if (balance < 0) return 'text-green-600'; // They owe us money
    return 'text-gray-600'; // Balanced
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'partial': return 'bg-yellow-100 text-yellow-800';
      case 'unpaid': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading dealer details...</div>
        </div>
      </div>
    );
  }

  if (error || !dealer) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg text-red-600">{error || 'Dealer not found'}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/dashboard/dealers">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dealers
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">{dealer.name}</h1>
        <div className="flex gap-2 ml-auto">
          <Link href={`/dashboard/dealers/edit/${dealer.id}`}>
            <Button variant="outline" size="sm">
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </Link>
          <Link href={`/dashboard/dealers/purchases/new?dealerId=${dealer.id}`}>
            <Button size="sm">
              <ShoppingCart className="h-4 w-4 mr-2" />
              New Purchase
            </Button>
          </Link>
          <Link href={`/dashboard/dealers/payments/new?dealerId=${dealer.id}`}>
            <Button variant="outline" size="sm">
              <CreditCard className="h-4 w-4 mr-2" />
              Make Payment
            </Button>
          </Link>
        </div>
      </div>

      {/* Dealer Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">
              <span className={getBalanceColor(dealer.currentBalance)}>
                {formatCurrency(Math.abs(dealer.currentBalance))}
              </span>
            </div>
            <p className="text-xs text-muted-foreground">
              Current Balance
              {dealer.currentBalance > 0 && ' (We owe)'}
              {dealer.currentBalance < 0 && ' (They owe)'}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{formatCurrency(dealer.creditLimit)}</div>
            <p className="text-xs text-muted-foreground">Credit Limit</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{purchases.length}</div>
            <p className="text-xs text-muted-foreground">Total Purchases</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{payments.length}</div>
            <p className="text-xs text-muted-foreground">Total Payments</p>
          </CardContent>
        </Card>
      </div>

      {/* Dealer Details */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Dealer Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p><strong>Name:</strong> {dealer.name}</p>
              {dealer.dealerCode && <p><strong>Code:</strong> {dealer.dealerCode}</p>}
              {dealer.contactPerson && <p><strong>Contact Person:</strong> {dealer.contactPerson}</p>}
              {dealer.paymentTerms && <p><strong>Payment Terms:</strong> {dealer.paymentTerms}</p>}
            </div>
            <div>
              {dealer.email && <p><strong>Email:</strong> {dealer.email}</p>}
              {dealer.phone && <p><strong>Phone:</strong> {dealer.phone}</p>}
              {dealer.address && <p><strong>Address:</strong> {dealer.address}</p>}
              <p><strong>Status:</strong> 
                <Badge variant={dealer.isActive ? 'default' : 'secondary'} className="ml-2">
                  {dealer.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </p>
            </div>
          </div>
          {dealer.notes && (
            <div className="mt-4">
              <p><strong>Notes:</strong> {dealer.notes}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tabs for Purchases and Payments */}
      <Tabs defaultValue="purchases" className="w-full">
        <TabsList>
          <TabsTrigger value="purchases">Purchases ({purchases.length})</TabsTrigger>
          <TabsTrigger value="payments">Payments ({payments.length})</TabsTrigger>
        </TabsList>
        
        <TabsContent value="purchases">
          <Card>
            <CardHeader>
              <CardTitle>Purchase History</CardTitle>
            </CardHeader>
            <CardContent>
              {purchases.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">No purchases found.</p>
                  <Link href={`/dashboard/dealers/purchases/new?dealerId=${dealer.id}`}>
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Record First Purchase
                    </Button>
                  </Link>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Purchase No</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Total Amount</TableHead>
                      <TableHead>Paid Amount</TableHead>
                      <TableHead>Due Amount</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {purchases.map((purchase) => (
                      <TableRow key={purchase.id}>
                        <TableCell className="font-medium">{purchase.purchaseNo}</TableCell>
                        <TableCell>{formatDate(purchase.date)}</TableCell>
                        <TableCell>{formatCurrency(purchase.totalAmount)}</TableCell>
                        <TableCell>{formatCurrency(purchase.paidAmount)}</TableCell>
                        <TableCell>{formatCurrency(purchase.dueAmount)}</TableCell>
                        <TableCell>
                          <Badge className={getPaymentStatusColor(purchase.paymentStatus)}>
                            {purchase.paymentStatus}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="payments">
          <Card>
            <CardHeader>
              <CardTitle>Payment History</CardTitle>
            </CardHeader>
            <CardContent>
              {payments.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">No payments found.</p>
                  <Link href={`/dashboard/dealers/payments/new?dealerId=${dealer.id}`}>
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Make First Payment
                    </Button>
                  </Link>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Method</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Purchase</TableHead>
                      <TableHead>Notes</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {payments.map((payment) => (
                      <TableRow key={payment.id}>
                        <TableCell>{formatDate(payment.date)}</TableCell>
                        <TableCell className="font-medium">{formatCurrency(payment.amount)}</TableCell>
                        <TableCell className="capitalize">{payment.paymentMethod}</TableCell>
                        <TableCell className="capitalize">{payment.paymentType.replace('_', ' ')}</TableCell>
                        <TableCell>
                          {payment.purchase ? payment.purchase.purchaseNo : 'N/A'}
                        </TableCell>
                        <TableCell>{payment.notes || 'N/A'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
