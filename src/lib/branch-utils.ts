import { db } from './db';
import { branches } from '@/db/schema';
import { v4 as uuidv4 } from 'uuid';

/**
 * Creates a main branch for a tenant
 * @param tenantId The ID of the tenant (which is the user ID of the tenant user)
 * @param companyName The company name to use for the branch name
 * @returns The created branch object or null if creation failed
 */
export async function createMainBranchForTenant(tenantId: string, companyName: string) {
  try {
    console.log(`Creating main branch for tenant ${tenantId} with company name ${companyName}`);
    
    // Generate a branch code based on company name
    const branchCode = generateBranchCode(companyName);
    
    // Create the branch
    const [branch] = await db
      .insert(branches)
      .values({
        id: uuidv4(),
        tenantId: tenantId,
        name: `${companyName} Main Branch`,
        code: branchCode,
        isMain: true,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();
    
    console.log(`Main branch created successfully with ID: ${branch.id}`);
    return branch;
  } catch (error) {
    console.error('Error creating main branch for tenant:', error);
    return null;
  }
}

/**
 * Generates a branch code based on the company name
 * @param companyName The company name
 * @returns A branch code in the format BR-XXX where XXX is derived from the company name
 */
function generateBranchCode(companyName: string): string {
  // Extract first 3 characters from company name, convert to uppercase
  const prefix = companyName
    .replace(/[^a-zA-Z0-9]/g, '') // Remove non-alphanumeric characters
    .substring(0, 3)
    .toUpperCase();
  
  // Add a random 3-digit number
  const randomNum = Math.floor(Math.random() * 900) + 100; // Random number between 100-999
  
  return `BR-${prefix}${randomNum}`;
}
