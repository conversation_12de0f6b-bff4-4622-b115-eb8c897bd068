'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { CalendarIcon, DownloadIcon, TrendingUpIcon, TrendingDownIcon, DollarSignIcon } from 'lucide-react';
import { toast } from 'sonner';
import { usePermissionCheck } from '@/lib/check-permissions';

interface DealerSummary {
  id: string;
  name: string;
  dealerCode?: string;
  currentBalance: number;
  totalPurchases: number;
  totalPayments: number;
  lastPurchaseDate?: string;
  lastPaymentDate?: string;
  isActive: boolean;
}

interface ReportSummary {
  totalDealers: number;
  activeDealers: number;
  totalOutstanding: number;
  totalPurchasesAmount: number;
  totalPaymentsAmount: number;
}

export default function DealerReportsPage() {
  const { data: session } = useSession();
  const [dealers, setDealers] = useState<DealerSummary[]>([]);
  const [summary, setSummary] = useState<ReportSummary>({
    totalDealers: 0,
    activeDealers: 0,
    totalOutstanding: 0,
    totalPurchasesAmount: 0,
    totalPaymentsAmount: 0,
  });
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  });

  // Check permissions
  usePermissionCheck('/dashboard/dealers', 'view');

  useEffect(() => {
    fetchReportData();
  }, [dateRange]);

  const fetchReportData = async () => {
    try {
      setLoading(true);

      // Fetch dealers
      const dealersResponse = await fetch('/api/dealers');
      if (dealersResponse.ok) {
        const dealersData = await dealersResponse.json();
        if (dealersData.success) {
          const dealersList = dealersData.dealers || [];
          
          // Calculate summary statistics
          const totalDealers = dealersList.length;
          const activeDealers = dealersList.filter((d: any) => d.isActive).length;
          const totalOutstanding = dealersList.reduce((sum: number, d: any) => sum + Math.max(0, d.currentBalance), 0);

          // For now, we'll use mock data for purchases and payments totals
          // In a real implementation, you'd fetch this from the API with date filtering
          const totalPurchasesAmount = dealersList.length * 50000; // Mock data
          const totalPaymentsAmount = dealersList.length * 30000; // Mock data

          setSummary({
            totalDealers,
            activeDealers,
            totalOutstanding,
            totalPurchasesAmount,
            totalPaymentsAmount,
          });

          // Transform dealers data for the table
          const dealersWithSummary = dealersList.map((dealer: any) => ({
            ...dealer,
            totalPurchases: Math.floor(Math.random() * 10) + 1, // Mock data
            totalPayments: Math.floor(Math.random() * 8) + 1, // Mock data
            lastPurchaseDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
            lastPaymentDate: new Date(Date.now() - Math.random() * 15 * 24 * 60 * 60 * 1000).toISOString(),
          }));

          setDealers(dealersWithSummary);
        }
      }
    } catch (error) {
      console.error('Error fetching report data:', error);
      toast.error('Failed to load report data');
    } finally {
      setLoading(false);
    }
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setDateRange(prev => ({ ...prev, [name]: value }));
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BDT',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getBalanceColor = (balance: number) => {
    if (balance > 0) return 'text-red-600'; // We owe them money
    if (balance < 0) return 'text-green-600'; // They owe us money
    return 'text-gray-600'; // Balanced
  };

  const exportReport = () => {
    // Simple CSV export
    const headers = ['Dealer Name', 'Code', 'Current Balance', 'Total Purchases', 'Total Payments', 'Status'];
    const csvContent = [
      headers.join(','),
      ...dealers.map(dealer => [
        dealer.name,
        dealer.dealerCode || '',
        dealer.currentBalance,
        dealer.totalPurchases,
        dealer.totalPayments,
        dealer.isActive ? 'Active' : 'Inactive'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `dealer-report-${dateRange.startDate}-to-${dateRange.endDate}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading report data...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Dealer Reports & Analytics</h1>
        <Button onClick={exportReport} variant="outline">
          <DownloadIcon className="h-4 w-4 mr-2" />
          Export CSV
        </Button>
      </div>

      {/* Date Range Filter */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex gap-4 items-center">
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date</Label>
              <Input
                id="startDate"
                name="startDate"
                type="date"
                value={dateRange.startDate}
                onChange={handleDateChange}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="endDate">End Date</Label>
              <Input
                id="endDate"
                name="endDate"
                type="date"
                value={dateRange.endDate}
                onChange={handleDateChange}
              />
            </div>
            <Button onClick={fetchReportData} className="mt-6">
              <CalendarIcon className="h-4 w-4 mr-2" />
              Update Report
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <TrendingUpIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Dealers</p>
                <p className="text-2xl font-bold">{summary.totalDealers}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <TrendingUpIcon className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Dealers</p>
                <p className="text-2xl font-bold">{summary.activeDealers}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <DollarSignIcon className="h-6 w-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Outstanding</p>
                <p className="text-2xl font-bold text-red-600">{formatCurrency(summary.totalOutstanding)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUpIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Purchases</p>
                <p className="text-2xl font-bold">{formatCurrency(summary.totalPurchasesAmount)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <TrendingDownIcon className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Payments</p>
                <p className="text-2xl font-bold">{formatCurrency(summary.totalPaymentsAmount)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Dealers Table */}
      <Card>
        <CardHeader>
          <CardTitle>Dealer Summary</CardTitle>
        </CardHeader>
        <CardContent>
          {dealers.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No dealers found.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Dealer</TableHead>
                    <TableHead>Current Balance</TableHead>
                    <TableHead>Purchases</TableHead>
                    <TableHead>Payments</TableHead>
                    <TableHead>Last Purchase</TableHead>
                    <TableHead>Last Payment</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {dealers.map((dealer) => (
                    <TableRow key={dealer.id}>
                      <TableCell>
                        <div className="font-medium">{dealer.name}</div>
                        {dealer.dealerCode && (
                          <div className="text-sm text-gray-500">Code: {dealer.dealerCode}</div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className={`font-medium ${getBalanceColor(dealer.currentBalance)}`}>
                          {formatCurrency(Math.abs(dealer.currentBalance))}
                        </div>
                        <div className="text-xs text-gray-500">
                          {dealer.currentBalance > 0 ? 'We owe' : dealer.currentBalance < 0 ? 'They owe' : 'Balanced'}
                        </div>
                      </TableCell>
                      <TableCell>{dealer.totalPurchases}</TableCell>
                      <TableCell>{dealer.totalPayments}</TableCell>
                      <TableCell>
                        {dealer.lastPurchaseDate ? formatDate(dealer.lastPurchaseDate) : 'N/A'}
                      </TableCell>
                      <TableCell>
                        {dealer.lastPaymentDate ? formatDate(dealer.lastPaymentDate) : 'N/A'}
                      </TableCell>
                      <TableCell>
                        <Badge variant={dealer.isActive ? 'default' : 'secondary'}>
                          {dealer.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
