import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { expenses, expenseCategories, branches } from "@/db/schema";
import { auth } from "~/auth";
import { v4 as uuidv4 } from "uuid";
import { eq, and, desc } from "drizzle-orm";
import { checkMenuPermission } from "@/middleware/menuPermissions";

// GET /api/expenses - Get all expenses for the current tenant
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get tenant ID from session
    const tenantId = session.user.role === 'tenant'
      ? session.user.id
      : session.user.tenantId;

    if (!tenantId) {
      return NextResponse.json(
        { success: false, error: "Tenant ID not found" },
        { status: 400 }
      );
    }

    // Check if specific expense ID is requested
    const url = new URL(request.url);
    const expenseId = url.searchParams.get('id');

    if (expenseId) {
      // Get specific expense with related data
      const expense = await db.query.expenses.findFirst({
        where: and(
          eq(expenses.id, expenseId),
          eq(expenses.tenantId, tenantId)
        ),
        with: {
          category: true,
          branch: true,
          performer: true
        }
      });

      if (!expense) {
        return NextResponse.json(
          { success: false, error: "Expense not found" },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        expense
      });
    }

    // Get all expenses for the tenant with related data
    const allExpenses = await db.query.expenses.findMany({
      where: eq(expenses.tenantId, tenantId),
      with: {
        category: true,
        branch: true,
        performer: true
      },
      orderBy: [desc(expenses.date)]
    });

    return NextResponse.json({
      success: true,
      expenses: allExpenses
    });
  } catch (error) {
    console.error("Error fetching expenses:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch expenses" },
      { status: 500 }
    );
  }
}

// POST /api/expenses - Create a new expense
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Admin and tenant users have full permissions
    if (session.user.role !== 'admin' && session.user.role !== 'tenant') {
      // For tenant_sale users, check permissions
      const permissionCheck = await checkMenuPermission(request, '/dashboard/expenses');
      if (!permissionCheck.success) {
        return NextResponse.json(
          { success: false, error: permissionCheck.error },
          { status: permissionCheck.status }
        );
      }
    }

    // Get tenant ID from session
    const tenantId = session.user.role === 'tenant'
      ? session.user.id
      : session.user.tenantId;

    if (!tenantId) {
      return NextResponse.json(
        { success: false, error: "Tenant ID not found" },
        { status: 400 }
      );
    }

    // Parse request body
    const body = await request.json();
    const {
      categoryId,
      branchId,
      amount,
      date,
      description,
      paymentMethod,
      reference
    } = body;

    // Validate required fields
    if (!categoryId || !branchId || !amount || amount <= 0) {
      return NextResponse.json(
        { success: false, error: "Category, branch, and a positive amount are required" },
        { status: 400 }
      );
    }

    // Verify category belongs to tenant
    const category = await db.query.expenseCategories.findFirst({
      where: and(
        eq(expenseCategories.id, categoryId),
        eq(expenseCategories.tenantId, tenantId)
      )
    });

    if (!category) {
      return NextResponse.json(
        { success: false, error: "Invalid expense category" },
        { status: 400 }
      );
    }

    // Verify branch belongs to tenant
    const branch = await db.query.branches.findFirst({
      where: and(
        eq(branches.id, branchId),
        eq(branches.tenantId, tenantId)
      )
    });

    if (!branch) {
      return NextResponse.json(
        { success: false, error: "Invalid branch" },
        { status: 400 }
      );
    }

    // Create new expense
    const newExpense = {
      id: uuidv4(),
      tenantId,
      categoryId,
      branchId,
      amount,
      date: date ? new Date(date) : new Date(),
      description: description || null,
      paymentMethod: paymentMethod || "cash",
      reference: reference || null,
      performedBy: session.user.id,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await db.insert(expenses).values(newExpense);

    return NextResponse.json({
      success: true,
      message: "Expense recorded successfully",
      expense: newExpense
    });
  } catch (error) {
    console.error("Error creating expense:", error);
    return NextResponse.json(
      { success: false, error: "Failed to record expense" },
      { status: 500 }
    );
  }
}

// PUT /api/expenses - Update an existing expense
export async function PUT(request: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Admin and tenant users have full permissions
    if (session.user.role !== 'admin' && session.user.role !== 'tenant') {
      // For tenant_sale users, check permissions
      const permissionCheck = await checkMenuPermission(request, '/dashboard/expenses');
      if (!permissionCheck.success) {
        return NextResponse.json(
          { success: false, error: permissionCheck.error },
          { status: permissionCheck.status }
        );
      }
    }

    // Get tenant ID from session
    const tenantId = session.user.role === 'tenant'
      ? session.user.id
      : session.user.tenantId;

    if (!tenantId) {
      return NextResponse.json(
        { success: false, error: "Tenant ID not found" },
        { status: 400 }
      );
    }

    // Parse request body
    const body = await request.json();
    const {
      id,
      categoryId,
      branchId,
      amount,
      date,
      description,
      paymentMethod,
      reference
    } = body;

    // Validate required fields
    if (!id || !categoryId || !branchId || !amount || amount <= 0) {
      return NextResponse.json(
        { success: false, error: "Expense ID, category, branch, and a positive amount are required" },
        { status: 400 }
      );
    }

    // Check if expense exists and belongs to the tenant
    const existingExpense = await db.query.expenses.findFirst({
      where: and(
        eq(expenses.id, id),
        eq(expenses.tenantId, tenantId)
      )
    });

    if (!existingExpense) {
      return NextResponse.json(
        { success: false, error: "Expense not found or you don't have permission to update it" },
        { status: 404 }
      );
    }

    // Verify category belongs to tenant
    const category = await db.query.expenseCategories.findFirst({
      where: and(
        eq(expenseCategories.id, categoryId),
        eq(expenseCategories.tenantId, tenantId)
      )
    });

    if (!category) {
      return NextResponse.json(
        { success: false, error: "Invalid expense category" },
        { status: 400 }
      );
    }

    // Verify branch belongs to tenant
    const branch = await db.query.branches.findFirst({
      where: and(
        eq(branches.id, branchId),
        eq(branches.tenantId, tenantId)
      )
    });

    if (!branch) {
      return NextResponse.json(
        { success: false, error: "Invalid branch" },
        { status: 400 }
      );
    }

    // Update expense
    await db.update(expenses)
      .set({
        categoryId,
        branchId,
        amount,
        date: date ? new Date(date) : existingExpense.date,
        description: description || null,
        paymentMethod: paymentMethod || existingExpense.paymentMethod,
        reference: reference || null,
        updatedAt: new Date()
      })
      .where(eq(expenses.id, id));

    return NextResponse.json({
      success: true,
      message: "Expense updated successfully"
    });
  } catch (error) {
    console.error("Error updating expense:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update expense" },
      { status: 500 }
    );
  }
}

// DELETE /api/expenses - Delete an expense
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Admin and tenant users have full permissions
    if (session.user.role !== 'admin' && session.user.role !== 'tenant') {
      // For tenant_sale users, check permissions
      const permissionCheck = await checkMenuPermission(request, '/dashboard/expenses');
      if (!permissionCheck.success) {
        return NextResponse.json(
          { success: false, error: permissionCheck.error },
          { status: permissionCheck.status }
        );
      }
    }

    // Get tenant ID from session
    const tenantId = session.user.role === 'tenant'
      ? session.user.id
      : session.user.tenantId;

    if (!tenantId) {
      return NextResponse.json(
        { success: false, error: "Tenant ID not found" },
        { status: 400 }
      );
    }

    // Get expense ID from URL
    const url = new URL(request.url);
    const expenseId = url.searchParams.get('id');

    if (!expenseId) {
      return NextResponse.json(
        { success: false, error: "Expense ID is required" },
        { status: 400 }
      );
    }

    // Check if expense exists and belongs to the tenant
    const existingExpense = await db.query.expenses.findFirst({
      where: and(
        eq(expenses.id, expenseId),
        eq(expenses.tenantId, tenantId)
      )
    });

    if (!existingExpense) {
      return NextResponse.json(
        { success: false, error: "Expense not found or you don't have permission to delete it" },
        { status: 404 }
      );
    }

    // Delete expense
    await db.delete(expenses)
      .where(eq(expenses.id, expenseId));

    return NextResponse.json({
      success: true,
      message: "Expense deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting expense:", error);
    return NextResponse.json(
      { success: false, error: "Failed to delete expense" },
      { status: 500 }
    );
  }
}
