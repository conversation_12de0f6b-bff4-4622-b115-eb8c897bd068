'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Save, CreditCard } from 'lucide-react';
import { toast } from 'sonner';
import { usePer<PERSON><PERSON>heck } from '@/lib/check-permissions';
import Link from 'next/link';
import { usePaymentMethods, getDefaultPaymentMethod } from '@/hooks/usePaymentMethods';

interface Dealer {
  id: string;
  name: string;
  dealerCode?: string;
  currentBalance: number;
}

interface Branch {
  id: string;
  name: string;
  code?: string;
}

interface Purchase {
  id: string;
  purchaseNo: string;
  totalAmount: number;
  paidAmount: number;
  dueAmount: number;
  date: string;
}

export default function NewPaymentPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [dealers, setDealers] = useState<Dealer[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [purchases, setPurchases] = useState<Purchase[]>([]);
  const [selectedDealer, setSelectedDealer] = useState<Dealer | null>(null);

  // State for dealer search
  const [dealerSearchOpen, setDealerSearchOpen] = useState(false);
  const [dealerSearchValue, setDealerSearchValue] = useState('');

  // Check permissions
  usePermissionCheck('/dashboard/dealers', 'create');

  // Fetch payment methods
  const { paymentMethods, isLoading: isLoadingPaymentMethods } = usePaymentMethods();

  // Form state
  const [formData, setFormData] = useState({
    dealerId: searchParams?.get('dealerId') || '',
    branchId: '',
    purchaseId: '',
    amount: 0,
    paymentMethod: '',
    paymentReference: '',
    paymentType: 'purchase_payment' as 'purchase_payment' | 'advance_payment' | 'adjustment',
    date: new Date().toISOString().split('T')[0],
    notes: '',
  });

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (formData.dealerId) {
      fetchDealerPurchases();
      const dealer = dealers.find(d => d.id === formData.dealerId);
      setSelectedDealer(dealer || null);
    }
  }, [formData.dealerId, dealers]);

  // Set default payment method when payment methods are loaded
  useEffect(() => {
    if (paymentMethods.length > 0 && !formData.paymentMethod) {
      const defaultMethod = getDefaultPaymentMethod(paymentMethods);
      if (defaultMethod) {
        setFormData(prev => ({ ...prev, paymentMethod: defaultMethod.code }));
      }
    }
  }, [paymentMethods, formData.paymentMethod]);

  const fetchData = async () => {
    try {
      // Fetch dealers
      const dealersResponse = await fetch('/api/dealers');
      if (dealersResponse.ok) {
        const dealersData = await dealersResponse.json();
        if (dealersData.success) {
          setDealers(dealersData.dealers || []);
        }
      }

      // Fetch branches
      const branchesResponse = await fetch('/api/branches');
      if (branchesResponse.ok) {
        const branchesData = await branchesResponse.json();
        if (branchesData.success) {
          setBranches(branchesData.branches || []);
          // Set default branch if user has a specific branch
          if (session?.user?.branchId) {
            setFormData(prev => ({ ...prev, branchId: session.user.branchId }));
          }
        }
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load data');
    }
  };

  const fetchDealerPurchases = async () => {
    if (!formData.dealerId) return;

    try {
      const response = await fetch(`/api/dealers/purchases?dealerId=${formData.dealerId}`);
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Filter purchases that have due amounts
          const unpaidPurchases = data.purchases.filter((p: Purchase) => p.dueAmount > 0);
          setPurchases(unpaidPurchases || []);
        }
      }
    } catch (error) {
      console.error('Error fetching purchases:', error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value,
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleDealerSelect = (dealerId: string) => {
    const dealer = dealers.find(d => d.id === dealerId);
    if (dealer) {
      setFormData(prev => ({ ...prev, dealerId }));
      setDealerSearchValue(dealer.name + (dealer.dealerCode ? ` (${dealer.dealerCode})` : ''));
      setDealerSearchOpen(false);
    }
  };

  const getSelectedDealerName = () => {
    if (!formData.dealerId) return '';
    const dealer = dealers.find(d => d.id === formData.dealerId);
    return dealer ? dealer.name + (dealer.dealerCode ? ` (${dealer.dealerCode})` : '') : '';
  };

  const handlePayFullBalance = () => {
    if (selectedDealer && selectedDealer.currentBalance > 0) {
      setFormData(prev => ({
        ...prev,
        amount: selectedDealer.currentBalance,
        paymentType: 'purchase_payment',
        purchaseId: '', // Clear specific purchase selection
      }));
    }
  };

  const handlePayPurchaseDue = (purchase: Purchase) => {
    setFormData(prev => ({
      ...prev,
      amount: purchase.dueAmount,
      paymentType: 'purchase_payment',
      purchaseId: purchase.id,
    }));
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BDT',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.dealerId || !formData.branchId || !formData.amount || formData.amount <= 0) {
      toast.error('Please fill all required fields with valid values');
      return;
    }

    if (selectedDealer && formData.amount > selectedDealer.currentBalance) {
      toast.error('Payment amount cannot exceed the current balance');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/dealers/payments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dealerId: formData.dealerId,
          branchId: formData.branchId,
          purchaseId: formData.purchaseId || null,
          amount: formData.amount,
          paymentMethod: formData.paymentMethod,
          paymentReference: formData.paymentReference || null,
          paymentType: formData.paymentType,
          date: formData.date,
          notes: formData.notes || null,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Payment created successfully');
        router.push(`/dashboard/dealers/${formData.dealerId}`);
      } else {
        toast.error(data.error || 'Failed to create payment');
      }
    } catch (error) {
      console.error('Error creating payment:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/dashboard/dealers">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dealers
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Make Payment to Dealer</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Payment Form */}
        <div className="lg:col-span-2">
          <form onSubmit={handleSubmit}>
            <Card>
              <CardHeader>
                <CardTitle>Payment Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Dealer and Branch Selection */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="dealerId">Dealer *</Label>
                    <Popover open={dealerSearchOpen} onOpenChange={setDealerSearchOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={dealerSearchOpen}
                          className="w-full justify-between"
                        >
                          {formData.dealerId
                            ? getSelectedDealerName()
                            : "Select dealer..."}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <Command>
                          <CommandInput
                            placeholder="Search dealers..."
                            value={dealerSearchValue}
                            onValueChange={setDealerSearchValue}
                          />
                          <CommandList>
                            <CommandEmpty>No dealer found.</CommandEmpty>
                            <CommandGroup>
                              {dealers
                                .filter((dealer) =>
                                  dealer.name.toLowerCase().includes(dealerSearchValue.toLowerCase()) ||
                                  (dealer.dealerCode && dealer.dealerCode.toLowerCase().includes(dealerSearchValue.toLowerCase()))
                                )
                                .map((dealer) => (
                                  <CommandItem
                                    key={dealer.id}
                                    value={dealer.id}
                                    onSelect={() => handleDealerSelect(dealer.id)}
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        formData.dealerId === dealer.id ? "opacity-100" : "opacity-0"
                                      )}
                                    />
                                    <div className="flex flex-col">
                                      <span>{dealer.name} {dealer.dealerCode && `(${dealer.dealerCode})`}</span>
                                      {dealer.currentBalance > 0 && (
                                        <span className="text-sm text-red-600">
                                          Balance: {formatCurrency(dealer.currentBalance)}
                                        </span>
                                      )}
                                    </div>
                                  </CommandItem>
                                ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="branchId">Branch *</Label>
                    <Select value={formData.branchId} onValueChange={(value) => handleSelectChange('branchId', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select branch" />
                      </SelectTrigger>
                      <SelectContent>
                        {branches.map((branch) => (
                          <SelectItem key={branch.id} value={branch.id}>
                            {branch.name} {branch.code && `(${branch.code})`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Payment Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="amount">Payment Amount (BDT) *</Label>
                    <Input
                      id="amount"
                      name="amount"
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.amount}
                      onChange={handleInputChange}
                      placeholder="0.00"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="paymentMethod">Payment Method</Label>
                    <Select value={formData.paymentMethod} onValueChange={(value) => handleSelectChange('paymentMethod', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder={isLoadingPaymentMethods ? "Loading..." : "Select payment method"} />
                      </SelectTrigger>
                      <SelectContent>
                        {isLoadingPaymentMethods ? (
                          <SelectItem value="" disabled>Loading payment methods...</SelectItem>
                        ) : paymentMethods.length > 0 ? (
                          paymentMethods.map((method) => (
                            <SelectItem key={method.id} value={method.code}>
                              {method.name}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="" disabled>No payment methods available</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="paymentReference">Payment Reference</Label>
                    <Input
                      id="paymentReference"
                      name="paymentReference"
                      value={formData.paymentReference}
                      onChange={handleInputChange}
                      placeholder="Cheque number, transaction ID, etc."
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="date">Payment Date *</Label>
                    <Input
                      id="date"
                      name="date"
                      type="date"
                      value={formData.date}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>

                {/* Payment Type */}
                <div className="space-y-2">
                  <Label htmlFor="paymentType">Payment Type</Label>
                  <Select value={formData.paymentType} onValueChange={(value) => handleSelectChange('paymentType', value as any)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="purchase_payment">Purchase Payment</SelectItem>
                      <SelectItem value="advance_payment">Advance Payment</SelectItem>
                      <SelectItem value="adjustment">Adjustment</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Specific Purchase Selection */}
                {formData.paymentType === 'purchase_payment' && purchases.length > 0 && (
                  <div className="space-y-2">
                    <Label htmlFor="purchaseId">Specific Purchase (Optional)</Label>
                    <Select value={formData.purchaseId} onValueChange={(value) => handleSelectChange('purchaseId', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select specific purchase or leave blank for general payment" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">General Payment</SelectItem>
                        {purchases.map((purchase) => (
                          <SelectItem key={purchase.id} value={purchase.id}>
                            {purchase.purchaseNo} - Due: {formatCurrency(purchase.dueAmount)} ({formatDate(purchase.date)})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Notes */}
                <div className="space-y-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    placeholder="Enter any additional notes"
                    rows={3}
                  />
                </div>

                {/* Submit Button */}
                <div className="flex gap-4 pt-4">
                  <Button type="submit" disabled={loading}>
                    <Save className="h-4 w-4 mr-2" />
                    {loading ? 'Processing...' : 'Make Payment'}
                  </Button>
                  <Link href="/dashboard/dealers">
                    <Button type="button" variant="outline">
                      Cancel
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </form>
        </div>

        {/* Dealer Info and Quick Actions */}
        <div className="space-y-6">
          {/* Dealer Balance */}
          {selectedDealer && (
            <Card>
              <CardHeader>
                <CardTitle>Dealer Balance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600 mb-2">
                    {formatCurrency(selectedDealer.currentBalance)}
                  </div>
                  <p className="text-sm text-gray-600 mb-4">Current Outstanding Balance</p>
                  {selectedDealer.currentBalance > 0 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handlePayFullBalance}
                    >
                      <CreditCard className="h-4 w-4 mr-2" />
                      Pay Full Balance
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Unpaid Purchases */}
          {purchases.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Unpaid Purchases</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {purchases.map((purchase) => (
                    <div key={purchase.id} className="border rounded-lg p-3">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <div className="font-medium">{purchase.purchaseNo}</div>
                          <div className="text-sm text-gray-600">{formatDate(purchase.date)}</div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium text-red-600">
                            {formatCurrency(purchase.dueAmount)}
                          </div>
                          <div className="text-xs text-gray-600">Due</div>
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => handlePayPurchaseDue(purchase)}
                      >
                        Pay This Purchase
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
