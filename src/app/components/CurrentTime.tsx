'use client';

import { useState, useEffect } from 'react';
import { formatInDhaka } from '@/lib/timezone';
import { Clock, Calendar } from 'lucide-react';

export default function CurrentTime() {
  const [currentTime, setCurrentTime] = useState<string>('');
  const [currentDate, setCurrentDate] = useState<string>('');

  useEffect(() => {
    // Function to update the time and date
    const updateDateTime = () => {
      const now = new Date();
      // Format time in Dhaka timezone with hours, minutes, seconds and AM/PM
      const formattedTime = formatInDhaka(now, 'hh:mm:ss a');
      // Format date in Dhaka timezone
      const formattedDate = formatInDhaka(now, 'EEE, MMM dd, yyyy');

      setCurrentTime(formattedTime);
      setCurrentDate(formattedDate);
    };

    // Update time immediately
    updateDateTime();

    // Update time every second
    const intervalId = setInterval(updateDateTime, 1000);

    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, []);

  return (
    <div className="flex flex-wrap gap-2">
      <div className="text-sm bg-blue-50 text-blue-800 px-3 py-1.5 rounded-md flex items-center">
        <Calendar className="h-4 w-4 mr-1.5 flex-shrink-0" />
        <span className="whitespace-nowrap">{currentDate}</span>
      </div>
      <div className="text-sm bg-blue-50 text-blue-800 px-3 py-1.5 rounded-md flex items-center">
        <Clock className="h-4 w-4 mr-1.5 flex-shrink-0" />
        <span className="whitespace-nowrap">{currentTime}</span>
      </div>
    </div>
  );
}
