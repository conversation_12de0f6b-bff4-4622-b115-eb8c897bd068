import { NextResponse } from 'next/server';
import { db } from '../../../../lib/db';
import { tenants, tenantUsers, users } from '../../../../db/schema';
import { auth } from '../../../../../auth';
import { eq, and } from 'drizzle-orm';

// GET /api/tenants/[id] - Get a specific tenant
export async function GET(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Check authentication and authorization
    const session = await auth();
    
    // Only admin or the user themselves can access this endpoint
    if (!session?.user || (session.user.role !== 'admin' && session.user.id !== (await params).id)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Get user by ID
    const user = await db.query.users.findFirst({
      where: eq(users.id, (await params).id),
      with: {
        tenantOwned: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Process user and add subscription info
    const now = new Date();
    const tenant = user.tenantOwned[0]; // Get the first tenant owned by the user
    
    // Only process subscription info if tenant exists
    let subscriptionInfo = null;
    if (tenant) {
      const isActive = tenant.isActive && 
                      (tenant.subscriptionEndDate ? 
                        new Date(tenant.subscriptionEndDate) > now : 
                        false);
                        
      const daysRemaining = tenant.subscriptionEndDate ? 
        Math.max(0, Math.ceil((new Date(tenant.subscriptionEndDate).getTime() - now.getTime()) / 
                             (1000 * 60 * 60 * 24))) : 
        0;
      
      subscriptionInfo = {
        plan: tenant.subscriptionType,
        startDate: tenant.subscriptionStartDate,
        endDate: tenant.subscriptionEndDate,
        isActive,
        daysRemaining,
        paymentStatus: tenant.paymentStatus,
        lastPaymentDate: tenant.lastPaymentDate,
        paymentDueDate: tenant.paymentDueDate
      };
    }
    
    const userWithSubscriptionInfo = {
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.fullName,
      phone: user.phone || null,
      address: user.address || null,
      role: user.role,
      isActive: user.isActive,
      lastLogin: user.lastLogin || null,
      tenant: tenant ? {
        id: tenant.id,
        companyName: tenant.companyName,
        domain: tenant.domain || null,
        isActive: tenant.isActive,
        subscription: subscriptionInfo
      } : null
    };

    return NextResponse.json({
      success: true,
      user: userWithSubscriptionInfo
    });
  } catch (error) {
    console.error('Error fetching user with subscription:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch user with subscription details' },
      { status: 500 }
    );
  }
}

// PUT /api/tenants/[id] - Update a tenant
export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Ensure params is awaited before using its properties
    const tenantId = (await params).id;
    const session = await auth();
  

    // Check if tenant exists
    const existingTenant = await db.query.tenants.findFirst({
      where: eq(tenants.id, tenantId)
    });

    if (!existingTenant) {
      return NextResponse.json(
        { success: false, error: 'Tenant not found' },
        { status: 404 }
      );
    }

    // Parse request body
    const { 
      name, 
      domain,
      isActive,
      plan,
      planStartDate,
      planEndDate,
      settings
    } = await request.json();

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date()
    };

    if (name !== undefined) updateData.name = name;
    if (domain !== undefined) updateData.domain = domain;
    if (isActive !== undefined) updateData.isActive = isActive;
    if (plan !== undefined) updateData.plan = plan;
    if (planStartDate !== undefined) updateData.planStartDate = new Date(planStartDate);
    if (planEndDate !== undefined) updateData.planEndDate = new Date(planEndDate);
    if (settings !== undefined) updateData.settings = settings;

    // Update tenant
    await db.update(tenants)
      .set(updateData)
      .where(eq(tenants.id, tenantId));

    // Get updated tenant
    const updatedTenant = await db.query.tenants.findFirst({
      where: eq(tenants.id, tenantId)
    });

    return NextResponse.json({
      success: true,
      tenant: updatedTenant
    });
  } catch (error) {
    console.error('Error updating tenant:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update tenant' },
      { status: 500 }
    );
  }
}

// DELETE /api/tenants/[id] - Delete a tenant
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Ensure params is awaited before using its properties
    const tenantId = (await params).id;
    const session = await auth();
    
    // Only admin can delete tenants
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 403 }
      );
      }

    // Check if tenant exists
    const existingTenant = await db.query.tenants.findFirst({
      where: eq(tenants.id, tenantId)
    });

    if (!existingTenant) {
      return NextResponse.json(
        { success: false, error: 'Tenant not found' },
        { status: 404 }
      );
    }

    // Delete tenant
    await db.delete(tenants).where(eq(tenants.id, tenantId));

    // Note: We're not deleting the user, just removing the tenant record

    return NextResponse.json({
      success: true,
      message: 'Tenant deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting tenant:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete tenant' },
      { status: 500 }
    );
  }
} 