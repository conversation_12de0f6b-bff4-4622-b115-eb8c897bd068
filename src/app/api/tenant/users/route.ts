import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { users, tenantUsers } from '@/db/schema';
import { auth } from '~/auth';
import { and, eq, like, or } from 'drizzle-orm';

// GET /api/tenant/users - Get all tenant_sale users for the current tenant with optional search
export async function GET(request: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await auth();

    // Only tenant users can access this endpoint
    if (!session?.user || session.user.role !== 'tenant') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Only tenant admins can access tenant users.' },
        { status: 403 }
      );
    }

    // Get the search query from URL parameters
    const url = new URL(request.url);
    const searchQuery = url.searchParams.get("search");

    // Get all tenant_sale users for this tenant
    const tenantSaleUsers = await db.query.tenantUsers.findMany({
      where: eq(tenantUsers.tenantId, session.user.id),
      with: {
        user: true
      }
    });

    // Filter to only include tenant_sale users
    let filteredUsers = tenantSaleUsers.filter(tu => tu.user.role === 'tenant_sale');

    // Apply search filter if query is provided
    if (searchQuery && searchQuery.trim() !== "") {
      const lowerQuery = searchQuery.toLowerCase().trim();
      filteredUsers = filteredUsers.filter(tu =>
        (tu.user.fullName?.toLowerCase().includes(lowerQuery) || false) ||
        (tu.user.email?.toLowerCase().includes(lowerQuery) || false) ||
        (tu.user.username?.toLowerCase().includes(lowerQuery) || false)
      );
    }

    // Remove sensitive information
    const safeUsers = filteredUsers.map(tu => {
      const { password, ...userWithoutPassword } = tu.user;
      return {
        ...tu,
        user: userWithoutPassword
      };
    });

    return NextResponse.json({
      success: true,
      users: safeUsers
    });
  } catch (error) {
    console.error('Error fetching tenant users:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch tenant users' },
      { status: 500 }
    );
  }
}
