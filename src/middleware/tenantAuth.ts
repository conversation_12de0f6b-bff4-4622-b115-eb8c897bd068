import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../auth';
import { db } from '../lib/db';
import { tenants } from '../db/schema';
import { eq } from 'drizzle-orm';

// Middleware to check if tenant has active subscription
export async function checkTenantSubscription(req: NextRequest) {
  try {
    const session = await auth();

    // No session means no access
    if (!session?.user) {
      return {
        success: false,
        error: 'Authentication required',
        status: 401
      };
    }

    // Admin users bypass subscription check
    if (session.user.role === 'admin') {
      return {
        success: true,
        isAdmin: true
      };
    }

    // For tenant users, check if they have an active subscription
    if (session.user.role === 'tenant') {
      // Find tenant record for this user
      const tenant = await db.select()
        .from(tenants)
        .where(eq(tenants.userId, session.user.id))
        .get();

      // No tenant record found
      if (!tenant) {
        return {
          success: false,
          error: 'Tenant account not found',
          status: 404
        };
      }

      // Check if tenant is active
      if (!tenant.isActive) {
        return {
          success: false,
          error: 'Tenant account is inactive',
          status: 403,
          subscription: {
            isActive: false,
            tenant
          }
        };
      }

      // Check if subscription has not expired
      const now = new Date();
      if (!tenant.subscriptionEndDate || tenant.subscriptionEndDate < now) {
        return {
          success: false,
          error: 'Subscription has expired',
          status: 403,
          subscription: {
            isActive: false,
            tenant,
            expired: true
          }
        };
      }

      // Calculate days remaining
      const daysRemaining = tenant.subscriptionEndDate ?
        Math.max(0, Math.ceil((tenant.subscriptionEndDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))) :
        0;

      // Tenant is active with valid subscription
      return {
        success: true,
        tenant,
        subscription: {
          isActive: true,
          daysRemaining
        }
      };
    }

    // Other roles don't have tenant access
    return {
      success: false,
      error: 'Role not authorized for tenant access',
      status: 403
    };
  } catch (error) {
    console.error('Tenant authorization check error:', error);
    return {
      success: false,
      error: 'Error checking tenant authorization',
      status: 500
    };
  }
}

// Route handler wrapper for tenant subscription check
export function withTenantAuth(handler: Function) {
  return async (req: NextRequest, context: any) => {
    const authCheck = await checkTenantSubscription(req);

    if (!authCheck.success) {
      return NextResponse.json(
        { success: false, error: authCheck.error },
        { status: authCheck.status }
      );
    }

    // Pass auth check result to the handler
    return handler(req, context, authCheck);
  };
}