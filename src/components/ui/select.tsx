import * as React from "react";
import { cn } from "@/lib/utils";

// Create context outside of the component
const SelectContext = React.createContext<{
    isOpen: boolean;
    selectedValue: string;
    onSelect: (value: string) => void;
    toggle: () => void;
}>({
    isOpen: false,
    selectedValue: "",
    onSelect: () => { },
    toggle: () => { },
});

const Select = React.forwardRef<
    HTMLSelectElement,
    React.SelectHTMLAttributes<HTMLSelectElement> & {
        onValueChange?: (value: string) => void;
        defaultValue?: string;
    }
>(({ className, children, onValueChange, defaultValue, ...props }, ref) => {
    const [isOpen, setIsOpen] = React.useState(false);
    const [selectedValue, setSelectedValue] = React.useState(defaultValue || "");
    const selectRef = React.useRef<HTMLDivElement>(null);

    // Handle outside click to close dropdown
    React.useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    const handleSelect = (value: string) => {
        setSelectedValue(value);
        setIsOpen(false);
        if (onValueChange) {
            onValueChange(value);
        }
    };

    return (
        <SelectContext.Provider value={{
            isOpen,
            selectedValue,
            onSelect: handleSelect,
            toggle: () => setIsOpen(!isOpen)
        }}>
            <div ref={selectRef} className="relative w-full">
                {children}
            </div>
        </SelectContext.Provider>
    );
});
Select.displayName = "Select";

const SelectTrigger = React.forwardRef<
    HTMLButtonElement,
    React.ButtonHTMLAttributes<HTMLButtonElement>
>(({ className, children, ...props }, ref) => {
    const { isOpen, toggle } = React.useContext(SelectContext);

    return (
        <button
            ref={ref}
            type="button"
            onClick={toggle}
            aria-expanded={isOpen}
            className={cn(
                "flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
                className
            )}
            {...props}
        >
            {children}
            <span className="ml-2">
                <svg
                    width="15"
                    height="15"
                    viewBox="0 0 15 15"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className={`transition-transform ${isOpen ? 'rotate-180' : ''}`}
                >
                    <path
                        d="M3.13523 6.15803C3.3241 5.95657 3.64052 5.94637 3.84197 6.13523L7.5 9.56464L11.158 6.13523C11.3595 5.94637 11.6759 5.95657 11.8648 6.15803C12.0536 6.35949 12.0434 6.67591 11.842 6.86477L7.84197 10.6148C7.64964 10.7951 7.35036 10.7951 7.15803 10.6148L3.15803 6.86477C2.95657 6.67591 2.94637 6.35949 3.13523 6.15803Z"
                        fill="currentColor"
                    />
                </svg>
            </span>
        </button>
    );
});
SelectTrigger.displayName = "SelectTrigger";

const SelectValue = React.forwardRef<
    HTMLSpanElement,
    React.HTMLAttributes<HTMLSpanElement> & { placeholder?: string; children?: React.ReactNode; }
>(({ className, placeholder, children, ...props }, ref) => {
    const { selectedValue } = React.useContext(SelectContext);

    return (
        <span
            ref={ref}
            className={cn("flex h-full items-center truncate", className)}
            {...props}
        >
            {children || selectedValue || placeholder}
        </span>
    );
});
SelectValue.displayName = "SelectValue";

const SelectContent = React.forwardRef<
    HTMLDivElement,
    React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => {
    const { isOpen } = React.useContext(SelectContext);

    if (!isOpen) return null;

    return (
        <div
            ref={ref}
            className={cn(
                "absolute z-[100] w-full min-w-[8rem] max-h-[300px] overflow-y-auto rounded-md border bg-white p-1 text-black shadow-lg mt-1 left-0 top-full",
                className
            )}
            {...props}
        >
            {children}
        </div>
    );
});
SelectContent.displayName = "SelectContent";

const SelectItem = React.forwardRef<
    HTMLDivElement,
    React.HTMLAttributes<HTMLDivElement> & { value: string; }
>(({ className, children, value, ...props }, ref) => {
    const { onSelect, selectedValue } = React.useContext(SelectContext);

    return (
        <div
            ref={ref}
            className={cn(
                "relative flex w-full cursor-pointer select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-slate-100 focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
                selectedValue === value ? "bg-slate-100" : "",
                className
            )}
            onClick={() => onSelect(value)}
            {...props}
        >
            {selectedValue === value && (
                <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
                    <svg
                        width="15"
                        height="15"
                        viewBox="0 0 15 15"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M11.4669 3.72684C11.7558 3.91574 11.8369 4.30308 11.648 4.59198L7.39799 11.092C7.29783 11.2452 7.13556 11.3467 6.95402 11.3699C6.77247 11.3931 6.58989 11.3355 6.45446 11.2124L3.70446 8.71241C3.44905 8.48022 3.43023 8.08494 3.66242 7.82953C3.89461 7.57412 4.28989 7.55529 4.5453 7.78749L6.75292 9.79441L10.6018 3.90792C10.7907 3.61902 11.178 3.53795 11.4669 3.72684Z"
                            fill="currentColor"
                        />
                    </svg>
                </span>
            )}
            <span className="truncate">{children}</span>
        </div>
    );
});
SelectItem.displayName = "SelectItem";

export {
    Select,
    SelectTrigger,
    SelectValue,
    SelectContent,
    SelectItem,
};