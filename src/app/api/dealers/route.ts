import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { dealers } from "@/db/schema";
import { auth } from "~/auth";
import { v4 as uuidv4 } from "uuid";
import { eq, and, desc, sql, or, like } from "drizzle-orm";

// GET /api/dealers - Get all dealers for the current tenant
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get tenant ID based on user role
    const tenantId = session.user.role === 'tenant_sale' && session.user.tenantId
      ? session.user.tenantId
      : session.user.id;

    const { searchParams } = new URL(request.url);
    const dealerId = searchParams.get("id");
    const search = searchParams.get("search");

    // Case 1: Fetch a dealer by ID
    if (dealerId) {
      const dealer = await db.query.dealers.findFirst({
        where: and(
          eq(dealers.id, dealerId),
          eq(dealers.tenantId, tenantId)
        ),
      });

      if (!dealer) {
        return NextResponse.json(
          { success: false, error: "Dealer not found" },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        dealer,
      });
    }

    // Case 2: Fetch all dealers with optional search
    let whereCondition;

    if (search && search.trim() !== '') {
      // Search in name, email, phone, or dealer code
      whereCondition = and(
        eq(dealers.tenantId, tenantId),
        or(
          like(dealers.name, `%${search}%`),
          like(dealers.email, `%${search}%`),
          like(dealers.phone, `%${search}%`),
          like(dealers.dealerCode, `%${search}%`)
        )
      );
    } else {
      // No search - get all dealers for this tenant
      whereCondition = eq(dealers.tenantId, tenantId);
    }

    const dealersList = await db.query.dealers.findMany({
      where: whereCondition,
      orderBy: [desc(dealers.createdAt)],
    });

    return NextResponse.json({
      success: true,
      dealers: dealersList,
    });
  } catch (error) {
    console.error("Error fetching dealers:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch dealers" },
      { status: 500 }
    );
  }
}

// POST /api/dealers - Create a new dealer
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const data = await request.json();
    const {
      name,
      dealerCode = null,
      email = null,
      phone = null,
      address = null,
      contactPerson = null,
      creditLimit = 0,
      paymentTerms = null,
      isActive = true,
      notes = null,
    } = data;

    if (!name) {
      return NextResponse.json(
        { success: false, error: "Name is required" },
        { status: 400 }
      );
    }

    // Get tenant ID based on user role
    const tenantId = session.user.role === 'tenant_sale' && session.user.tenantId
      ? session.user.tenantId
      : session.user.id;

    const dealerId = uuidv4();

    // Create dealer
    const dealerValues = {
      id: dealerId,
      tenantId,
      name,
      dealerCode: dealerCode || null,
      email: email || null,
      phone: phone || null,
      address: address || null,
      contactPerson: contactPerson || null,
      currentBalance: 0, // Start with zero balance
      creditLimit: creditLimit || 0,
      paymentTerms: paymentTerms || null,
      isActive: isActive === true,
      notes: notes || null,
    };

    await db.insert(dealers).values(dealerValues);

    return NextResponse.json({
      success: true,
      message: "Dealer created successfully",
      dealerId: dealerId,
    });
  } catch (error) {
    console.error("Error creating dealer:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to create dealer",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

// PUT /api/dealers - Update an existing dealer
export async function PUT(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get tenant ID based on user role
    const tenantId = session.user.role === 'tenant_sale' && session.user.tenantId
      ? session.user.tenantId
      : session.user.id;

    const {
      id,
      name,
      dealerCode = null,
      email = null,
      phone = null,
      address = null,
      contactPerson = null,
      creditLimit = 0,
      paymentTerms = null,
      isActive = true,
      notes = null,
    } = await request.json();

    if (!id) {
      return NextResponse.json(
        { success: false, error: "Dealer ID is required" },
        { status: 400 }
      );
    }

    // Verify dealer exists and belongs to the tenant
    const existingDealer = await db.query.dealers.findFirst({
      where: sql`${dealers.id} = ${id} AND ${dealers.tenantId} = ${tenantId}`,
    });

    if (!existingDealer) {
      return NextResponse.json(
        { success: false, error: "Dealer not found" },
        { status: 404 }
      );
    }

    // Update dealer
    await db
      .update(dealers)
      .set({
        name,
        dealerCode: dealerCode || null,
        email: email || null,
        phone: phone || null,
        address: address || null,
        contactPerson: contactPerson || null,
        creditLimit: creditLimit || 0,
        paymentTerms: paymentTerms || null,
        isActive: isActive === true,
        notes: notes || null,
        updatedAt: new Date(),
      })
      .where(sql`${dealers.id} = ${id} AND ${dealers.tenantId} = ${tenantId}`);

    return NextResponse.json({
      success: true,
      message: "Dealer updated successfully",
    });
  } catch (error) {
    console.error("Error updating dealer:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to update dealer",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

// DELETE /api/dealers - Delete a dealer
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get tenant ID based on user role
    const tenantId = session.user.role === 'tenant_sale' && session.user.tenantId
      ? session.user.tenantId
      : session.user.id;

    const { searchParams } = new URL(request.url);
    const dealerId = searchParams.get("id");

    if (!dealerId) {
      return NextResponse.json(
        { success: false, error: "Dealer ID is required" },
        { status: 400 }
      );
    }

    // Check if the dealer exists and belongs to the tenant
    const existingDealer = await db.query.dealers.findFirst({
      where: sql`${dealers.id} = ${dealerId} AND ${dealers.tenantId} = ${tenantId}`,
    });

    if (!existingDealer) {
      return NextResponse.json(
        { success: false, error: "Dealer not found" },
        { status: 404 }
      );
    }

    // Delete the dealer
    await db
      .delete(dealers)
      .where(sql`${dealers.id} = ${dealerId} AND ${dealers.tenantId} = ${tenantId}`);

    return NextResponse.json({
      success: true,
      message: "Dealer deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting dealer:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to delete dealer",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
