'use client';

import { useActionState } from 'react';
import { useEffect } from 'react';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, CheckCircle2 } from "lucide-react";
import { updateBrandSettings, type BrandSettingsState } from './actions';

interface BrandSettingsFormProps {
  tenantId: string;
  companyName: string;
  phone: string | null;
  address: string | null;
}

const initialState: BrandSettingsState = {
  success: false,
  message: '',
};

export default function BrandSettingsForm({ tenantId, companyName, phone, address }: BrandSettingsFormProps) {
  const [state, formAction] = useActionState(updateBrandSettings, initialState);

  // Auto-dismiss the alert after 5 seconds
  useEffect(() => {
    if (state.message) {
      const timer = setTimeout(() => {
        // Reset the form state after 5 seconds
        // This is a workaround since we can't directly modify the state
        window.history.replaceState({}, '', '/dashboard/brand-settings');
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [state.message]);

  return (
    <>
      {state.message && (
        <Alert
          className={`mb-6 ${state.success ? "bg-green-50 text-green-800 border-green-200" : "bg-red-50 text-red-800 border-red-200"}`}
        >
          {state.success ? (
            <CheckCircle2 className="h-4 w-4 mr-2" />
          ) : (
            <AlertCircle className="h-4 w-4 mr-2" />
          )}
          <AlertTitle>{state.success ? "Success" : "Error"}</AlertTitle>
          <AlertDescription>{state.message}</AlertDescription>
        </Alert>
      )}

      <form action={formAction} className="space-y-6">
        <input type="hidden" name="tenantId" value={tenantId} />

        <div className="space-y-2">
          <Label htmlFor="companyName">Company Name</Label>
          <Input
            id="companyName"
            name="companyName"
            defaultValue={companyName ?? ''}
            required
          />
          <p className="text-sm text-muted-foreground">
            This will be displayed prominently in your memo header.
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number</Label>
          <Input
            id="phone"
            name="phone"
            defaultValue={phone ?? ''}
          />
          <p className="text-sm text-muted-foreground">
            Contact number that will appear in your memo header.
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="address">Address</Label>
          <Textarea
            id="address"
            name="address"
            defaultValue={address ?? ''}
            rows={3}
          />
          <p className="text-sm text-muted-foreground">
            Your business address that will be shown in the memo header.
          </p>
        </div>

        <Button type="submit" className='cursor-pointer'>Save Changes</Button>
      </form>
    </>
  );
}
