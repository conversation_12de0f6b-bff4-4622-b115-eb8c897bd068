'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { usePermissionCheck } from '@/lib/check-permissions';

// Define the schema for inventory form validation
const inventorySchema = z.object({
    productId: z.string().min(1, { message: 'Product is required' }),
    branchId: z.string().min(1, { message: 'Branch is required' }),
    quantity: z.number().min(0, { message: 'Quantity must be 0 or greater' }),
    warehouseQuantity: z.number().min(0, { message: 'Warehouse quantity must be 0 or greater' }),
    storeQuantity: z.number().min(0, { message: 'Store quantity must be 0 or greater' }),
    unitPrice: z.number().min(0, { message: 'Unit price must be 0 or greater' }),
    notes: z.string().nullable().optional(),
    transactionType: z.enum(['in', 'out', 'adjustment']),
});

// Define the type for form values
type InventoryFormValues = z.infer<typeof inventorySchema>;

// Define types for products and branches
interface Product {
    id: string;
    name: string;
    code: string | null;
    costPrice: number;
    retailPrice: number;
    unit?: string;
    isActive?: boolean;
    categoryId?: string | null;
    category?: {
        name: string;
    };
}

interface Branch {
    id: string;
    name: string;
    code: string;
    isMain?: boolean;
    isActive?: boolean;
}

// Define interface for current inventory data
interface CurrentInventory {
    quantity: number;
    warehouseStock: number;
    storeStock: number;
    lastStockUpdate: string | null;
}

export default function ClientWrapper() {
    // Check if user has permission to create inventory
    const { isLoading: permissionLoading } = usePermissionCheck('/dashboard/inventory', 'create');

    const router = useRouter();
    const [products, setProducts] = useState<Product[]>([]);
    const [branches, setBranches] = useState<Branch[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [submitting, setSubmitting] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [userRole, setUserRole] = useState<string | null>(null);
    const [userBranchId, setUserBranchId] = useState<string | null>(null);
    const [currentInventory, setCurrentInventory] = useState<CurrentInventory | null>(null);
    const [fetchingInventory, setFetchingInventory] = useState<boolean>(false);

    const {
        register,
        handleSubmit,
        formState: { errors },
        watch,
        setValue,
        getValues
    } = useForm<InventoryFormValues>({
        resolver: zodResolver(inventorySchema),
        defaultValues: {
            productId: '',
            branchId: '',
            quantity: 0,
            warehouseQuantity: 0,
            storeQuantity: 0,
            unitPrice: 0,
            notes: null,
            transactionType: 'in',
        },
    });

    // Watch for changes in productId to update unit price
    const watchProductId = watch('productId');
    const watchBranchId = watch('branchId');

    // Watch for changes in quantities to update allocation
    const watchQuantity = watch('quantity');
    const watchWarehouseQuantity = watch('warehouseQuantity');
    const watchStoreQuantity = watch('storeQuantity');

    // Function to fetch a specific product by ID
    const fetchProductById = async (productId: string) => {
        try {
            console.log('Fetching specific product with ID:', productId);
            const response = await fetch(`/api/products?id=${productId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache',
                },
            });

            if (!response.ok) {
                console.error('Failed to fetch product by ID, status:', response.status);
                return;
            }

            const data = await response.json();
            console.log('Fetched product by ID response:', data);

            if (data.success && data.product) {
                // Add this product to the products list if it's not already there
                setProducts(prevProducts => {
                    if (!prevProducts.some(p => p.id === data.product.id)) {
                        return [...prevProducts, data.product];
                    }
                    return prevProducts;
                });

                // Set the product ID in the form
                setValue('productId', data.product.id);
                console.log('Set product ID from direct fetch:', data.product.id);
            }
        } catch (err) {
            console.error('Error fetching product by ID:', err);
        }
    };

    useEffect(() => {
        fetchProducts();
        fetchBranches();
    }, []);

    // Check for productId in the URL and set it if found
    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);

        // Check for both 'productId' and 'id' parameters
        const productIdFromUrl = urlParams.get('productId') || urlParams.get('id');
        console.log('Product ID from URL:', productIdFromUrl);

        if (productIdFromUrl && products.length > 0) {
            console.log('Setting product ID from URL:', productIdFromUrl);
            console.log('Available products:', products.map(p => p.id));

            // Check if the product exists in the loaded products
            const productExists = products.some(p => p.id === productIdFromUrl);
            if (productExists) {
                setValue('productId', productIdFromUrl);
                console.log('Successfully set product ID from URL');
            } else {
                console.error('Product ID from URL not found in loaded products:', productIdFromUrl);
                // Try to fetch the product directly
                fetchProductById(productIdFromUrl);
            }
        } else if (productIdFromUrl && products.length === 0) {
            // If products haven't loaded yet but we have a product ID, fetch it directly
            fetchProductById(productIdFromUrl);
        }
    }, [setValue, products]); // Add products as a dependency

    // Function to fetch current inventory for a product
    const fetchCurrentInventory = useCallback(async (productId: string, branchId: string = '') => {
        if (!productId) {
            setCurrentInventory(null);
            return;
        }

        try {
            setFetchingInventory(true);

            // Build the URL with query parameters
            let url = `/api/inventory?productId=${productId}`;
            if (branchId) {
                url += `&branchId=${branchId}`;
            }

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache',
                },
            });

            if (!response.ok) {
                console.error('Failed to fetch inventory data, status:', response.status);
                setCurrentInventory(null);
                return;
            }

            const data = await response.json();

            if (data.success && data.inventory && data.inventory.length > 0) {
                // Sum up quantities across all branches if no specific branch is selected
                if (!branchId) {
                    const totalQuantity = data.inventory.reduce((sum: number, item: any) => sum + item.quantity, 0);
                    const totalWarehouseStock = data.inventory.reduce((sum: number, item: any) => sum + item.warehouseStock, 0);
                    const totalStoreStock = data.inventory.reduce((sum: number, item: any) => sum + item.storeStock, 0);

                    // Find the most recent update date
                    let latestUpdate: string | null = null;
                    data.inventory.forEach((item: any) => {
                        if (item.lastStockUpdate && (!latestUpdate || new Date(item.lastStockUpdate) > new Date(latestUpdate))) {
                            latestUpdate = item.lastStockUpdate;
                        }
                    });

                    setCurrentInventory({
                        quantity: totalQuantity,
                        warehouseStock: totalWarehouseStock,
                        storeStock: totalStoreStock,
                        lastStockUpdate: latestUpdate
                    });
                } else {
                    // Find the inventory for the specific branch
                    const branchInventory = data.inventory.find((item: any) => item.branchId === branchId);

                    if (branchInventory) {
                        setCurrentInventory({
                            quantity: branchInventory.quantity,
                            warehouseStock: branchInventory.warehouseStock,
                            storeStock: branchInventory.storeStock,
                            lastStockUpdate: branchInventory.lastStockUpdate
                        });
                    } else {
                        // No inventory found for this branch
                        setCurrentInventory({
                            quantity: 0,
                            warehouseStock: 0,
                            storeStock: 0,
                            lastStockUpdate: null
                        });
                    }
                }
            } else {
                // No inventory found
                setCurrentInventory({
                    quantity: 0,
                    warehouseStock: 0,
                    storeStock: 0,
                    lastStockUpdate: null
                });
            }
        } catch (err) {
            console.error('Error fetching inventory:', err);
            setCurrentInventory(null);
        } finally {
            setFetchingInventory(false);
        }
    }, [setCurrentInventory, setFetchingInventory]);

    // Update unit price when product selection changes
    useEffect(() => {
        if (watchProductId && products.length > 0) {
            const product = products.find(p => p.id === watchProductId);
            if (product) {
                // Update unit price with product cost price
                setValue('unitPrice', product.costPrice || 0);

                // Fetch current inventory for this product
                const branchId = getValues('branchId');
                fetchCurrentInventory(watchProductId, branchId);
            }
        } else {
            // Clear current inventory if no product is selected
            setCurrentInventory(null);
        }
    }, [watchProductId, products, setValue, getValues, fetchCurrentInventory]);

    // Update inventory when branch selection changes
    useEffect(() => {
        if (watchProductId && watchBranchId) {
            fetchCurrentInventory(watchProductId, watchBranchId);
        }
    }, [watchBranchId, watchProductId, fetchCurrentInventory]);

    // Update quantities to maintain the constraint: warehouse + store = total
    useEffect(() => {
        // Get the current values
        const totalQty = getValues('quantity');
        const warehouseQty = getValues('warehouseQuantity');
        const storeQty = getValues('storeQuantity');

        // Determine which field was last changed
        const lastChanged = document.activeElement?.id;

        // If total quantity was changed, adjust store quantity
        if (lastChanged === 'quantity') {
            // Keep warehouse the same, adjust store
            setValue('storeQuantity', Math.max(0, totalQty - warehouseQty));
        }
        // If warehouse quantity was changed, adjust store quantity
        else if (lastChanged === 'warehouseQuantity') {
            // Keep total the same, adjust store
            setValue('storeQuantity', Math.max(0, totalQty - warehouseQty));
        }
        // If store quantity was changed, adjust warehouse quantity
        else if (lastChanged === 'storeQuantity') {
            // Keep total the same, adjust warehouse
            setValue('warehouseQuantity', Math.max(0, totalQty - storeQty));
        }
        // If no field is focused (initial load), put all quantity in store
        else if (totalQty > 0 && warehouseQty === 0 && storeQty === 0) {
            // Put all quantity in store
            setValue('warehouseQuantity', 0);
            setValue('storeQuantity', totalQty);
        }
    }, [watchQuantity, watchWarehouseQuantity, watchStoreQuantity, setValue, getValues]);


    const fetchProducts = async () => {
        try {
            setLoading(true);
            setError(null); // Clear any previous errors

            // Get productId from URL if present
            const urlParams = new URLSearchParams(window.location.search);
            const productIdFromUrl = urlParams.get('productId') || urlParams.get('id');
            console.log('Fetching products with URL product ID:', productIdFromUrl);

            // Make sure we're using the correct API endpoint
            const response = await fetch('/api/products', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    // Add cache control to prevent caching issues
                    'Cache-Control': 'no-cache',
                },
            });

            // If we have a product ID in the URL, also fetch that specific product
            if (productIdFromUrl) {
                // We'll fetch the specific product in parallel
                fetchProductById(productIdFromUrl);
            }

            console.log('Products API response status:', response.status);

            // Get the response text for debugging
            const responseText = await response.text();

            // Try to parse as JSON or log the text if it fails
            let data;
            try {
                data = JSON.parse(responseText);
                console.log('Fetched products response:', data);
            } catch (e) {
                console.error('Failed to parse products response as JSON:', responseText);
                throw new Error('Invalid response format from products API');
            }

            if (data.success) {
                if (Array.isArray(data.products)) {
                    setProducts(data.products);
                } else {
                    console.error('Products data is not an array:', data.products);
                    setError('Invalid products data format');
                }
            } else {
                setError(data.error || 'Failed to load products');
            }
        } catch (err) {
            console.error('Error fetching products:', err);
            setError(typeof err === 'object' && err !== null && 'message' in err
                ? (err as Error).message
                : 'An error occurred while fetching products');
        } finally {
            setLoading(false);
        }
    };

    const fetchBranches = async () => {
        try {
            setLoading(true);
            setError(null);

            // First get the user session to check role and branchId
            const sessionResponse = await fetch('/api/auth/session');
            if (!sessionResponse.ok) {
                console.error('Failed to fetch session');
                throw new Error('Failed to fetch user session');
            }

            const sessionData = await sessionResponse.json();
            const userRole = sessionData.user?.role;
            const userBranchId = sessionData.user?.branchId;

            setUserRole(userRole);
            setUserBranchId(userBranchId);

            console.log('User role from session:', userRole);
            console.log('User branch ID from session:', userBranchId);

            const response = await fetch('/api/branches', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache',
                },
            });

            const responseText = await response.text();

            let data;
            try {
                data = JSON.parse(responseText);
            } catch (e) {
                console.error('Failed to parse branches response as JSON:', responseText);
                throw new Error('Invalid response format from branches API');
            }

            if (data.success) {
                if (Array.isArray(data.branches)) {
                    // For tenant_sale users, filter to only show their assigned branch
                    if (userRole === 'tenant_sale' && userBranchId) {
                        const userBranch = data.branches.find((b: Branch) => b.id === userBranchId && b.isActive !== false);
                        if (userBranch) {
                            // Only show the user's assigned branch if it's active
                            setBranches([userBranch]);
                            // Automatically select the user's branch
                            setValue('branchId', userBranchId);
                            console.log('Tenant sale user - setting branch to:', userBranchId);
                        } else {
                            console.error('User branch not found in branches list or is inactive');
                            setBranches([]);
                            setError('Your assigned branch was not found or is inactive. Please contact your administrator.');
                        }
                    } else {
                        // For other users, show only active branches
                        const activeBranches = data.branches.filter((b: Branch) => b.isActive !== false);
                        setBranches(activeBranches);

                        // If there's only one branch, select it by default
                        if (activeBranches.length === 1) {
                            setValue('branchId', activeBranches[0].id);
                        }

                        // If there's a main branch, select it by default
                        const mainBranch = activeBranches.find((b: Branch) => b.isMain);
                        if (mainBranch) {
                            setValue('branchId', mainBranch.id);
                        }
                    }
                } else {
                    console.error('Branches data is not an array:', data.branches);
                    setError('Invalid branches data format');
                }
            } else {
                setError(data.error || 'Failed to load branches');
            }
        } catch (err) {
            console.error('Error fetching branches:', err);
            setError(typeof err === 'object' && err !== null && 'message' in err
                ? (err as Error).message
                : 'An error occurred while fetching branches');
        } finally {
            setLoading(false);
        }
    };

    const onSubmit = async (data: InventoryFormValues) => {
        try {
            setSubmitting(true);
            setError(null);

            console.log('Submitting inventory data:', data);

            const response = await fetch('/api/inventory', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
            });

            console.log('Response status:', response.status);
            const result = await response.json();
            console.log('Response data:', result);

            if (!response.ok) {
                throw new Error(result.error || 'Failed to add inventory');
            }

            if (result.success) {
                // Redirect to inventory list page
                router.push('/dashboard/inventory');
                router.refresh();
            } else {
                setError(result.error || 'Failed to add inventory');
            }
        } catch (err: any) {
            console.error('Error adding inventory:', err);
            setError(err.message || 'An error occurred while adding inventory');
        } finally {
            setSubmitting(false);
        }
    };

    if (permissionLoading || loading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold">Add New Inventory</h1>
                <button
                    onClick={() => router.back()}
                    className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 cursor-pointer"
                >
                    Back to Inventory
                </button>
            </div>

            {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">Error: </strong>
                    <span className="block sm:inline">{error}</span>
                </div>
            )}

            {products.length === 0 && !loading && (
                <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">No products found! </strong>
                    <span className="block sm:inline">Please create products first before adding inventory.</span>
                    <button
                        onClick={() => router.push('/dashboard/products/new')}
                        className="mt-2 bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-1 px-3 rounded focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50"
                    >
                        Create Product
                    </button>
                </div>
            )}

            {branches.length === 0 && !loading && (
                <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">No branches found! </strong>
                    <span className="block sm:inline">Please create at least one branch before adding inventory.</span>
                    <button
                        onClick={() => router.push('/dashboard/branches')}
                        className="mt-2 bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-1 px-3 rounded focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50"
                    >
                        Manage Branches
                    </button>
                </div>
            )}

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <form onSubmit={handleSubmit(onSubmit)}>
                    {watchWarehouseQuantity + watchStoreQuantity !== watchQuantity && (
                        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded relative mb-6" role="alert">
                            <strong className="font-bold">Warning: </strong>
                            <span className="block sm:inline">
                                The sum of Warehouse Quantity ({watchWarehouseQuantity}) and Store Quantity ({watchStoreQuantity}) must equal Total Quantity ({watchQuantity}).
                            </span>
                        </div>
                    )}
                    <div className="mb-6">
                        <label htmlFor="productId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Product *
                        </label>
                        <select
                            id="productId"
                            {...register('productId')}
                            className={`w-full border ${errors.productId ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm px-4 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500`}
                            disabled={products.length === 0}
                        >
                            <option value="">Select Product</option>
                            {products.filter(product => product.isActive).map((product) => (
                                <option key={product.id} value={product.id}>
                                    {product.name} {product.code ? `(${product.code})` : ''}
                                </option>
                            ))}
                        </select>
                        {errors.productId && (
                            <p className="mt-1 text-sm text-red-600">{errors.productId.message}</p>
                        )}

                        {/* Display current inventory information */}
                        {watchProductId && (
                            <div className="mt-2">
                                {fetchingInventory ? (
                                    <p className="text-sm text-gray-500">Loading current inventory...</p>
                                ) : currentInventory ? (
                                    <div className="bg-blue-50 border border-blue-200 rounded p-2">
                                        <p className="text-sm font-medium text-blue-800">Current Inventory:</p>
                                        <div className="grid grid-cols-3 gap-2 mt-1">
                                            <div>
                                                <p className="text-xs text-gray-500">Total</p>
                                                <p className="text-sm font-medium">{currentInventory.quantity}</p>
                                            </div>
                                            <div>
                                                <p className="text-xs text-gray-500">Warehouse</p>
                                                <p className="text-sm font-medium">{currentInventory.warehouseStock}</p>
                                            </div>
                                            <div>
                                                <p className="text-xs text-gray-500">Store</p>
                                                <p className="text-sm font-medium">{currentInventory.storeStock}</p>
                                            </div>
                                        </div>
                                        {currentInventory.lastStockUpdate && (
                                            <p className="text-xs text-gray-500 mt-1">
                                                Last updated: {new Date(currentInventory.lastStockUpdate).toLocaleString()}
                                            </p>
                                        )}
                                    </div>
                                ) : (
                                    <p className="text-sm text-gray-500">No current inventory data available</p>
                                )}
                            </div>
                        )}
                    </div>

                    <div className="mb-6">
                        <label htmlFor="branchId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Branch *
                        </label>
                        <select
                            id="branchId"
                            {...register('branchId')}
                            className={`w-full border ${errors.branchId ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm px-4 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500`}
                            disabled={branches.length === 0 || userRole === 'tenant_sale'}
                        >
                            <option value="">Select Branch</option>
                            {branches.map((branch) => (
                                <option key={branch.id} value={branch.id}>
                                    {branch.name} ({branch.code})
                                </option>
                            ))}
                        </select>
                        {userRole === 'tenant_sale' && userBranchId && (
                            <p className="mt-1 text-xs text-gray-500">
                                As a sales user, you can only work with your assigned branch.
                            </p>
                        )}
                        {errors.branchId && (
                            <p className="mt-1 text-sm text-red-600">{errors.branchId.message}</p>
                        )}
                    </div>

                    <div className="mb-6">
                        <label htmlFor="transactionType" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Transaction Type *
                        </label>
                        <select
                            id="transactionType"
                            {...register('transactionType')}
                            className={`w-full border ${errors.transactionType ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm px-4 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500`}
                        >
                            <option value="in">Stock In</option>
                            <option value="out">Stock Out</option>
                            <option value="adjustment">Adjustment</option>
                        </select>
                        {errors.transactionType && (
                            <p className="mt-1 text-sm text-red-600">{errors.transactionType.message}</p>
                        )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Total Quantity *
                            </label>
                            <input
                                type="number"
                                id="quantity"
                                min="0"
                                step="1"
                                {...register('quantity', { valueAsNumber: true })}
                                className={`w-full border ${errors.quantity ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm px-4 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500`}
                                onChange={(e) => {
                                    const newTotal = parseInt(e.target.value) || 0;
                                    // Initially fill all quantity to store
                                    setValue('quantity', newTotal);
                                    setValue('warehouseQuantity', 0);
                                    setValue('storeQuantity', newTotal);
                                }}
                            />
                            {errors.quantity && (
                                <p className="mt-1 text-sm text-red-600">{errors.quantity.message}</p>
                            )}
                        </div>

                        <div>
                            <label htmlFor="unitPrice" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Unit Price *
                            </label>
                            <input
                                type="number"
                                id="unitPrice"
                                min="0"
                                step="0.01"
                                {...register('unitPrice', { valueAsNumber: true })}
                                className={`w-full border ${errors.unitPrice ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm px-4 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500`}
                            />
                            {errors.unitPrice && (
                                <p className="mt-1 text-sm text-red-600">{errors.unitPrice.message}</p>
                            )}
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label htmlFor="warehouseQuantity" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Warehouse Quantity *
                            </label>
                            <input
                                type="number"
                                id="warehouseQuantity"
                                min="0"
                                step="1"
                                {...register('warehouseQuantity', { valueAsNumber: true })}
                                className={`w-full border ${errors.warehouseQuantity ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm px-4 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500`}
                            />
                            {errors.warehouseQuantity && (
                                <p className="mt-1 text-sm text-red-600">{errors.warehouseQuantity.message}</p>
                            )}
                        </div>

                        <div>
                            <label htmlFor="storeQuantity" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Store Quantity *
                            </label>
                            <input
                                type="number"
                                id="storeQuantity"
                                min="0"
                                step="1"
                                {...register('storeQuantity', { valueAsNumber: true })}
                                className={`w-full border ${errors.storeQuantity ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm px-4 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500`}
                            />
                            {errors.storeQuantity && (
                                <p className="mt-1 text-sm text-red-600">{errors.storeQuantity.message}</p>
                            )}
                            <p className={`mt-1 text-sm ${Number(watchWarehouseQuantity) + Number(watchStoreQuantity) === Number(watchQuantity) ? 'text-gray-500' : 'text-red-500 font-medium'}`}>
                                Warehouse ({watchWarehouseQuantity}) + Store ({watchStoreQuantity}) = {Number(watchWarehouseQuantity) + Number(watchStoreQuantity)} {Number(watchWarehouseQuantity) + Number(watchStoreQuantity) !== Number(watchQuantity) && `(should be ${watchQuantity})`}
                            </p>

                            {Number(watchWarehouseQuantity) + Number(watchStoreQuantity) !== Number(watchQuantity) && (
                                <div className="flex space-x-2 mt-2">
                                    <button
                                        type="button"
                                        onClick={() => {
                                            const totalQty = getValues('quantity');
                                            setValue('warehouseQuantity', 0);
                                            setValue('storeQuantity', totalQty);
                                        }}
                                        className="bg-blue-500 hover:bg-blue-600 text-white text-xs font-medium py-1 px-2 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                                    >
                                        Fill to Store
                                    </button>
                                    <button
                                        type="button"
                                        onClick={() => {
                                            const totalQty = getValues('quantity');
                                            const half = Math.floor(totalQty / 2);
                                            setValue('warehouseQuantity', half);
                                            setValue('storeQuantity', totalQty - half);
                                        }}
                                        className="bg-blue-500 hover:bg-blue-600 text-white text-xs font-medium py-1 px-2 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                                    >
                                        Split 50/50
                                    </button>
                                    <button
                                        type="button"
                                        onClick={() => {
                                            const totalQty = getValues('quantity');
                                            setValue('warehouseQuantity', totalQty);
                                            setValue('storeQuantity', 0);
                                        }}
                                        className="bg-blue-500 hover:bg-blue-600 text-white text-xs font-medium py-1 px-2 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                                    >
                                        Fill to Warehouse
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="mb-6">
                        <label htmlFor="notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Notes (Optional)
                        </label>
                        <textarea
                            id="notes"
                            {...register('notes')}
                            rows={3}
                            className="w-full border border-gray-300 rounded-md shadow-sm px-4 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Add any additional notes about this inventory transaction"
                        ></textarea>
                    </div>

                    <div className="flex justify-end">
                        <button
                            type="submit"
                            disabled={submitting || products.length === 0 || branches.length === 0}
                            className={`bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 ${submitting || products.length === 0 || branches.length === 0 ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                        >
                            {submitting ? 'Adding...' : 'Add Inventory'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}
