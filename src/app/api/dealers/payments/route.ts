import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { dealerPayments, dealers, dealerPurchases } from "@/db/schema";
import { auth } from "~/auth";
import { v4 as uuidv4 } from "uuid";
import { eq, and, desc, sql } from "drizzle-orm";

// GET /api/dealers/payments - Get all payments for a dealer or all payments
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get tenant ID based on user role
    const tenantId = session.user.role === 'tenant_sale' && session.user.tenantId
      ? session.user.tenantId
      : session.user.id;

    const { searchParams } = new URL(request.url);
    const dealerId = searchParams.get("dealerId");
    const paymentId = searchParams.get("id");

    // Case 1: Get specific payment by ID
    if (paymentId) {
      const payment = await db.query.dealerPayments.findFirst({
        where: and(
          eq(dealerPayments.id, paymentId),
          eq(dealerPayments.tenantId, tenantId)
        ),
        with: {
          dealer: true,
          branch: true,
          purchase: true,
          performer: {
            columns: {
              id: true,
              fullName: true,
            },
          },
        },
      });

      if (!payment) {
        return NextResponse.json(
          { success: false, error: "Payment not found" },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        payment,
      });
    }

    // Case 2: Get all payments for a specific dealer
    if (dealerId) {
      const payments = await db.query.dealerPayments.findMany({
        where: and(
          eq(dealerPayments.dealerId, dealerId),
          eq(dealerPayments.tenantId, tenantId)
        ),
        orderBy: [desc(dealerPayments.createdAt)],
        with: {
          dealer: true,
          branch: true,
          purchase: true,
          performer: {
            columns: {
              id: true,
              fullName: true,
            },
          },
        },
      });

      return NextResponse.json({
        success: true,
        payments,
      });
    }

    // Case 3: Get all payments for the tenant
    const payments = await db.query.dealerPayments.findMany({
      where: eq(dealerPayments.tenantId, tenantId),
      orderBy: [desc(dealerPayments.createdAt)],
      with: {
        dealer: true,
        branch: true,
        purchase: true,
        performer: {
          columns: {
            id: true,
            fullName: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      payments,
    });
  } catch (error) {
    console.error("Error fetching dealer payments:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch payments" },
      { status: 500 }
    );
  }
}

// POST /api/dealers/payments - Create a new payment to dealer
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const data = await request.json();
    const {
      dealerId,
      branchId,
      purchaseId = null, // Optional: link to specific purchase
      amount,
      paymentMethod = "cash",
      paymentReference = null,
      paymentType = "purchase_payment",
      date,
      notes = null,
    } = data;

    // Validate required fields
    if (!dealerId || !branchId || !amount || amount <= 0) {
      return NextResponse.json(
        { success: false, error: "Missing required fields or invalid amount" },
        { status: 400 }
      );
    }

    // Get tenant ID based on user role
    const tenantId = session.user.role === 'tenant_sale' && session.user.tenantId
      ? session.user.tenantId
      : session.user.id;

    // Verify dealer exists and belongs to tenant
    const dealer = await db.query.dealers.findFirst({
      where: and(
        eq(dealers.id, dealerId),
        eq(dealers.tenantId, tenantId)
      ),
    });

    if (!dealer) {
      return NextResponse.json(
        { success: false, error: "Dealer not found" },
        { status: 404 }
      );
    }

    // If purchaseId is provided, verify it exists and belongs to the dealer
    if (purchaseId) {
      const purchase = await db.query.dealerPurchases.findFirst({
        where: and(
          eq(dealerPurchases.id, purchaseId),
          eq(dealerPurchases.dealerId, dealerId),
          eq(dealerPurchases.tenantId, tenantId)
        ),
      });

      if (!purchase) {
        return NextResponse.json(
          { success: false, error: "Purchase not found" },
          { status: 404 }
        );
      }
    }

    const paymentId = uuidv4();

    await db.transaction(async (tx) => {
      // Create the payment record
      await tx.insert(dealerPayments).values({
        id: paymentId,
        tenantId,
        branchId,
        dealerId,
        purchaseId: purchaseId || null,
        amount,
        paymentMethod: paymentMethod as "cash" | "card" | "bank_transfer" | "cheque",
        paymentReference: paymentReference || null,
        paymentType: paymentType as "purchase_payment" | "advance_payment" | "adjustment",
        date: new Date(date || new Date()),
        notes: notes || null,
        performedBy: session.user.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Update dealer balance (reduce the amount we owe them)
      await tx
        .update(dealers)
        .set({
          currentBalance: sql`${dealers.currentBalance} - ${amount}`,
          updatedAt: new Date(),
        })
        .where(eq(dealers.id, dealerId));

      // If payment is linked to a specific purchase, update purchase payment status
      if (purchaseId) {
        const purchase = await tx.query.dealerPurchases.findFirst({
          where: eq(dealerPurchases.id, purchaseId),
        });

        if (purchase) {
          const newPaidAmount = (purchase.paidAmount || 0) + amount;
          const newDueAmount = purchase.totalAmount - newPaidAmount;
          
          let newPaymentStatus: "paid" | "partial" | "unpaid" = "unpaid";
          if (newPaidAmount >= purchase.totalAmount) {
            newPaymentStatus = "paid";
          } else if (newPaidAmount > 0) {
            newPaymentStatus = "partial";
          }

          await tx
            .update(dealerPurchases)
            .set({
              paidAmount: newPaidAmount,
              dueAmount: Math.max(0, newDueAmount),
              paymentStatus: newPaymentStatus,
              updatedAt: new Date(),
            })
            .where(eq(dealerPurchases.id, purchaseId));
        }
      }
    });

    return NextResponse.json({
      success: true,
      message: "Payment created successfully",
      paymentId,
    });
  } catch (error) {
    console.error("Error creating dealer payment:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to create payment",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

// PUT /api/dealers/payments - Update an existing payment
export async function PUT(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const data = await request.json();
    const {
      id,
      amount,
      paymentMethod = "cash",
      paymentReference = null,
      paymentType = "purchase_payment",
      date,
      notes = null,
    } = data;

    if (!id || !amount || amount <= 0) {
      return NextResponse.json(
        { success: false, error: "Missing required fields or invalid amount" },
        { status: 400 }
      );
    }

    // Get tenant ID based on user role
    const tenantId = session.user.role === 'tenant_sale' && session.user.tenantId
      ? session.user.tenantId
      : session.user.id;

    // Get existing payment
    const existingPayment = await db.query.dealerPayments.findFirst({
      where: and(
        eq(dealerPayments.id, id),
        eq(dealerPayments.tenantId, tenantId)
      ),
    });

    if (!existingPayment) {
      return NextResponse.json(
        { success: false, error: "Payment not found" },
        { status: 404 }
      );
    }

    const amountDifference = amount - existingPayment.amount;

    await db.transaction(async (tx) => {
      // Update the payment record
      await tx
        .update(dealerPayments)
        .set({
          amount,
          paymentMethod: paymentMethod as "cash" | "card" | "bank_transfer" | "cheque",
          paymentReference: paymentReference || null,
          paymentType: paymentType as "purchase_payment" | "advance_payment" | "adjustment",
          date: new Date(date || new Date()),
          notes: notes || null,
          updatedAt: new Date(),
        })
        .where(eq(dealerPayments.id, id));

      // Update dealer balance with the difference
      if (amountDifference !== 0) {
        await tx
          .update(dealers)
          .set({
            currentBalance: sql`${dealers.currentBalance} - ${amountDifference}`,
            updatedAt: new Date(),
          })
          .where(eq(dealers.id, existingPayment.dealerId));
      }
    });

    return NextResponse.json({
      success: true,
      message: "Payment updated successfully",
    });
  } catch (error) {
    console.error("Error updating dealer payment:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to update payment",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
