'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';

interface User {
    id: string;
    username: string;
    email: string;
    fullName: string;
    phone?: string | null;
    address?: string | null;
    role: string;
    isActive: boolean;
    lastLogin?: string | null;
}

interface Subscription {
    plan: string;
    startDate: string;
    endDate: string;
    isActive: boolean;
    daysRemaining: number;
    paymentStatus: string;
    lastPaymentDate?: string | null;
    paymentDueDate?: string;
}

interface Tenant {
    id: string;
    companyName: string;
    domain?: string | null;
    isActive: boolean;
    subscription: Subscription;
}

export default function TenantDetailsPage() {
    const router = useRouter();
    const params = useParams();
    const tenantId = params?.id as string | undefined;

    const [tenant, setTenant] = useState<Tenant | null>(null);
    const [user, setUser] = useState<User | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string>('');
    const [successMessage, setSuccessMessage] = useState<string>('');

    useEffect(() => {
        fetchTenantDetails();
    }, [tenantId]);

    const fetchTenantDetails = async () => {
        try {
            setLoading(true);
            setError('');

            const response = await fetch(`/api/tenants/${tenantId}`);

            if (!response.ok) {
                throw new Error('Failed to fetch tenant details');
            }

            const data = await response.json();

            if (data.success) {
                setTenant(data.user.tenant);
                setUser(data.user);
            } else {
                setError(data.error || 'Failed to load tenant details');
            }
        } catch (err) {
            setError('An error occurred while fetching tenant details');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    const handleActivateDeactivate = async () => {
        if (!tenant || !user) return;

        try {
            setSuccessMessage('');
            setError('');

            const response = await fetch(`/api/tenants/${user.id}/status`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    isActive: !tenant.isActive
                }),
            });

            const data = await response.json();

            if (data.success) {
                setSuccessMessage(`Tenant ${tenant.isActive ? 'deactivated' : 'activated'} successfully`);
                // Update tenant data
                setTenant(prev => prev ? { ...prev, isActive: !prev.isActive } : null);
                // Also update the user's active status to match
                setUser(prev => prev ? { ...prev, isActive: !tenant.isActive } : null);
            } else {
                setError(data.error || `Failed to ${tenant.isActive ? 'deactivate' : 'activate'} tenant`);
            }
        } catch (err) {
            setError(`An error occurred while ${tenant.isActive ? 'deactivating' : 'activating'} the tenant`);
            console.error(err);
        }
    };

    const handleDeleteTenant = async () => {
        if (!window.confirm('Are you sure you want to delete this tenant? This action cannot be undone.')) {
            return;
        }

        if (!tenant || !tenant.id) {
            setError('Cannot delete tenant: Tenant ID is missing');
            return;
        }

        try {
            setError('');
            setSuccessMessage('');

            // Use the actual tenant.id instead of the user ID (tenantId)
            const response = await fetch(`/api/tenants/${tenant.id}`, {
                method: 'DELETE',
            });

            const data = await response.json();

            if (data.success) {
                setSuccessMessage('Tenant deleted successfully');
                // Redirect back to tenants list after short delay
                setTimeout(() => {
                    router.push('/dashboard/tenants');
                }, 1500);
            } else {
                setError(data.error || 'Failed to delete tenant');
            }
        } catch (err) {
            setError('An error occurred while deleting the tenant');
            console.error(err);
        }
    };

    const formatDate = (dateString: string) => {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (!tenant) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="flex items-center mb-6">
                    <button
                        onClick={() => router.push('/dashboard/tenants')}
                        className="mr-4 text-blue-500 hover:text-blue-700"
                    >
                        &larr; Back to Tenants
                    </button>
                    <h1 className="text-2xl font-bold">Tenant Not Found</h1>
                </div>
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <strong className="font-bold">Error: </strong>
                    <span className="block sm:inline">{error || 'Tenant could not be found'}</span>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="flex items-center mb-6">
                <button
                    onClick={() => router.push('/dashboard/tenants')}
                    className="mr-4 text-blue-500 hover:text-blue-700 cursor-pointer"
                >
                    &larr; Back to Tenants
                </button>
                <h1 className="text-2xl font-bold">Tenant Details</h1>
            </div>

            {successMessage && (
                <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <span className="block sm:inline">{successMessage}</span>
                </div>
            )}

            {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">Error: </strong>
                    <span className="block sm:inline">{error}</span>
                </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {/* Tenant Information */}
                <div className="col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h2 className="text-xl font-semibold mb-4">Tenant Information</h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p className="text-gray-600 dark:text-gray-400 mb-2">
                                <span className="font-medium">Name:</span> {tenant.companyName}
                            </p>
                            <p className="text-gray-600 dark:text-gray-400 mb-2">
                                <span className="font-medium">Status:</span>{' '}
                                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${tenant.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                    {tenant.isActive ? 'Active' : 'Inactive'}
                                </span>
                            </p>
                            <p className="text-gray-600 dark:text-gray-400 mb-2">
                                <span className="font-medium">Start Date:</span> {formatDate(tenant.subscription.startDate)}
                            </p>
                            <p className="text-gray-600 dark:text-gray-400 mb-2">
                                <span className="font-medium">End Date:</span> {formatDate(tenant.subscription.endDate)}
                            </p>
                            <p className={`mb-2 ${(tenant.subscription.daysRemaining < 7) ? 'text-red-600 font-medium' : 'text-gray-600 dark:text-gray-400'}`}>
                                <span className="font-medium">Days Remaining:</span> {tenant.subscription.daysRemaining}
                            </p>
                        </div>
                    </div>
                </div>

                {/* User Information */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h2 className="text-xl font-semibold mb-4">Admin User</h2>

                    {user ? (
                        <>
                            <p className="text-gray-600 dark:text-gray-400 mb-2">
                                <span className="font-medium">Name:</span> {user.fullName}
                            </p>
                            <p className="text-gray-600 dark:text-gray-400 mb-2">
                                <span className="font-medium">Username:</span> {user.username}
                            </p>
                            <p className="text-gray-600 dark:text-gray-400 mb-2">
                                <span className="font-medium">Email:</span> {user.email}
                            </p>
                        </>
                    ) : (
                        <p className="text-gray-600 dark:text-gray-400">No user information available</p>
                    )}
                </div>
            </div>

            {/* Actions */}
            <div className="mt-8 flex flex-wrap gap-4">
                <button
                    onClick={() => router.push(`/dashboard/tenants/${user?.id}/subscription`)}
                    className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 cursor-pointer"
                >
                    Manage Subscription
                </button>

                <button
                    onClick={handleActivateDeactivate}
                    className={`${tenant.isActive ? 'bg-amber-500 hover:bg-amber-600 cursor-pointer' : 'bg-green-500 hover:bg-green-600'} text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-opacity-50 cursor-pointer`}
                >
                    {tenant.isActive ? 'Deactivate Tenant' : 'Activate Tenant'}
                </button>

                <button
                    onClick={handleDeleteTenant}
                    className="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 cursor-pointer"
                >
                    Delete Tenant
                </button>
            </div>
        </div>
    );
}