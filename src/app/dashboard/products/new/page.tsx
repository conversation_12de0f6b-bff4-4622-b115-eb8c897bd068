'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Search } from 'lucide-react';
import { useSession } from 'next-auth/react';

const productSchema = z.object({
    name: z.string().min(1, { message: 'Product name is required' }),
    code: z.string().optional().nullable(),
    description: z.string().optional().nullable(),
    costPrice: z.coerce.number().min(0, { message: 'Cost price must be a positive number' }),
    sellingPrice: z.coerce.number().min(1, { message: 'MRP Price required and must be a positive number' }),
    discount: z.coerce.number().min(0).max(100).default(0),
    categoryId: z.string().optional().nullable(),
    vendorId: z.string().optional().nullable(),
    unit: z.string().min(1, { message: 'Unit is required' }),
    royaltyType: z.enum(['none', 'fixed', 'percentage']).default('none'),
    royaltyValue: z.coerce.number().min(0).default(0),
    sku: z.string().optional().nullable(),
    weight: z.coerce.number().min(0).optional().nullable(),
});

type ProductFormValues = z.infer<typeof productSchema>;

export default function NewProductPage() {
    const router = useRouter();
    const { data: session } = useSession();
    const vendorDropdownRef = useRef<HTMLDivElement>(null);
    const [categories, setCategories] = useState<{ id: string; name: string; isActive?: boolean }[]>([]);
    const [vendors, setVendors] = useState<{ id: string; name: string; vendorCode?: string; userId?: string; isActive?: boolean; user?: { fullName?: string } }[]>([]);
    const [vendorSearch, setVendorSearch] = useState('');
    const [selectedVendorId, setSelectedVendorId] = useState<string | null>(null);
    const [filteredVendors, setFilteredVendors] = useState<{ id: string; name: string; vendorCode?: string; userId?: string; isActive?: boolean; user?: { fullName?: string } }[]>([]);
    const [showVendorDropdown, setShowVendorDropdown] = useState(false);
    const [loading, setLoading] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [userPermissions, setUserPermissions] = useState<{
        canView: boolean;
        canCreate: boolean;
        canEdit: boolean;
        canDelete: boolean;
    }>({ canView: false, canCreate: false, canEdit: false, canDelete: false });

    const {
        register,
        handleSubmit,
        formState: { errors },
        reset,
    } = useForm<ProductFormValues>({
        resolver: zodResolver(productSchema),
        defaultValues: {
            name: '',
            code: '',
            description: '',
            costPrice: 0,
            sellingPrice: 0,
            discount: 0,
            categoryId: '',
            vendorId: '',
            unit: '',
            royaltyType: 'none',
            royaltyValue: 0,
            sku: null, // Adjusted default value for SKU
            weight: null, // Adjusted default value for weight
        },
    });

    useEffect(() => {
        fetchCategories();
        fetchVendors();

        // Add click outside listener to close vendor dropdown
        const handleClickOutside = (event: MouseEvent) => {
            if (vendorDropdownRef.current && !vendorDropdownRef.current.contains(event.target as Node)) {
                setShowVendorDropdown(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Check user permissions when session is loaded
    useEffect(() => {
        if (session) {
            fetchUserPermissions();
        }
    }, [session]);

    // Fetch user permissions
    const fetchUserPermissions = async () => {
        try {
            if (!session) {
                return; // Wait for session to be loaded
            }

            if (session?.user?.role === 'admin' || session?.user?.role === 'tenant') {
                // Admin and tenant users have full permissions
                setUserPermissions({
                    canView: true,
                    canCreate: true,
                    canEdit: true,
                    canDelete: true
                });
                return;
            }

            // For tenant_sale users, fetch their specific permissions from the API
            const response = await fetch(`/api/user/permissions?menuPath=/dashboard/products`);
            const data = await response.json();

            if (data.success) {
                setUserPermissions(data.permissions);

                // If user doesn't have create permission, redirect to dashboard
                if (!data.permissions.canCreate) {
                    router.push('/dashboard');
                }
            } else {
                console.error('Error fetching permissions:', data.error);
                router.push('/dashboard');
            }
        } catch (error) {
            console.error('Error fetching user permissions:', error);
            router.push('/dashboard');
        }
    };

    useEffect(() => {
        if (vendorSearch.trim() === '') {
            // Sort vendors: admin vendors first, then tenant vendors
            const sortedVendors = [...vendors].sort((a, b) => {
                // Admin vendors (with userId) come first
                if (a.userId && !b.userId) return -1;
                if (!a.userId && b.userId) return 1;
                // Then sort by name
                return a.name.localeCompare(b.name);
            });
            setFilteredVendors(sortedVendors);
        } else {
            const searchTerm = vendorSearch.toLowerCase();

            // If we have a selected vendor ID, prioritize showing that vendor
            if (selectedVendorId) {
                const selectedVendor = vendors.find(v => v.id === selectedVendorId);
                if (selectedVendor) {
                    // Check if the search term matches the selected vendor
                    const vendorNameMatches = selectedVendor.name.toLowerCase().includes(searchTerm);
                    const vendorCodeMatches = selectedVendor.vendorCode && selectedVendor.vendorCode.toLowerCase().includes(searchTerm);

                    if (vendorNameMatches || vendorCodeMatches || searchTerm.includes(selectedVendor.vendorCode || '')) {
                        console.log('Showing selected vendor in dropdown:', selectedVendor.name);
                        setFilteredVendors([selectedVendor]);
                        return;
                    }
                }
            }

            // Check if the search term is a selected vendor (contains parentheses with code)
            const codeMatch = searchTerm.match(/\(([^)]+)\)/); // Extract code from parentheses
            if (codeMatch) {
                const vendorCode = codeMatch[1].trim();
                console.log('Detected vendor code in search:', vendorCode);

                // Find the vendor with this code
                const matchingVendor = vendors.find(v => v.vendorCode === vendorCode);
                if (matchingVendor) {
                    console.log('Found matching vendor:', matchingVendor.name);
                    // Show only this vendor in the dropdown
                    setFilteredVendors([matchingVendor]);
                    // Update the selected vendor ID
                    setSelectedVendorId(matchingVendor.id);
                } else {
                    // If no match (unlikely), show all vendors
                    console.log('No vendor found with code:', vendorCode);
                    setFilteredVendors(vendors);
                }
                return;
            }

            const filtered = vendors.filter(vendor => {
                // Search by name
                if (vendor.name.toLowerCase().includes(searchTerm)) {
                    return true;
                }

                // Search by vendor code
                if (vendor.vendorCode && vendor.vendorCode.toLowerCase().includes(searchTerm)) {
                    return true;
                }

                // Search by user fullName if it exists
                if (vendor.user?.fullName && vendor.user.fullName.toLowerCase().includes(searchTerm)) {
                    return true;
                }

                // Search by admin status
                if (vendor.userId && 'admin'.includes(searchTerm)) {
                    return true;
                }

                // Search by tenant status
                if (!vendor.userId && 'tenant'.includes(searchTerm)) {
                    return true;
                }

                return false;
            });

            // Sort filtered vendors: admin vendors first, then tenant vendors
            const sortedFiltered = [...filtered].sort((a, b) => {
                // Admin vendors (with userId) come first
                if (a.userId && !b.userId) return -1;
                if (!a.userId && b.userId) return 1;
                // Then sort by name
                return a.name.localeCompare(b.name);
            });

            setFilteredVendors(sortedFiltered);
        }
    }, [vendorSearch, vendors]);

    const fetchCategories = async () => {
        try {
            setLoading(true);
            const response = await fetch('/api/products/categories');

            if (!response.ok) {
                throw new Error('Failed to fetch categories');
            }

            const data = await response.json();

            if (data.success) {
                setCategories(data.categories);
            } else {
                setError(data.error || 'Failed to load categories');
            }
        } catch (err) {
            setError('An error occurred while fetching categories');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    const fetchVendors = async () => {
        try {
            setLoading(true);
            // Use the new API endpoint that fetches both tenant vendors and admin vendors
            const response = await fetch('/api/products/vendors');

            if (!response.ok) {
                throw new Error('Failed to fetch vendors');
            }

            const data = await response.json();

            if (data.success) {
                console.log('Vendors loaded:', data.vendors.length);
                // Log some sample vendor data to help with debugging
                if (data.vendors.length > 0) {
                    console.log('Sample vendor:', {
                        id: data.vendors[0].id,
                        name: data.vendors[0].name,
                        code: data.vendors[0].vendorCode,
                        userId: data.vendors[0].userId
                    });
                }
                setVendors(data.vendors);
                setFilteredVendors(data.vendors);
            } else {
                setError(data.error || 'Failed to load vendors');
            }
        } catch (err) {
            setError('An error occurred while fetching vendors');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    const onSubmit = async (data: ProductFormValues) => {
        try {
            setSubmitting(true);
            setError(null);

            const response = await fetch('/api/products', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Failed to add product');
            }

            if (result.success) {
                router.push('/dashboard/products');
                router.refresh();
            } else {
                setError(result.error || 'Failed to add product');
            }
        } catch (err: any) {
            setError(err.message || 'An error occurred while adding product');
            console.error(err);
        } finally {
            setSubmitting(false);
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-2 py-2">
            <div className="flex justify-between items-center mb-2">
                <h1 className="text-xl font-bold">Add New Product</h1>
                <button
                    onClick={() => router.push('/dashboard/products')}
                    className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 cursor-pointer"
                >
                    Back to Products
                </button>
            </div>

            {error && (
                <div className="bg-red-50 border-l-2 border-red-500 text-red-700 px-2 py-1 text-sm  rounded-sm mb-2" role="alert">
                    <span className="inline">{error}</span>
                </div>
            )}

            <div className="bg-white dark:bg-gray-800 rounded border border-gray-100 p-3">
                <form onSubmit={handleSubmit(onSubmit)}>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <div className="col-span-3">
                            <label htmlFor="name" className="block text-sm  font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Product Name *
                            </label>
                            <input
                                type="text"
                                id="name"
                                {...register('name')}
                                className="w-full px-2 py-1 text-sm border border-gray-200 rounded focus:outline-none focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                placeholder="Enter product name"
                            />
                            {errors.name && (
                                <p className="mt-0.5 text-sm  text-red-600 dark:text-red-400">{errors.name.message}</p>
                            )}
                        </div>

                        <div>
                            <label htmlFor="code" className="block text-sm  font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Product Code
                            </label>
                            <input
                                type="text"
                                id="code"
                                {...register('code')}
                                className="w-full px-2 py-1 text-sm border border-gray-200 rounded focus:outline-none focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                placeholder="Enter code (optional)"
                            />
                            {errors.code && (
                                <p className="mt-0.5 text-sm  text-red-600 dark:text-red-400">{errors.code.message}</p>
                            )}
                        </div>

                        <div>
                            <label htmlFor="vendorId" className="block text-sm  font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Vendor (Optional)
                            </label>
                            <div className="relative" ref={vendorDropdownRef}>
                                <div className="flex items-center w-full px-2 py-1 text-sm border border-gray-200 rounded focus-within:outline-none focus-within:border-blue-500 dark:bg-gray-700 dark:text-white">
                                    <Search className="h-3 w-3 text-gray-500 dark:text-gray-400 mr-1" />
                                    <input
                                        type="text"
                                        placeholder="Search vendor"
                                        value={vendorSearch}
                                        onChange={(e) => {
                                            setVendorSearch(e.target.value);
                                            // Always show dropdown when typing
                                            setShowVendorDropdown(true);
                                        }}
                                        onFocus={() => setShowVendorDropdown(true)}
                                        className="w-full outline-none bg-transparent text-sm"
                                    />
                                </div>

                                {showVendorDropdown && (
                                    <div className="absolute z-10 w-full mt-0.5 bg-white dark:bg-gray-800 border border-gray-200 rounded shadow-sm max-h-40 overflow-y-auto text-sm ">
                                        {filteredVendors.length > 0 ? (
                                            filteredVendors.map((vendor) => (
                                                vendor.isActive === true &&
                                                <div
                                                    key={vendor.id}
                                                    className="px-2 py-1 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer flex justify-between"
                                                    onClick={() => {
                                                        // Set the value in the form
                                                        const event = {
                                                            target: { name: 'vendorId', value: vendor.id }
                                                        };
                                                        register('vendorId').onChange(event as any);
                                                        setSelectedVendorId(vendor.id);

                                                        // Update the search field to show the selected vendor
                                                        let displayText = vendor.isActive === true ? vendor.name : "";
                                                        if (vendor.vendorCode) {
                                                            displayText += ` (${vendor.vendorCode})`;
                                                        }
                                                        setVendorSearch(displayText);
                                                        // Store the selected vendor ID and close dropdown
                                                        setShowVendorDropdown(false);
                                                    }}
                                                >
                                                    <div className="flex flex-col">
                                                        <span className="font-medium">{vendor.isActive === true && vendor.name}</span>
                                                    </div>
                                                    <div className="flex flex-col items-end">
                                                        {vendor.vendorCode && <span className="text-gray-500 dark:text-gray-400 text-sm ">{vendor.vendorCode}</span>}
                                                    </div>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="px-2 py-1 text-gray-500 dark:text-gray-400 text-sm ">No vendors found</div>
                                        )}
                                    </div>
                                )}
                            </div>
                            <input type="hidden" {...register('vendorId')} />
                            {errors.vendorId && (
                                <p className="mt-0.5 text-sm  text-red-600 dark:text-red-400">{errors.vendorId.message}</p>
                            )}
                        </div>

                        <div>
                            <label htmlFor="categoryId" className="block text-sm  font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Category (Optional)
                            </label>
                            <select
                                id="categoryId"
                                {...register('categoryId')}
                                className="w-full px-2 py-1 text-sm border border-gray-200 rounded focus:outline-none focus:border-blue-500 dark:bg-gray-700 dark:text-white cursor-pointer"
                            >
                                <option value="">Select category</option>
                                {categories.map((category) => (
                                    category.isActive === true &&
                                    <option key={category.id} value={category.id}>
                                        {category.name}
                                    </option>
                                ))}
                            </select>
                            {errors.categoryId && (
                                <p className="mt-0.5 text-sm  text-red-600 dark:text-red-400">{errors.categoryId.message}</p>
                            )}
                        </div>

                        <div>
                            <label htmlFor="unit" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Unit *
                            </label>
                            {/* <input
                                type="text"
                                id="unit"
                                {...register('unit')}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                placeholder="Enter unit of measurement"
                            /> */}
                            <select
                                id="unit"
                                {...register('unit')}
                                className="w-full px-3 py-1 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                            >
                                <option value="">Select Unit</option>
                                <option value="pcs">pcs</option>
                                <option value="ml">ml</option>
                                <option value="g">g</option>

                            </select>
                            {errors.unit && (
                                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.unit.message}</p>
                            )}
                        </div>



                        <div>
                            <label htmlFor="costPrice" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Cost Price *
                            </label>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span className="text-gray-500 dark:text-gray-400 text-2xl">৳</span>
                                </div>
                                <input
                                    type="number"
                                    id="costPrice"
                                    step="0.01"
                                    min="0"
                                    {...register('costPrice')}
                                    className="w-full pl-7 px-3 py-1 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                    placeholder="0.00"
                                />
                            </div>
                            {errors.costPrice && (
                                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.costPrice.message}</p>
                            )}
                        </div>

                        <div>
                            <label htmlFor="sellingPrice" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                MRP Price *
                            </label>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span className="text-gray-500 dark:text-gray-400 text-2xl">৳</span>
                                </div>
                                <input
                                    type="number"
                                    id="sellingPrice"
                                    step="0.01"
                                    min="0"
                                    {...register('sellingPrice')}
                                    className="w-full pl-7 px-3 py-1 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                    placeholder="0.00"
                                />
                            </div>
                            {errors.sellingPrice && (
                                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.sellingPrice.message}</p>
                            )}
                        </div>
                        <div className="">
                            <label htmlFor="discount" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Discount %
                            </label>
                            <input
                                id="discount"
                                type="number"
                                min="0"
                                max="100"
                                {...register('discount')}
                                className='w-full px-3 py-1 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white'
                            />
                            {errors.discount && (
                                <p className="text-sm text-red-500">{errors.discount.message}</p>
                            )}
                        </div>

                        <div>
                            <label htmlFor="sku" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                SKU
                            </label>
                            <input
                                type="text"
                                id="sku"
                                {...register('sku')}
                                className="w-full px-3 py-1 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                placeholder="Enter SKU"
                            />
                            {errors.sku && (
                                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.sku.message}</p>
                            )}
                        </div>

                        <div>
                            <label htmlFor="weight" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Weight
                            </label>
                            <input
                                type="number"
                                id="weight"
                                step="0.01"
                                min="0"
                                {...register('weight')}
                                className="w-full px-3 py-1 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                placeholder="Enter weight"
                            />
                            {errors.weight && (
                                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.weight.message}</p>
                            )}
                        </div>

                        <div>
                            <label htmlFor="royaltyType" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Royalty Type
                            </label>
                            <select
                                id="royaltyType"
                                {...register('royaltyType')}
                                className="w-full px-3 py-1 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                            >
                                <option value="none">None</option>
                                <option value="fixed">Fixed</option>
                                <option value="percentage">Percentage</option>
                            </select>
                        </div>

                        <div>
                            <label htmlFor="royaltyValue" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Royalty Value
                            </label>
                            <input
                                type="number"
                                id="royaltyValue"
                                step="0.01"
                                min="0"
                                {...register('royaltyValue')}
                                className="w-full px-3 py-1 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                placeholder="0.00"
                            />
                        </div>

                        <div className="col-span-2">
                            <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Description
                            </label>
                            <textarea
                                id="description"
                                rows={4}
                                {...register('description')}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                placeholder="Enter product description (optional)"
                            ></textarea>
                            {errors.description && (
                                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.description.message}</p>
                            )}
                        </div>
                    </div>

                    <div className="flex justify-end mt-6">
                        <button
                            type="button"
                            onClick={() => {
                                reset();
                                setVendorSearch('');
                                setSelectedVendorId(null);
                            }}
                            className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 mr-4 cursor-pointer"
                            disabled={submitting}
                        >
                            Reset
                        </button>
                        <button
                            type="submit"
                            className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 cursor-pointer"
                            disabled={submitting}
                        >
                            {submitting ? 'Adding...' : 'Add Product'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}
