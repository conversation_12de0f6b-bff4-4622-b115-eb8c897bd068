'use client';

import { useEffect } from 'react';
import { usePermissionCheck } from '@/lib/check-permissions';
import { CustomerForm } from '../../../new/_components/customer-form';

interface ClientWrapperProps {
  tenantId: string;
  customerId: string;
  defaultValues: {
    name: string;
    code: string;
    phone: string;
    email: string;
    address: string;
    type: "retail" | "wholesale";
    creditLimit: number;
    extraCommission: number;
    notes: string;
  };
}

export default function ClientWrapper({ tenantId, customerId, defaultValues }: ClientWrapperProps) {
  // Check if user has permission to edit customers
  const { isLoading } = usePermissionCheck('/dashboard/customers', 'edit');

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <CustomerForm
      tenantId={tenantId}
      customerId={customerId}
      defaultValues={defaultValues}
    />
  );
}
