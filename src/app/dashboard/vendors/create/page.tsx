'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, FormProvider } from 'react-hook-form';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';

// Define schema for form validation
const formSchema = z.object({
  name: z.string().min(2, { message: 'Vendor name must be at least 2 characters' }),
  userId: z.string().optional().nullable(), // Make userId optional
  vendorCode: z.string().optional(), // New field for vendor code
  email: z.string().email({ message: 'Please enter a valid email address' }).optional().or(z.literal('')),
  phone: z.string().optional().or(z.literal('')),
  address: z.string().optional().or(z.literal('')),
  isActive: z.boolean().default(true),
});

type FormValues = z.infer<typeof formSchema>;

interface User {
  id: string;
  fullName: string;
  username: string;
  email: string;
  role: string;
}

// Define a type for the field props to fix the TypeScript errors
interface FieldProps {
  field: {
    value: any;
    onChange: (value: any) => void;
    onBlur: () => void;
    name: string;
    ref: React.Ref<any>;
  };
}

export default function CreateVendorPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [vendorUsers, setVendorUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [userRole, setUserRole] = useState<string | null>(null);

  // Initialize the form
  const formMethods = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      userId: '', // Changed from null to empty string to avoid React warnings
      vendorCode: '', // Default value for vendor code - will be auto-generated
      email: '',
      phone: '',
      address: '',
      isActive: true,
    },
  });

  // Fetch users with vendor role
  const fetchVendorUsers = async () => {
    try {
      const response = await fetch('/api/users?role=vendor');

      if (!response.ok) {
        throw new Error('Failed to fetch vendor users');
      }

      const data = await response.json();

      if (data.success) {
        setVendorUsers(data.users);
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to load vendor users',
          variant: 'destructive',
        });
      }
    } catch (err) {
      toast({
        title: 'Error',
        description: 'An error occurred while fetching vendor users',
        variant: 'destructive',
      });
      console.error(err);
    }
  };

  // Get current user role and ID
  const fetchUserRole = async () => {
    try {
      const response = await fetch('/api/auth/me');
      if (response.ok) {
        const data = await response.json();
        setUserRole(data.user?.role || null);

        // For admin users, we need to validate userId field
        if (data.user?.role === 'admin') {
          // Update form validation for admin users
          formMethods.trigger('userId');
        }
      }
    } catch (error) {
      console.error('Error fetching user role:', error);
    }
  };

  useEffect(() => {
    fetchUserRole();
    fetchVendorUsers();
  }, []);

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    setLoading(true);

    try {
      // For admin users, userId is required
      if (userRole === 'admin' && !values.userId) {
        toast({
          title: 'Error',
          description: 'Please select a user to associate with this vendor',
          variant: 'destructive',
        });
        setLoading(false);
        return;
      }

      // Include email and phone in the log
      console.log('Submitting vendor data:', values);

      const response = await fetch('/api/vendors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();
      console.log('Response:', data);

      if (data.success) {
        toast({
          title: 'Success',
          description: 'Vendor created successfully',
        });
        router.push('/dashboard/vendors');
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to create vendor',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error creating vendor:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while creating the vendor',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Create New Vendor</h1>
        <Button
          variant="outline"
          onClick={() => router.push('/dashboard/vendors')}
          className="bg-blue-600 hover:bg-blue-700 text-white hover:text-white cursor-pointer"
        >
          Back to Vendors
        </Button>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <FormProvider {...formMethods}>
          <form onSubmit={formMethods.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={formMethods.control}
                name="name"
                render={({ field }: FieldProps) => (
                  <FormItem>
                    <FormLabel>Vendor Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter vendor name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={formMethods.control}
                name="vendorCode"
                render={({ field }: FieldProps) => (
                  <FormItem>
                    <FormLabel>Vendor Code</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Will be auto-generated"
                        {...field}
                        disabled={true}
                      />
                    </FormControl>
                    <FormDescription>
                      A unique code will be automatically generated
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {userRole === 'admin' && (
              <FormField
                control={formMethods.control}
                name="userId"
                render={({ field }: FieldProps) => (
                  <FormItem>
                    <FormLabel>Associated User</FormLabel>
                    <FormControl>
                      <select
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        value={field.value}
                        onChange={field.onChange}
                        required={userRole === 'admin'}
                      >
                        <option value="">Select a user with vendor role</option>
                        {vendorUsers.map((user) => (
                          <option key={user.id} value={user.id}>
                            {user.fullName} ({user.email})
                          </option>
                        ))}
                      </select>
                    </FormControl>
                    <FormDescription>
                      This user will be associated with the vendor.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {userRole === 'admin' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={formMethods.control}
                  name="email"
                  render={({ field }: FieldProps) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter vendor email" type="email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={formMethods.control}
                  name="phone"
                  render={({ field }: FieldProps) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter vendor phone number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            <FormField
              control={formMethods.control}
              name="address"
              render={({ field }: FieldProps) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Enter vendor address" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={formMethods.control}
              name="isActive"
              render={({ field }: FieldProps) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Active</FormLabel>
                    <FormDescription>
                      Set the vendor as active or inactive.
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                A unique vendor code will be automatically generated for this vendor with the format VN0000.
              </p>
            </div>

            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 text-white cursor-pointer"
              >
                {loading ? 'Creating...' : 'Create Vendor'}
              </Button>
            </div>
          </form>
        </FormProvider>
      </div>
    </div>
  );
}