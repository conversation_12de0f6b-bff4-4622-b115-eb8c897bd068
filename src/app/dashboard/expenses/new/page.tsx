'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { toast } from 'sonner';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { usePaymentMethods, getDefaultPaymentMethod } from '@/hooks/usePaymentMethods';

// Define schema for form validation
const expenseSchema = z.object({
  categoryId: z.string().min(1, { message: 'Category is required' }),
  branchId: z.string().min(1, { message: 'Branch is required' }),
  amount: z.coerce.number().positive({ message: 'Amount must be greater than 0' }),
  date: z.string().min(1, { message: 'Date is required' }),
  description: z.string().optional(),
  paymentMethod: z.string().min(1, { message: 'Payment method is required' }),
  reference: z.string().optional(),
});

type ExpenseFormValues = z.infer<typeof expenseSchema>;

interface ExpenseCategory {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
}

interface Branch {
  id: string;
  name: string;
  code: string;
  isMain: boolean;
  isActive: boolean;
}

export default function NewExpensePage() {
  const router = useRouter();
  const [categories, setCategories] = useState<ExpenseCategory[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategoryName, setSelectedCategoryName] = useState<string>("");
  const [selectedBranchName, setSelectedBranchName] = useState<string>("");

  // Fetch payment methods
  const { paymentMethods, isLoading: isLoadingPaymentMethods } = usePaymentMethods();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<ExpenseFormValues>({
    resolver: zodResolver(expenseSchema),
    defaultValues: {
      categoryId: '',
      branchId: '',
      amount: 0,
      date: new Date().toISOString().split('T')[0],
      description: '',
      paymentMethod: '',
      reference: '',
    },
  });

  // Set default payment method when payment methods are loaded
  useEffect(() => {
    if (paymentMethods.length > 0 && !watch('paymentMethod')) {
      const defaultMethod = getDefaultPaymentMethod(paymentMethods);
      if (defaultMethod) {
        setValue('paymentMethod', defaultMethod.code);
      }
    }
  }, [paymentMethods, setValue, watch]);

  useEffect(() => {
    fetchCategories();
    fetchBranches();
  }, []);

  // Update selected names when form values change
  const watchedCategoryId = watch('categoryId');
  const watchedBranchId = watch('branchId');

  useEffect(() => {
    if (watchedCategoryId && categories.length > 0) {
      const category = categories.find(cat => cat.id === watchedCategoryId);
      if (category) {
        setSelectedCategoryName(category.name);
      }
    }
  }, [watchedCategoryId, categories]);

  useEffect(() => {
    if (watchedBranchId && branches.length > 0) {
      const branch = branches.find(b => b.id === watchedBranchId);
      if (branch) {
        setSelectedBranchName(branch.code ? `[${branch.code}] ${branch.name}` : branch.name);
      }
    }
  }, [watchedBranchId, branches]);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/expense-categories');

      if (!response.ok) {
        throw new Error(`Failed to fetch categories: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        // Filter only active categories
        const activeCategories = data.categories.filter((cat: ExpenseCategory) => cat.isActive);
        setCategories(activeCategories || []);
      } else {
        setError(data.error || 'Failed to load expense categories');
      }
    } catch (error) {
      console.error('Error fetching expense categories:', error);
      setError('An error occurred while fetching expense categories');
    }
  };

  const fetchBranches = async () => {
    try {
      const response = await fetch('/api/branches');

      if (!response.ok) {
        throw new Error(`Failed to fetch branches: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        // Filter only active branches
        const activeBranches = data.branches.filter((branch: Branch) => branch.isActive);
        setBranches(activeBranches || []);

        // If there's only one branch, select it automatically
        if (activeBranches.length === 1) {
          setValue('branchId', activeBranches[0].id);
          setSelectedBranchName(activeBranches[0].code ?
            `[${activeBranches[0].code}] ${activeBranches[0].name}` :
            activeBranches[0].name);
        }
      } else {
        setError(data.error || 'Failed to load branches');
      }
    } catch (error) {
      console.error('Error fetching branches:', error);
      setError('An error occurred while fetching branches');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: ExpenseFormValues) => {
    try {
      setSubmitting(true);

      const response = await fetch('/api/expenses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Expense recorded successfully');
        router.push('/dashboard/expenses');
      } else {
        toast.error(result.error || 'Failed to record expense');
      }
    } catch (error) {
      console.error('Error recording expense:', error);
      toast.error('An error occurred while recording the expense');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Record New Expense</h1>
        <Button
          onClick={() => router.push('/dashboard/expenses')}
          variant="outline"
          className='cursor-pointer'
        >
          Back to Expenses
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Expense Details</CardTitle>
          <CardDescription>
            Enter the details of the expense you want to record
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="categoryId">Expense Category</Label>
                <Select
                  onValueChange={(value) => {
                    setValue('categoryId', value);
                    const category = categories.find(cat => cat.id === value);
                    if (category) {
                      setSelectedCategoryName(category.name);
                    }
                  }}
                  defaultValue={watch('categoryId')}
                >
                  <SelectTrigger id="categoryId">
                    <SelectValue placeholder="Select a category">
                      {selectedCategoryName || "Select a category"}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {categories.length === 0 ? (
                      <SelectItem value="" data-disabled={true}>
                        No categories available
                      </SelectItem>
                    ) : (
                      categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {errors.categoryId && (
                  <p className="text-red-500 text-sm">{errors.categoryId.message}</p>
                )}
                {categories.length === 0 && (
                  <p className="text-amber-500 text-sm">
                    No expense categories found. <Link href="/dashboard/expenses/categories" className="underline">Create one first</Link>
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="branchId">Branch</Label>
                <Select
                  onValueChange={(value) => {
                    setValue('branchId', value);
                    const branch = branches.find(b => b.id === value);
                    if (branch) {
                      setSelectedBranchName(branch.code ? `[${branch.code}] ${branch.name}` : branch.name);
                    }
                  }}
                  defaultValue={watch('branchId')}
                >
                  <SelectTrigger id="branchId">
                    <SelectValue placeholder="Select a branch">
                      {selectedBranchName || "Select a branch"}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {branches.length === 0 ? (
                      <SelectItem value="" data-disabled={true}>
                        No branches available
                      </SelectItem>
                    ) : (
                      branches.map((branch) => (
                        <SelectItem key={branch.id} value={branch.id}>
                          {branch.code ? `[${branch.code}] ${branch.name}` : branch.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {errors.branchId && (
                  <p className="text-red-500 text-sm">{errors.branchId.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="amount">Amount</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  min="0.01"
                  placeholder="0.00"
                  {...register('amount')}
                />
                {errors.amount && (
                  <p className="text-red-500 text-sm">{errors.amount.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  {...register('date')}
                  className="h-9 px-3 py-2 bg-white border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                {errors.date && (
                  <p className="text-red-500 text-sm">{errors.date.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="paymentMethod">Payment Method</Label>
                <Select
                  onValueChange={(value) => setValue('paymentMethod', value)}
                  defaultValue={watch('paymentMethod')}
                >
                  <SelectTrigger id="paymentMethod">
                    <SelectValue placeholder={isLoadingPaymentMethods ? "Loading..." : "Select payment method"} />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingPaymentMethods ? (
                      <SelectItem value="" disabled>Loading payment methods...</SelectItem>
                    ) : paymentMethods.length > 0 ? (
                      paymentMethods.map((method) => (
                        <SelectItem key={method.id} value={method.code}>
                          {method.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="" disabled>No payment methods available</SelectItem>
                    )}
                  </SelectContent>
                </Select>
                {errors.paymentMethod && (
                  <p className="text-red-500 text-sm">{errors.paymentMethod.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="reference">Reference (Optional)</Label>
                <Input
                  id="reference"
                  placeholder="e.g., Invoice #, Receipt #"
                  {...register('reference')}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                placeholder="Add details about this expense"
                rows={3}
                {...register('description')}
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-between pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/dashboard/expenses')}
              disabled={submitting}
              className="cursor-pointer border-gray-300 hover:bg-gray-100 transition-colors duration-200 py-2 px-4"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={submitting || categories.length === 0}
              className="bg-blue-600 hover:bg-blue-700 text-white cursor-pointer shadow-md hover:shadow-lg transition-all duration-200 py-2 px-4"
            >
              {submitting ? 'Recording...' : 'Record Expense'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
