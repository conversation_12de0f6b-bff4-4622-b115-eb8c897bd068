'use client';

import React, { useEffect, useState } from 'react';
import { numberToBanglaWords } from '@/lib/bangla-utils';

// Define display type options
type DisplayType = 'text' | 'component';

// Define props interface
interface BanglaNumberConverterProps {
  numb: number | string;
  displayType?: DisplayType;
  showCurrency?: boolean;
}

/**
 * Converts numeric values to Bengali words
 * @param numb - The number to convert to Bengali words
 * @param displayType - How to display the result ('text' or 'component')
 * @param showCurrency - Whether to append "টাকা মাত্র" after the number
 * @returns React component or text with Bengali words
 */
const BanglaNumberConverter: React.FC<BanglaNumberConverterProps> = ({
  numb,
  displayType = 'text',
  showCurrency = false
}: BanglaNumberConverterProps): React.ReactElement => {
  // Use state to handle client-side rendering
  const [banglaWord, setBanglaWord] = useState<string>('');

  // Process the number on the client side to avoid hydration issues
  useEffect(() => {
    // Convert input to number
    const numValue: number = typeof numb === 'string' ? parseFloat(numb) : numb;

    // Generate Bengali word representation
    let result = '';

    if (!isNaN(numValue)) {
      result = numberToBanglaWords(numValue);
      if (showCurrency) {
        result += ' টাকা মাত্র';
      }
    }

    setBanglaWord(result);
  }, [numb, showCurrency]);

  // For server-side rendering, return an empty placeholder
  // This ensures consistent rendering between server and client
  if (banglaWord === '') {
    return <span className="bangla-number-placeholder"></span>;
  }

  // Return as component or text based on displayType
  if (displayType === 'component') {
    return (
      <div style={{ fontFamily: 'sans-serif' }}>
        <h2 style={{ color: '#4CAF50' }}>
          {banglaWord}
        </h2>
      </div>
    );
  }

  return <span className="bangla-number">{banglaWord}</span>;
};

export default BanglaNumberConverter;
