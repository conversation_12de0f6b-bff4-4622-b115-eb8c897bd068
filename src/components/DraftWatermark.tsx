'use client';

import React from 'react';

interface DraftWatermarkProps {
  className?: string;
}

export default function DraftWatermark({ className = '' }: DraftWatermarkProps) {
  return (
    <div className={`absolute inset-0 flex items-center justify-center pointer-events-none ${className}`}>
      <div className="transform rotate-45 text-red-200 text-8xl font-bold opacity-30">
        DRAFT
      </div>
    </div>
  );
}
