'use client';

import { usePermissionCheck } from '@/lib/check-permissions';
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils";
import {
    Table,
    TableHeader,
    TableBody,
    TableCell,
    TableRow,
    TableHead,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight, Eye, Pencil, RefreshCw } from "lucide-react";
import { ToastHandler } from "./toast-handler";
import { toast } from "sonner";
import { SearchForm } from "./search-form";
import { BranchFilter } from "./branch-filter";
import DirectPrintButton from "./direct-print-button";
import { useEffect, useState, useCallback } from "react";

interface Branch {
    id: string;
    name: string;
    isMain?: boolean | null;
    code?: string;
    isActive?: boolean | null;
    email?: string | null;
    phone?: string | null;
    address?: string | null;
    tenantId?: string;
    notes?: string | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
}

interface Order {
    id: string;
    memoNo: string;
    date: Date;
    totalAmount: number | null;
    paidAmount: number | null;
    dueAmount: number | null;
    paymentStatus: string;
    paymentMethod: string;
    customerId: string | null;
    previousDue: number | null;
    status?: "pending" | "completed" | "cancelled" | "draft";
    customer: {
        name: string;
    } | null;
    branch: {
        name: string;
        code?: string;
        isMain?: boolean | null;
    } | null;
    performer?: {
        id?: string;
        fullName?: string;
        username?: string;
    } | null;
}

interface Pagination {
    totalCount: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
}

interface ClientWrapperProps {
    initialOrders: Order[];
    search: string;
    branches: Branch[];
    selectedBranchId: string | null | undefined;
    userRole: string;
    userBranch: Branch | null;
}

export default function ClientWrapper({
    initialOrders,
    search,
    branches,
    selectedBranchId,
    userRole,
    userBranch
}: ClientWrapperProps) {
    // Check if user has permission to view sales
    const { isLoading: permissionLoading } = usePermissionCheck('/dashboard/sales', 'view');
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [pageSize] = useState<number>(20);

    // State for orders and pagination
    const [orders, setOrders] = useState<Order[]>(initialOrders);
    const [pagination, setPagination] = useState<Pagination>({
        totalCount: initialOrders.length,
        totalPages: 1,
        currentPage: 1,
        pageSize: initialOrders.length
    });
    const [dataLoading, setDataLoading] = useState<boolean>(false);
    const [isConverting, setIsConverting] = useState<boolean>(false);

    // Build the API URL with query parameters
    const getApiUrl = useCallback(() => {
        // Check if window is defined (client-side only)
        if (typeof window === 'undefined') {
            // Return a placeholder during server-side rendering
            return '';
        }

        const url = new URL(`/api/sales/paginated`, window.location.origin);
        url.searchParams.append('page', currentPage.toString());
        url.searchParams.append('pageSize', pageSize.toString());

        if (search) {
            url.searchParams.append('search', search);
        }

        if (selectedBranchId) {
            url.searchParams.append('branchId', selectedBranchId);
        }

        return url.toString();
    }, [currentPage, pageSize, search, selectedBranchId]);

    // Function to fetch data
    const fetchData = useCallback(async () => {
        const apiUrl = getApiUrl();
        if (!apiUrl) return;

        setDataLoading(true);
        try {
            // Add a timestamp to the URL to prevent browser caching
            const cacheBustUrl = `${apiUrl}${apiUrl.includes('?') ? '&' : '?'}_t=${Date.now()}`;

            const response = await fetch(cacheBustUrl, {
                // Add cache: 'no-store' to prevent caching
                cache: 'no-store',
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                setOrders(result.orders);
                setPagination(result.pagination);
            }
        } catch (error) {
            console.error('Error fetching sales data:', error);
        } finally {
            setDataLoading(false);
        }
    }, [getApiUrl]);

    // Fetch data when dependencies change
    useEffect(() => {
        fetchData();
    }, [fetchData]);

    // Function to handle converting a draft order to a regular order
    const handleConvertDraft = async (orderId: string) => {
        if (isConverting) return;

        try {
            setIsConverting(true);

            // Call the API to convert the draft order
            const response = await fetch(`/api/sales/${orderId}/convert-draft`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || "Failed to convert draft order");
            }

            // Show success message
            toast.success("Draft order converted to regular order successfully");

            // Refresh the data
            fetchData();
        } catch (error: any) {
            console.error("Error converting draft order:", error);
            toast.error(error.message || "An error occurred while converting the draft order");
        } finally {
            setIsConverting(false);
        }
    };

    const isLoading = permissionLoading || dataLoading;

    // Fetch today's sales data
    // useEffect(() => {
    //     const fetchTodaySales = async () => {
    //         try {
    //             const response = await fetch('/api/sales/today-cash');
    //             if (response.ok) {
    //                 const data = await response.json();
    //                 if (data.success) {
    //                     setTodayTotalSales(data.totalSales || 0);
    //                 }
    //             }
    //         } catch (error) {
    //             console.error('Error fetching today sales:', error);
    //         }
    //     };

    //     fetchTodaySales();
    // }, []);



    if (isLoading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }


    // Calculate today's cash sales from the orders array
    let todayCashTotal = 0;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    orders.forEach((order) => {
        const orderDate = new Date(order.date);
        // Check if the order is from today and payment method is cash
        if (orderDate >= today && orderDate < tomorrow && order.paymentMethod === "cash") {
            todayCashTotal += order.paidAmount || 0;
        }
    });

    return (
        <>            {/* Toast handler for success/error messages */}
            <ToastHandler />

            <div className="flex justify-between items-center mb-6">
                <h1 className="text-3xl font-bold">Sales Management</h1>
                <Link href="/dashboard/sales/new">
                    <Button className="cursor-pointer">New Sale</Button>
                </Link>
            </div>

            <div className="flex items-center mb-4 gap-4">
                <div className="bg-green-50 text-green-700 px-4 py-2 rounded-md font-medium">
                    Today Only Cash Sale: {todayCashTotal === 0 ? '00.00' : todayCashTotal.toFixed(2)} tk
                </div>

            </div>

            <Card className="mb-4">
                <CardHeader className="pb-2 pt-4">
                    <div className="flex justify-between items-center">
                        <div>
                            <CardTitle className="text-lg">Search Sales</CardTitle>
                        </div>
                        {/* Show branch info for tenant_sale users */}
                        {userRole === 'tenant_sale' && userBranch && (
                            <Badge variant="outline" className="bg-blue-50 text-blue-700 whitespace-nowrap">
                                Branch: {userBranch.name}
                            </Badge>
                        )}
                    </div>
                </CardHeader>
                <CardContent className="pt-2 pb-4">
                    <div className="flex items-center gap-2">
                        <div className="flex-1">
                            <SearchForm defaultValue={search} />
                        </div>

                        {/* Only show branch filter for tenant users */}
                        {userRole === 'tenant' && (
                            <div className="w-64">
                                <BranchFilter
                                    branches={branches}
                                    selectedBranchId={selectedBranchId}
                                />
                            </div>
                        )}
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardHeader className="pb-2 pt-4">
                    <div className="flex justify-between items-center">
                        <CardTitle className="text-lg">Sales Transactions</CardTitle>
                        <CardDescription className="text-xs mt-0">
                            {search && selectedBranchId
                                ? `Results for "${search}" in selected branch`
                                : search
                                    ? `Results for "${search}"`
                                    : selectedBranchId
                                        ? `Filtered by branch`
                                        : "Recent transactions"}
                        </CardDescription>
                    </div>
                </CardHeader>
                <CardContent className="pt-2">
                    <div className="overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="py-2">Customer</TableHead>
                                    <TableHead className="py-2">Branch</TableHead>
                                    <TableHead className="py-2">Seller</TableHead>
                                    <TableHead className="py-2">Memo No</TableHead>
                                    <TableHead className="py-2">Date</TableHead>
                                    <TableHead className="text-right py-2">Amount</TableHead>
                                    <TableHead className="text-right py-2">Paid</TableHead>
                                    <TableHead className="text-right py-2">Due</TableHead>
                                    <TableHead className="text-center py-2">Status</TableHead>
                                    <TableHead className="text-center py-2">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {orders.length === 0 ? (
                                    <TableRow>
                                        <TableCell colSpan={9} className="text-center py-4">
                                            No sales found
                                        </TableCell>
                                    </TableRow>
                                ) : (
                                    orders.map((order) => {
                                        const date = new Date(order.date);
                                        const formattedDate = new Intl.DateTimeFormat('en-US', {
                                            year: 'numeric',
                                            month: 'short',
                                            day: 'numeric',
                                        }).format(date);

                                        return (
                                            <TableRow key={order.id} className="border-b hover:bg-gray-50">
                                                <TableCell className="py-2">
                                                    {order.customer ? (
                                                        <span className="flex items-center">
                                                            {order.customer.name}
                                                            {order.previousDue !== null && order.previousDue > 0 && (
                                                                <span className="ml-2 text-xs bg-amber-100 text-amber-800 px-1 py-0.5 rounded">
                                                                    Due: {formatCurrency(order.previousDue)}
                                                                </span>
                                                            )}
                                                        </span>
                                                    ) : "Walk-in"}
                                                </TableCell>
                                                <TableCell className="py-2">
                                                    {order.branch ? (
                                                        <span>
                                                            {order.branch.code && (
                                                                <span className="text-gray-500 mr-1">[{order.branch.code}]</span>
                                                            )}
                                                            {order.branch.name}
                                                            {order.branch.isMain && (
                                                                <span className="ml-1 text-xs bg-blue-100 text-blue-800 px-1 py-0.5 rounded">
                                                                    Main
                                                                </span>
                                                            )}
                                                        </span>
                                                    ) : "Unknown"}
                                                </TableCell>
                                                <TableCell className="py-2">
                                                    {order.performer?.fullName || "N/A"}
                                                </TableCell>
                                                <TableCell className="py-2">{order.memoNo}</TableCell>
                                                <TableCell className="py-2">{formattedDate}</TableCell>
                                                <TableCell className="text-right py-2">{formatCurrency(order.totalAmount || 0)}</TableCell>
                                                <TableCell className="text-right py-2">{formatCurrency(order.paidAmount || 0)}</TableCell>
                                                <TableCell className="text-right py-2">{formatCurrency(order.dueAmount || 0)}</TableCell>
                                                <TableCell className="text-center py-2">
                                                    {order.status === "draft" ? (
                                                        <Badge variant="outline" className="bg-purple-100 text-purple-800 hover:bg-purple-100 text-xs">
                                                            Draft
                                                        </Badge>
                                                    ) : (
                                                        <Badge variant={
                                                            order.paymentStatus === "paid" ? "default" :
                                                                order.paymentStatus === "partial" ? "outline" : "destructive"
                                                        } className={
                                                            order.paymentStatus === "paid" ? "bg-green-100 text-green-800 hover:bg-green-100 text-xs" :
                                                                order.paymentStatus === "partial" ? "bg-yellow-100 text-yellow-800 hover:bg-yellow-100 text-xs" : "text-xs"
                                                        }>
                                                            {order.paymentStatus === "paid" ? "Paid" :
                                                                order.paymentStatus === "partial" ? "Partial" : "Unpaid"}
                                                        </Badge>
                                                    )}
                                                </TableCell>
                                                <TableCell className="text-center py-2">
                                                    <div className="flex justify-center space-x-1">
                                                        <Link href={`/dashboard/sales/${order.id}`}>
                                                            <Button variant="outline" size="sm" className="cursor-pointer h-7 w-7 p-0">
                                                                <Eye className="h-3 w-3" />
                                                            </Button>
                                                        </Link>

                                                        {order.status !== "draft" && (
                                                            <DirectPrintButton id={order.id} />
                                                        )}

                                                        <Link href={`/dashboard/sales/${order.id}/edit`}>
                                                            <Button variant="outline" size="sm" className="cursor-pointer h-7 w-7 p-0">
                                                                <Pencil className="h-3 w-3" />
                                                            </Button>
                                                        </Link>

                                                        {order.status !== "draft" && order.paymentStatus !== "paid" && (
                                                            <Link href={`/dashboard/payments/new?customerId=${order.customerId}&orderId=${order.id}`}>
                                                                <Button variant="outline" size="sm" className="bg-green-50 text-green-600 cursor-pointer hover:bg-green-100 h-7 w-7 p-0">
                                                                    <span className="text-sm font-semibold">৳</span>
                                                                </Button>
                                                            </Link>
                                                        )}
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        );
                                    })
                                )}
                            </TableBody>
                        </Table>
                    </div>

                    {/* Pagination */}
                    {pagination.totalPages > 1 && (
                        <div className="flex items-center justify-between mt-4 px-2">
                            <div className="text-sm text-gray-500">
                                Showing {((pagination.currentPage - 1) * pagination.pageSize) + 1} to {Math.min(pagination.currentPage * pagination.pageSize, pagination.totalCount)} of {pagination.totalCount} entries
                            </div>
                            <div className="flex items-center space-x-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                                    disabled={pagination.currentPage === 1}
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                </Button>

                                {/* Page numbers */}
                                <div className="flex items-center space-x-1">
                                    {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                                        // Show pages around current page
                                        let pageNum;
                                        if (pagination.totalPages <= 5) {
                                            pageNum = i + 1;
                                        } else if (pagination.currentPage <= 3) {
                                            pageNum = i + 1;
                                        } else if (pagination.currentPage >= pagination.totalPages - 2) {
                                            pageNum = pagination.totalPages - 4 + i;
                                        } else {
                                            pageNum = pagination.currentPage - 2 + i;
                                        }

                                        return (
                                            <Button
                                                key={pageNum}
                                                variant={pagination.currentPage === pageNum ? "default" : "outline"}
                                                size="sm"
                                                onClick={() => setCurrentPage(pageNum)}
                                                className="w-8 h-8 p-0"
                                            >
                                                {pageNum}
                                            </Button>
                                        );
                                    })}
                                </div>

                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, pagination.totalPages))}
                                    disabled={pagination.currentPage === pagination.totalPages}
                                >
                                    <ChevronRight className="h-4 w-4" />
                                </Button>

                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => fetchData()}
                                    title="Refresh"
                                >
                                    <RefreshCw className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>
        </>
    );
}
