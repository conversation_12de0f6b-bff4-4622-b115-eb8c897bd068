import { Metadata } from "next";
import { redirect } from "next/navigation";
import { auth } from "../../../../../auth";
import { getTenantId } from "@/lib/auth-utils";
import { db } from "@/lib/db";
import { orders, branches } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import ClientWrapper from "./_components/client-wrapper";

// Add export for no caching
export const dynamic = 'force-dynamic';
export const revalidate = 0;

export const metadata: Metadata = {
    title: "Sale Details",
    description: "View details of a sale transaction",
};

interface SaleDetailPageProps {
    params: Promise<{ id: string }>;
}

export default async function SaleDetailPage({ params }: SaleDetailPageProps) {
    const session = await auth();
    if (!session?.user) {
        redirect("/api/auth/signin");
    }

    const tenantId = await getTenantId();
    if (!tenantId) {
        redirect("/dashboard");
    }

    const { id } = await params;
    if (!id) {
        redirect("/dashboard/sales");
    }

    // Get the order
    const order = await db.query.orders.findFirst({
        where: (orders, { eq, and }) =>
            and(
                eq(orders.id, id),
                eq(orders.tenantId, tenantId)
            ),
        with: {
            customer: true,
            branch: true, // Include branch information
            items: {
                with: {
                    product: {
                        with: {
                            category: true,
                            vendor: true
                        }
                    }
                }
            },
            performer: {
                columns: {
                    id: true,
                    fullName: true,
                    username: true
                }
            }
        }
    });

    if (!order) {
        redirect("/dashboard/sales");
    }

    return (
        <div className="container py-10">
            <ClientWrapper order={order} />
        </div>
    );
}