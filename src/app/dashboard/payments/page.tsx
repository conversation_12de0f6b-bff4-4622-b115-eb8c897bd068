import React from "react";
import { db } from "@/lib/db";
import { payments } from "@/db/schema";
import { desc } from "drizzle-orm";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { formatCurrency, formatDate } from "@/lib/utils";
import { auth } from "~/auth";
import { redirect } from "next/navigation";
import { Metadata } from "next";

import {
    Table,
    TableHeader,
    TableBody,
    TableCell,
    TableRow,
    TableHead,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { DollarSign, Plus } from "lucide-react";

// Add export for no caching
export const dynamic = 'force-dynamic';
export const revalidate = 0;

export const metadata: Metadata = {
    title: "Payment History",
    description: "View and manage payment transactions",
};

export default async function PaymentsPage() {
    const session = await auth();
    if (!session?.user) {
        redirect("/api/auth/signin");
    }

    const tenantId = session.user.id;
    if (!tenantId) {
        redirect("/dashboard");
    }

    // Get payment transactions for this tenant
    const paymentTransactions = await db.query.payments.findMany({
        where: (payments, { eq }) => eq(payments.tenantId, tenantId),
        orderBy: [desc(payments.date)],
        limit: 100,
        with: {
            customer: true,
            order: true,
        },
    });

    return (
        <div className="container mx-auto py-10">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-3xl font-bold">Payment Transactions</h1>
                <Link href="/dashboard/payments/new">
                    <Button className="cursor-pointer">
                        <Plus className="w-4 h-4" />
                        Record Payment
                    </Button>
                </Link>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>Recent Payments</CardTitle>
                    <CardDescription>
                        History of payment transactions
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Date</TableHead>
                                    <TableHead>Customer</TableHead>
                                    <TableHead>Payment Type</TableHead>
                                    <TableHead>Method</TableHead>
                                    <TableHead className="text-right">Amount</TableHead>
                                    <TableHead>Reference</TableHead>
                                    <TableHead>Notes</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {paymentTransactions.length > 0 ? (
                                    paymentTransactions.map((payment) => (
                                        <TableRow key={payment.id} className="border-b hover:bg-gray-50">
                                            <TableCell>
                                                {formatDate(payment.date)}
                                            </TableCell>
                                            <TableCell className="font-medium">
                                                {payment.customer?.name || "—"}
                                            </TableCell>
                                            <TableCell>
                                                <Badge variant="outline" className={
                                                    payment.paymentType === "due_payment"
                                                        ? "bg-amber-50 text-amber-700 border-amber-200"
                                                        : "bg-green-50 text-green-700 border-green-200"
                                                }>
                                                    {payment.paymentType === "due_payment" ? "Due Payment" : "Sale Payment"}
                                                </Badge>
                                            </TableCell>
                                            <TableCell>
                                                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                                                    {payment.paymentMethod === "cash"
                                                        ? "Cash"
                                                        : payment.paymentMethod === "card"
                                                            ? "Card"
                                                            : "Bank Transfer"}
                                                </Badge>
                                            </TableCell>
                                            <TableCell className="text-right font-medium">
                                                {formatCurrency(payment.amount)}
                                            </TableCell>
                                            <TableCell>
                                                {payment.paymentReference || "—"}
                                            </TableCell>
                                            <TableCell className="truncate max-w-[200px]">
                                                {payment.notes || "—"}
                                            </TableCell>
                                        </TableRow>
                                    ))
                                ) : (
                                    <TableRow>
                                        <TableCell colSpan={7} className="text-center py-4 text-gray-500">
                                            No payment transactions found.
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </div>
                </CardContent>
            </Card>

            <div className="mt-6">
                <Card>
                    <CardHeader>
                        <CardTitle>Customers with Outstanding Balances</CardTitle>
                        <CardDescription>
                            View customers with pending due amounts
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <Link href="/dashboard/customers?filter=due" className="w-full">
                                <Button variant="outline" className="w-full justify-between">
                                    View Customers with Dues
                                    <DollarSign className="w-4 h-4 ml-2" />
                                </Button>
                            </Link>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}