"use client";

import { useEffect, useState } from "react";

export default function AutoPrint() {
    // Use state to track if we're in the browser
    const [isBrowser, setIsBrowser] = useState(false);

    // First effect to safely determine if we're in the browser
    useEffect(() => {
        setIsBrowser(true);
    }, []);

    // Second effect to handle printing only after we confirm we're in the browser
    useEffect(() => {
        if (isBrowser) {
            // Short delay to ensure the page is fully rendered
            const timer = setTimeout(() => {
                window.print();
            }, 1000); // Increased delay for better reliability

            return () => clearTimeout(timer);
        }
    }, [isBrowser]);

    return null;
}