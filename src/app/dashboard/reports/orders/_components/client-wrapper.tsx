'use client';

import { useState, useEffect } from 'react';
import { usePermissionCheck } from '@/lib/check-permissions';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { formatCurrency } from "@/lib/utils";
import { DateRange } from "react-day-picker";
import { format, subDays } from "date-fns";
import { toast } from 'sonner';

interface Branch {
  id: string;
  name: string;
  isMain?: boolean | null;
}

interface Product {
  id: string;
  name: string;
  code?: string | null;
}

interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

interface Order {
  id: string;
  memoNo: string;
  date: string;
  totalAmount: number;
  paidAmount: number;
  dueAmount: number;
  paymentStatus: string;
  customer: {
    name: string;
  } | null;
  branch: {
    name: string;
  };
  items: OrderItem[];
}

interface ClientWrapperProps {
  branches: Branch[];
  userRole: string;
  userBranch: Branch | null;
}

export default function ClientWrapper({
  branches,
  userRole,
  userBranch
}: ClientWrapperProps) {
  // Set default date range to last 30 days
  const defaultFrom = subDays(new Date(), 30);
  const defaultTo = new Date();

  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: defaultFrom,
    to: defaultTo
  });
  const [selectedBranchId, setSelectedBranchId] = useState<string>('');
  const [selectedProductId, setSelectedProductId] = useState<string>('');
  const [products, setProducts] = useState<Product[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [summary, setSummary] = useState({
    totalOrders: 0,
    totalAmount: 0,
    totalPaid: 0,
    totalDue: 0
  });

  // Check if user has permission to view order reports
  // Only tenant_sale users need permission check, tenant users have full access
  const { isLoading: permissionLoading } = userRole === 'tenant_sale'
    ? usePermissionCheck('/dashboard/reports/orders', 'view')
    : { isLoading: false };

  // Fetch products for the dropdown
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await fetch('/api/products/list');

        if (!response.ok) {
          toast.error(`Error loading products: ${response.status}`);
          return;
        }

        const data = await response.json();

        if (data.success) {
          setProducts(data.products);
        } else {
          toast.error(data.error || 'Failed to load products');
        }
      } catch (error) {
        console.error('Error fetching products:', error);
        toast.error('Error loading products');
      }
    };

    fetchProducts();
  }, []);

  // Set default branch for tenant_sale users
  useEffect(() => {
    if (userRole === 'tenant_sale' && userBranch) {
      setSelectedBranchId(userBranch.id);
    }
  }, [userRole, userBranch]);

  // Handle generating the report
  const handleGenerateReport = async () => {
    if (!dateRange?.from) {
      toast.error('Please select a date range');
      return;
    }

    setIsLoading(true);

    try {
      // Build query parameters
      const params = new URLSearchParams();

      if (dateRange.from) {
        params.append('fromDate', dateRange.from.toISOString());
      }

      if (dateRange.to) {
        params.append('toDate', dateRange.to.toISOString());
      }

      if (selectedBranchId) {
        params.append('branchId', selectedBranchId);
      }

      if (selectedProductId) {
        params.append('productId', selectedProductId);
      }

      // Fetch report data using the regular endpoint
      // The middleware will handle permissions correctly for both tenant and tenant_sale users
      const response = await fetch(`/api/reports/orders?${params.toString()}`);

      // Check if response is OK before trying to parse JSON
      if (!response.ok) {
        toast.error(`Error: ${response.status} - ${response.statusText}`);
        setIsLoading(false);
        return;
      }

      // Parse the JSON response
      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        toast.error('Failed to parse API response');
        setIsLoading(false);
        return;
      }

      if (data.success) {
        setOrders(data.orders);

        // Calculate summary
        const totalOrders = data.orders.length;
        const totalAmount = data.orders.reduce((sum: number, order: Order) => sum + order.totalAmount, 0);
        const totalPaid = data.orders.reduce((sum: number, order: Order) => sum + order.paidAmount, 0);
        const totalDue = data.orders.reduce((sum: number, order: Order) => sum + order.dueAmount, 0);

        setSummary({
          totalOrders,
          totalAmount,
          totalPaid,
          totalDue
        });
      } else {
        console.error('API returned error:', data.error);
        toast.error(data.error || 'Failed to generate report');
      }
    } catch (error) {
      console.error('Error generating report:', error);
      toast.error('An error occurred while generating the report. Check console for details.');
    } finally {
      setIsLoading(false);
    }
  };

  if (permissionLoading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Order Reports</h1>
      </div>

      <Card className="mb-6">
        <CardContent className="pt-4">
          <div className="flex flex-wrap items-end gap-3">
            <div className="flex-1 min-w-[200px]">
              <label className="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
              <DateRangePicker
                value={dateRange}
                onValueChange={setDateRange}
                placeholder="Select date range"
              />
            </div>

            <div className="flex-1 min-w-[150px]">
              <label className="block text-sm font-medium text-gray-700 mb-1">Branch</label>
              <Select
                value={selectedBranchId}
                onValueChange={setSelectedBranchId}
                disabled={userRole === 'tenant_sale' && userBranch !== null}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All branches" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All branches</SelectItem>
                  {branches.map((branch) => (
                    <SelectItem key={branch.id} value={branch.id}>
                      {branch.name} {branch.isMain ? "(Main)" : ""}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex-1 min-w-[150px]">
              <label className="block text-sm font-medium text-gray-700 mb-1">Product</label>
              <Select value={selectedProductId} onValueChange={setSelectedProductId}>
                <SelectTrigger>
                  <SelectValue>
                    {selectedProductId
                      ? products.find(p => p.id === selectedProductId)?.name || "All products"
                      : "All products"}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All products</SelectItem>
                  {products.map((product) => (
                    <SelectItem key={product.id} value={product.id}>
                      {product.name} {product.code ? `(${product.code})` : ''}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex-none">
              <Button
                onClick={handleGenerateReport}
                className="px-6"
                disabled={isLoading}
              >
                {isLoading ? 'Generating...' : 'Generate Report'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {orders.length > 0 && (
        <>
          <Card className="mb-6">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-gray-50 p-4 rounded-md">
                  <div className="text-sm text-gray-500">Total Orders</div>
                  <div className="text-2xl font-bold">{summary.totalOrders}</div>
                </div>
                <div className="bg-gray-50 p-4 rounded-md">
                  <div className="text-sm text-gray-500">Total Amount</div>
                  <div className="text-2xl font-bold">{formatCurrency(summary.totalAmount)}</div>
                </div>
                <div className="bg-gray-50 p-4 rounded-md">
                  <div className="text-sm text-gray-500">Total Paid</div>
                  <div className="text-2xl font-bold">{formatCurrency(summary.totalPaid)}</div>
                </div>
                <div className="bg-gray-50 p-4 rounded-md">
                  <div className="text-sm text-gray-500">Total Due</div>
                  <div className="text-2xl font-bold">{formatCurrency(summary.totalDue)}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Order Details</CardTitle>
              <CardDescription>
                {dateRange?.from && dateRange?.to
                  ? `Orders from ${format(dateRange.from, 'MMM d, yyyy')} to ${format(dateRange.to, 'MMM d, yyyy')}`
                  : 'All orders'}
                {selectedBranchId && branches.find(b => b.id === selectedBranchId)
                  ? ` - Branch: ${branches.find(b => b.id === selectedBranchId)?.name}`
                  : ''}
                {selectedProductId && products.find(p => p.id === selectedProductId)
                  ? ` - Product: ${products.find(p => p.id === selectedProductId)?.name}`
                  : ''}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Memo No</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Customer</TableHead>
                      <TableHead>Branch</TableHead>
                      <TableHead className="text-right">Total</TableHead>
                      <TableHead className="text-right">Paid</TableHead>
                      <TableHead className="text-right">Due</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {orders.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell>{order.memoNo}</TableCell>
                        <TableCell>{format(new Date(order.date), 'MMM d, yyyy')}</TableCell>
                        <TableCell>{order.customer?.name || 'N/A'}</TableCell>
                        <TableCell>{order.branch?.name}</TableCell>
                        <TableCell className="text-right">{formatCurrency(order.totalAmount)}</TableCell>
                        <TableCell className="text-right">{formatCurrency(order.paidAmount)}</TableCell>
                        <TableCell className="text-right">{formatCurrency(order.dueAmount)}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs ${order.paymentStatus === 'paid'
                            ? 'bg-green-100 text-green-800'
                            : order.paymentStatus === 'partial'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                            }`}>
                            {order.paymentStatus === 'paid'
                              ? 'Paid'
                              : order.paymentStatus === 'partial'
                                ? 'Partial'
                                : 'Unpaid'}
                          </span>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {!isLoading && orders.length === 0 && (
        <Card>
          <CardContent className="py-10">
            <div className="text-center text-gray-500">
              <p className="mb-2">No orders found matching the selected filters.</p>
              <p>Try adjusting your filters or select a different date range.</p>
            </div>
          </CardContent>
        </Card>
      )}
    </>
  );
}
