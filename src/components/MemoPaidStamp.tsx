"use client";

import { cn } from "@/lib/utils";

interface MemoPaidStampProps {
  className?: string;
  language?: "english" | "bengali";
  size?: "small" | "medium" | "large";
  color?: string;
}

export default function MemoPaidStamp({
  className,
  language = "bengali",
  size = "medium",
  color = "gray-400"
}: MemoPaidStampProps) {
  // Size classes mapping
  const sizeClasses = {
    small: "px-4 py-4 text-6xl",
    medium: "px-6 py-6 text-7xl",
    large: "px-8 py-8 text-8xl"
  };

  // Get the appropriate size class or default to medium
  const sizeClass = sizeClasses[size] || sizeClasses.medium;

  // Handle color - if it's a tailwind color like "red", convert to text-red-500 format
  // Otherwise use the color directly in the style
  const isCustomColor = color && !color.includes('-');
  const borderColorClass = isCustomColor ? "" : `border-${color}`;
  const textColorClass = isCustomColor ? "" : `text-${color}`;

  return (
    <div
      className={cn(
        "absolute transform rotate-[-30deg] border-4 border-gray-400 text-6xl text-gray-400 rounded-lg opacity-40 flex items-center justify-center font-bold",
        className
      )}
      style={{
        zIndex: 50,
      }}
    >
      {language === "bengali" ? "PAID" : "PAID"}
    </div>
  );
}
