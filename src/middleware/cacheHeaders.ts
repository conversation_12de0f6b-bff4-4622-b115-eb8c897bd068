import { NextRequest, NextResponse } from 'next/server';

/**
 * Middleware to add caching headers to API responses
 * @param handler The API route handler
 * @param maxAge Cache max age in seconds
 * @returns A new handler with caching headers
 */
export function withCacheHeaders(
  handler: (req: NextRequest, ...args: any[]) => Promise<NextResponse>,
  maxAge: number = 60 // Default to 1 minute
) {
  return async function (req: NextRequest, ...args: any[]) {
    // Call the original handler
    const response = await handler(req, ...args);
    
    // Add cache control headers
    response.headers.set(
      'Cache-Control',
      `public, s-maxage=${maxAge}, stale-while-revalidate=${maxAge * 2}`
    );
    
    return response;
  };
}

/**
 * Middleware to prevent caching of API responses
 * @param handler The API route handler
 * @returns A new handler with no-cache headers
 */
export function withNoCache(
  handler: (req: NextRequest, ...args: any[]) => Promise<NextResponse>
) {
  return async function (req: NextRequest, ...args: any[]) {
    // Call the original handler
    const response = await handler(req, ...args);
    
    // Add no-cache headers
    response.headers.set(
      'Cache-Control',
      'no-store, no-cache, must-revalidate, proxy-revalidate'
    );
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
    
    return response;
  };
}
