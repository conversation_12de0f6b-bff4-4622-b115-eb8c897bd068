# Tenant Permissions System

This document explains how the tenant permissions system works in the NextSaaS application.

## Overview

The system has a multi-tenant architecture with the following user roles:
- **Admin**: System administrators with full access
- **Tenant**: Main tenant users who can create and manage tenant_sale users
- **Tenant_Sale**: Sales users created by tenants with limited menu access

## Tenant Creation and Subscription

1. When a tenant is created, they get a subscription with an end date
2. The subscription can be free-trial or paid
3. When the subscription expires, the tenant and all their tenant_sale users cannot log in

## Tenant_Sale User Creation

1. Tenant users can create tenant_sale users with specific menu permissions
2. Each tenant_sale user is associated with their parent tenant through the tenantUsers table
3. Menu permissions are assigned through the userMenuPermissions table

## Menu Access Control

1. Admin and tenant users have access to all menus
2. Tenant_sale users can only access menus they have explicit permission for
3. Menu permissions include:
   - canView: Whether the user can see the menu
   - canCreate: Whether the user can create new items
   - canEdit: Whether the user can edit existing items
   - canDelete: Whether the user can delete items

## Authentication Flow

1. When a user logs in, the system checks their role
2. For tenant users, it verifies they have an active subscription
3. For tenant_sale users, it:
   - Verifies they have valid credentials
   - Checks if their parent tenant has an active subscription
   - Verifies they have at least one menu permission
4. After successful login, users are redirected to the dashboard

## Subscription Expiration

When a tenant's subscription expires:
1. The tenant cannot log in
2. All tenant_sale users associated with that tenant cannot log in
3. The system shows an error message about the expired subscription

## Implementation Details

- Tenant IDs in the database schema use `users.id` as the tenant ID, not `tenants.id`
- The `tenants` table is primarily used for licensing/subscription management
- The `tenantUsers` table manages user access permissions
- Menu permissions are defined in the `userMenuPermissions` table
