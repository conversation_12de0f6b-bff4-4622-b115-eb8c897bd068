import { auth } from "../../../auth";
import { db } from "../../lib/db";
import { users, products, tenants } from "../../db/schema";
import { count, eq } from "drizzle-orm";
import { getCachedData } from "@/lib/cache";

export default async function DashboardPage() {
  const session = await auth();

  // Get user count for statistics (only for admin)
  const userCount = session?.user?.role === 'admin'
    ? await getCachedData(
      'admin_user_count',
      async () => (await db.select({ count: count() }).from(users))[0]?.count || 0,
      5 * 60 * 1000 // Cache for 5 minutes
    )
    : null;

  // Get product count for statistics (only for tenant and tenant_sale roles with valid tenantId)
  let productCount = null;
  if ((session?.user?.role === 'tenant' || session?.user?.role === 'tenant_sale') && session?.user?.tenantId) {
    try {
      productCount = await getCachedData(
        `product_count_${session.user.tenantId}`,
        async () => {
          const result = await db.select({ count: count() }).from(products).where(eq(products.tenantId, session.user.tenantId as string));
          return result[0]?.count || 0;
        },
        5 * 60 * 1000 // Cache for 5 minutes
      );
    } catch (error) {
      productCount = 0;
    }
  }

  // Get tenant count for statistics (only for admin)
  const tenantCount = session?.user?.role === 'admin'
    ? await getCachedData(
      'admin_tenant_count',
      async () => (await db.select({ count: count() }).from(tenants))[0]?.count || 0,
      5 * 60 * 1000 // Cache for 5 minutes
    )
    : null;

  return (
    <div>
      <h2 className="text-2xl font-bold mb-6">Welcome, {session?.user?.name || 'User'}!</h2>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* User Stats Card (only for admin) */}
        {userCount !== null && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium mb-2">Users</h3>
            <p className="text-3xl font-bold">{userCount}</p>
            <p className="text-sm text-gray-500 mt-2">Total registered users</p>
          </div>
        )}

        {/* Tenant Stats Card (only for admin) */}
        {tenantCount !== null && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium mb-2">Tenants</h3>
            <p className="text-3xl font-bold">{tenantCount}</p>
            <p className="text-sm text-gray-500 mt-2">Active tenants</p>
          </div>
        )}

        {/* Product Stats Card (only for tenant and tenant_sale) */}
        {productCount !== null && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium mb-2">Products</h3>
            <p className="text-3xl font-bold">{productCount}</p>
            <p className="text-sm text-gray-500 mt-2">Total products</p>
          </div>
        )}
      </div>
    </div>
  );
}