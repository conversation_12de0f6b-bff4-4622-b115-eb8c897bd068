import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { orders, customers } from "@/db/schema";
import { auth } from "~/auth";
import { eq, desc, and, sql, count } from "drizzle-orm";
import { withMenuPermission } from "@/middleware/menuPermissions";

async function getHandler(request: NextRequest, _context: any, permissionCheck: any) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ success: false, message: "Unauthorized" }, { status: 401 });
    }

    // Check if user has view permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      const menuPermission = permissionCheck.permissions.find(
        (p: any) => p.menu.path === '/dashboard/sales'
      );

      if (!menuPermission?.canView) {
        return NextResponse.json(
          { success: false, error: "You do not have permission to view sales" },
          { status: 403 }
        );
      }
    }

    // Get tenant ID from session based on user role
    const tenantId = session.user.tenantId || session.user.id;

    // Get query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const pageSize = parseInt(url.searchParams.get("pageSize") || "20");
    const search = url.searchParams.get("search") || "";
    const branchId = url.searchParams.get("branchId") || "";

    // Calculate offset
    const offset = (page - 1) * pageSize;

    // Base conditions
    let whereConditions = [eq(orders.tenantId, tenantId)];

    // For tenant_sale users, only show orders from their assigned branch
    if (session.user.role === 'tenant_sale' && session.user.branchId) {
      whereConditions.push(eq(orders.branchId, session.user.branchId));
    }
    // For tenant users with branch filter, filter by the selected branch
    else if (session.user.role === 'tenant' && branchId) {
      whereConditions.push(eq(orders.branchId, branchId));
    }

    // Apply search filter if provided
    if (search) {
      // Get customer IDs that match the search term
      const customerIds = await db.select({ id: customers.id })
        .from(customers)
        .where(and(
          eq(customers.tenantId, tenantId),
          sql`LOWER(${customers.name}) LIKE LOWER(${'%' + search + '%'})`
        ));

      const customerIdList = customerIds.map(c => c.id);

      // Add search conditions
      if (customerIdList.length > 0) {
        // Create a combined SQL condition for memo number or customer ID
        const combinedCondition = sql`(LOWER(${orders.memoNo}) LIKE LOWER(${'%' + search + '%'}) OR ${orders.customerId} IN (${customerIdList.join(',')}))`;
        whereConditions.push(combinedCondition);
      } else {
        whereConditions.push(
          sql`LOWER(${orders.memoNo}) LIKE LOWER(${'%' + search + '%'})`
        );
      }
    }

    // Get total count for pagination
    const totalCountResult = await db
      .select({ count: count() })
      .from(orders)
      .where(and(...whereConditions));

    const totalCount = totalCountResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / pageSize);

    // Get paginated orders
    const paginatedOrders = await db.query.orders.findMany({
      where: and(...whereConditions),
      orderBy: [desc(orders.createdAt)],
      with: {
        customer: true,
        branch: {
          columns: {
            id: true,
            name: true,
            code: true,
            isMain: true
          }
        },
        performer: {
          columns: {
            id: true,
            fullName: true,
            username: true,
          }
        },
        items: {
          with: {
            product: {
              columns: {
                id: true,
                name: true,
              }
            },
          },
        },
      },
      limit: pageSize,
      offset: offset,
    });

    // Prepare result without caching
    const result = {
      orders: paginatedOrders,
      pagination: {
        totalCount,
        totalPages,
        currentPage: page,
        pageSize,
      }
    };

    const response = NextResponse.json({
      success: true,
      ...result
    });

    // Add explicit no-cache headers
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    return response;
  } catch (error) {
    console.error("Error fetching paginated orders:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch orders",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// Export the route handler with permission middleware (no caching)
export const GET = withMenuPermission(getHandler, '/dashboard/sales');
