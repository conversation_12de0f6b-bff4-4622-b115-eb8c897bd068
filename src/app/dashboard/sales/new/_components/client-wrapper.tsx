'use client';

import { usePermissionCheck } from '@/lib/check-permissions';
import { SaleForm } from './sale-form';

interface Product {
  id: string;
  name: string;
  code: string | null;
  sellingPrice: number | null;
  costPrice: number | null;
  stock: number;
  category: {
    name: string;
  } | null;
  vendor: {
    name: string;
  } | null;
}

interface Customer {
  id: string;
  name: string;
  phone?: string | null;
  email?: string | null;
  currentBalance?: number | null;
  tenantId: string;
  code?: string | null;
  address?: string | null;
  type?: "retail" | "wholesale" | null;
  isActive?: boolean | null;
  creditLimit?: number | null;
  initialDue?: number | null;
  notes?: string | null;
  createdAt?: Date | null;
  updatedAt?: Date | null;
}

interface Branch {
  id: string;
  name: string;
  code: string;
  isMain: boolean | null;
  isActive: boolean | null;
  email?: string | null;
  phone?: string | null;
  address?: string | null;
  tenantId: string;
  notes?: string | null;
  createdAt?: Date | null;
  updatedAt?: Date | null;
}

interface ClientWrapperProps {
  products: Product[];
  customers: Customer[];
  branches: Branch[];
  initialBranchId: string;
  initialMemoNo: string;
  tenantId: string;
  inventoryData: Map<string, number>;
  sessionBranchId: string | null;
}

export default function ClientWrapper({
  products,
  customers,
  branches,
  initialBranchId,
  initialMemoNo,
  tenantId,
  inventoryData,
  sessionBranchId
}: ClientWrapperProps) {
  // Check if user has permission to create sales
  const { isLoading } = usePermissionCheck('/dashboard/sales', 'create');

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <SaleForm
      products={products}
      customers={customers}
      branches={branches}
      initialBranchId={initialBranchId}
      initialMemoNo={initialMemoNo}
      tenantId={tenantId}
      inventoryData={inventoryData}
      sessionBranchId={sessionBranchId}
    />
  );
}
