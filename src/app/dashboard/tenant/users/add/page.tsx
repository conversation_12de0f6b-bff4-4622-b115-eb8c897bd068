import { auth } from "~/auth";
import { redirect } from "next/navigation";
import TenantSaleUserForm from "@/app/components/TenantSaleUserForm";

export default async function AddTenantSaleUserPage() {
  const session = await auth();
  
  // Redirect to login if not authenticated
  if (!session?.user) {
    redirect("/");
  }
  
  // Redirect if not tenant
  if (session.user.role !== 'tenant') {
    redirect("/dashboard");
  }
  
  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Add Tenant Sale User</h1>
      <TenantSaleUserForm />
    </div>
  );
}
