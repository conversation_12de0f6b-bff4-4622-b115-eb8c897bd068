'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

// User type for selection in the form
interface User {
    id: string;
    username: string;
    email: string;
    fullName: string;
}

export default function NewTenantPage() {
    const router = useRouter();
    const [availableUsers, setAvailableUsers] = useState<User[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState(false);

    // Form state
    const [formData, setFormData] = useState({
        userId: '',
        companyName: '',
        subscriptionType: 'free-trial',
        domain: '',
        isActive: true
    });

    // Fetch available users who don't have a tenant yet
    useEffect(() => {
        const fetchAvailableUsers = async () => {
            try {
                setLoading(true);
                const response = await fetch('/api/users/available');

                if (!response.ok) {
                    throw new Error('Failed to fetch available users');
                }

                const data = await response.json();

                if (data.success) {
                    setAvailableUsers(data.users);
                } else {
                    setError(data.error || 'Failed to load available users');
                }
            } catch (err) {
                setError('An error occurred while fetching available users');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        fetchAvailableUsers();
    }, []);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value, type } = e.target as HTMLInputElement;

        setFormData({
            ...formData,
            [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
        });
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        try {
            setLoading(true);
            setError('');
            setSuccess(false);

            // Validate form
            if (!formData.userId || !formData.companyName || !formData.subscriptionType) {
                setError('Please fill in all required fields');
                return;
            }

            const response = await fetch('/api/tenants', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();

            if (data.success) {
                setSuccess(true);
                // Redirect to tenants list after successful creation
                setTimeout(() => {
                    router.push('/dashboard/tenants');
                }, 2000);
            } else {
                setError(data.error || 'Failed to create tenant');
            }
        } catch (err) {
            setError('An error occurred while creating the tenant');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="mb-6">
                <button
                    onClick={() => router.back()}
                    className="text-gray-600 hover:text-blue-500 flex items-center"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
                    </svg>
                    Back to Tenants
                </button>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h1 className="text-2xl font-bold mb-6">Register New Tenant</h1>

                {error && (
                    <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                        <strong className="font-bold">Error: </strong>
                        <span className="block sm:inline">{error}</span>
                    </div>
                )}

                {success && (
                    <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
                        <span className="block sm:inline">Tenant created successfully! Redirecting...</span>
                    </div>
                )}

                <form onSubmit={handleSubmit}>
                    <div className="space-y-6">
                        <div>
                            <label htmlFor="userId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                User <span className="text-red-500">*</span>
                            </label>
                            <select
                                id="userId"
                                name="userId"
                                value={formData.userId}
                                onChange={handleChange}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                required
                            >
                                <option value="">Select a user</option>
                                {availableUsers.map(user => (
                                    <option key={user.id} value={user.id}>
                                        {user.fullName} ({user.email})
                                    </option>
                                ))}
                            </select>
                            {availableUsers.length === 0 && !loading && (
                                <p className="mt-1 text-sm text-orange-600">No available users. Create a user first.</p>
                            )}
                        </div>

                        <div>
                            <label htmlFor="companyName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Company Name <span className="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                id="companyName"
                                name="companyName"
                                value={formData.companyName}
                                onChange={handleChange}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                required
                            />
                        </div>

                        <div>
                            <label htmlFor="domain" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Domain
                            </label>
                            <input
                                type="text"
                                id="domain"
                                name="domain"
                                value={formData.domain}
                                onChange={handleChange}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                placeholder="example.com (optional)"
                            />
                        </div>

                        <div>
                            <label htmlFor="subscriptionType" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Subscription Type <span className="text-red-500">*</span>
                            </label>
                            <select
                                id="subscriptionType"
                                name="subscriptionType"
                                value={formData.subscriptionType}
                                onChange={handleChange}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                required
                            >
                                <option value="free-trial">Free Trial (7 days)</option>
                                <option value="paid">Paid</option>
                            </select>
                        </div>

                        <div className="flex items-center">
                            <input
                                type="checkbox"
                                id="isActive"
                                name="isActive"
                                checked={formData.isActive}
                                onChange={handleChange}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                Active
                            </label>
                        </div>

                        <div className="flex justify-end">
                            <button
                                type="button"
                                onClick={() => router.back()}
                                className="mr-4 bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                disabled={loading}
                                className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 disabled:opacity-50"
                            >
                                {loading ? 'Creating...' : 'Create Tenant'}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
} 