'use client';

import { useState, useEffect } from 'react';
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';

type AuthMode = 'login' | 'register';

// Branch interface removed as we're bypassing branch selection

export default function AuthForms() {
  const router = useRouter();
  const [mode, setMode] = useState<AuthMode>('login');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [accountStatus, setAccountStatus] = useState<{
    type: 'inactive' | 'expired' | 'permission' | null;
    message: string;
  } | null>(null);

  // Auto-dismiss alerts and success messages
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        setSuccess('');
      }, 3000); // Auto-dismiss after 3 seconds
      return () => clearTimeout(timer);
    }
  }, [success]);

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        setError('');
      }, 2000); // Auto-dismiss after 5 seconds
      return () => clearTimeout(timer);
    }
  }, [error]);

  useEffect(() => {
    if (accountStatus) {
      const timer = setTimeout(() => {
        setAccountStatus(null);
      }, 2000); // Auto-dismiss after 8 seconds
      return () => clearTimeout(timer);
    }
  }, [accountStatus]);

  // Branch-related code removed as we're bypassing branch selection

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setIsLoading(true);

    try {
      if (mode === 'login') {
        // Use NextAuth signIn for login
        const result = await signIn('credentials', {
          email,
          password,
          redirect: false
        });

        if (!result?.error) {
          // Get user data including tenant information
          const response = await fetch('/api/auth/me');
          const userData = await response.json();

          if (userData.success) {
            setSuccess('Login successful! Redirecting to dashboard...');

            // Redirect after a short delay
            setTimeout(() => {
              router.push('/dashboard');
            }, 2000);
          } else {
            setError('Could not retrieve user information');
          }
        } else {
          // Check for specific tenant-related errors
          const errorMsg = result.error || 'Login failed';

          // Handle the "Configuration" error or any other generic error which might be masking our actual error message
          if (errorMsg === 'Configuration' || errorMsg === 'Login failed' || errorMsg.includes('CredentialsSignin')) {
            // Try to determine the likely cause based on the user's email
            const checkUserStatus = async () => {
              try {
                // Make a direct API call to check user status
                const response = await fetch('/api/auth/check-status', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ email })
                });

                const data = await response.json();

                if (data.error) {
                  if (data.error.includes('deactivated')) {
                    const message = 'Your tenant account has been deactivated. Please contact admin for assistance.';
                    setAccountStatus({
                      type: 'inactive',
                      message: message
                    });
                  } else if (data.error.includes('expired')) {
                    const message = 'Your subscription has expired. Please contact admin to renew your subscription.';
                    setAccountStatus({
                      type: 'expired',
                      message: message
                    });
                  } else {
                    setAccountStatus({
                      type: 'permission',
                      message: data.error
                    });
                  }
                } else {
                  // If we can't determine the specific issue, show a generic message
                  setAccountStatus({
                    type: 'inactive',
                    message: 'Your account has been deactivated or your subscription has expired. Please contact admin for assistance.'
                  });
                }
              } catch (err) {
                console.error('Error checking user status:', err);
                // If the API call fails, show a generic message
                setAccountStatus({
                  type: 'inactive',
                  message: 'Your account has been deactivated or your subscription has expired. Please contact admin for assistance.'
                });
              }

            };

            checkUserStatus();
          } else if (errorMsg.includes('deactivated')) {
            setAccountStatus({
              type: 'inactive',
              message: errorMsg
            });
          } else if (errorMsg.includes('expired') || errorMsg.includes('subscription')) {
            setAccountStatus({
              type: 'expired',
              message: errorMsg
            });
          } else if (errorMsg.includes('permissions')) {
            setAccountStatus({
              type: 'permission',
              message: errorMsg
            });
          } else {
            setError(errorMsg);
          }
        }
      } else {
        // Register new user
        const response = await fetch('/api/auth/register', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email, password, name, companyName })
        });

        const data = await response.json();

        if (data.success) {
          setSuccess('Registration successful! You can now log in.');

          // Switch to login mode after a short delay
          setTimeout(() => {
            setMode('login');
          }, 3000);
        } else {
          const errorMessage = data.error || 'Registration failed';
          setError(errorMessage);
        }
      }
    } catch (err) {
      const errorMessage = 'An unexpected error occurred';
      setError(errorMessage);
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  // Branch-related code removed as we're bypassing branch selection

  return (
    <div className="w-full max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <>
        <h2 className="text-2xl font-bold mb-6 text-center">
          {mode === 'login' ? 'Login' : 'Register'}
        </h2>

        {/* Account status warning - more prominent than regular errors */}
        {accountStatus && (
          <div className="mb-6 p-4 border-l-4 border-red-500 bg-red-50 text-red-800 rounded shadow-sm">
            <div className="flex items-center mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <h3 className="font-bold text-lg">
                {accountStatus.type === 'inactive' ? 'Account Deactivated' :
                  accountStatus.type === 'expired' ? 'Subscription Expired' :
                    'Access Denied'}
              </h3>
            </div>
            <p>{accountStatus.message}</p>

          </div>
        )}

        {error && !accountStatus && (
          <div className="mb-4 p-3 bg-red-100 text-red-700 rounded">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-4 p-3 bg-green-100 text-green-700 rounded">
            {success}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {mode === 'register' && (
            <>
              <div>
                <label htmlFor="name" className="block text-sm font-medium mb-1">
                  Name
                </label>
                <input
                  id="name"
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Your name"
                />
              </div>
              <div>
                <label htmlFor="companyName" className="block text-sm font-medium mb-1">
                  Company Name
                </label>
                <input
                  id="companyName"
                  type="text"
                  value={companyName}
                  onChange={(e) => setCompanyName(e.target.value)}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Your company name"
                />
              </div>
            </>
          )}

          <div>
            <label htmlFor="email" className="block text-sm font-medium mb-1">
              Email
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium mb-1">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="••••••••"
            />
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 cursor-pointer"
          >
            {isLoading ? 'Processing...' : mode === 'login' ? 'Login' : 'Register'}
          </button>
        </form>

        <div className="mt-4 text-center">
          {mode === 'login' ? (
            <p>
              Don't have an account?{' '}
              <button
                onClick={() => setMode('register')}
                className="text-blue-600 hover:underline focus:outline-none cursor-pointer"
              >
                Register
              </button>
            </p>
          ) : (
            <p>
              Already have an account?{' '}
              <button
                onClick={() => setMode('login')}
                className="text-blue-600 hover:underline focus:outline-none cursor-pointer"
              >
                Login
              </button>
            </p>
          )}
        </div>
      </>
    </div>
  );
}