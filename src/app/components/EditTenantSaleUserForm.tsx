'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';

type UserData = {
  id: string;
  username: string;
  email: string;
  fullName: string;
  isActive: boolean;
};

type EditTenantSaleUserFormProps = {
  userId: string;
};

export default function EditTenantSaleUserForm({ userId }: EditTenantSaleUserFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [user, setUser] = useState<UserData | null>(null);
  
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    fullName: '',
    password: '',
    confirmPassword: '',
    isActive: true
  });

  // Fetch user data
  useEffect(() => {
    const fetchUser = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/tenant/users/${userId}`);
        const data = await response.json();
        
        if (data.success) {
          setUser(data.user);
          setFormData({
            username: data.user.username,
            email: data.user.email || '',
            fullName: data.user.fullName,
            password: '',
            confirmPassword: '',
            isActive: data.user.isActive
          });
        } else {
          toast.error(data.error || 'Failed to load user data');
          router.push('/dashboard/tenant/users');
        }
      } catch (error) {
        console.error('Error fetching user:', error);
        toast.error('An unexpected error occurred');
        router.push('/dashboard/tenant/users');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchUser();
  }, [userId, router]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Submit form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Validate form
    if (formData.password && formData.password !== formData.confirmPassword) {
      toast.error('Passwords do not match');
      setIsLoading(false);
      return;
    }
    
    try {
      // Prepare data for update
      const updateData = {
        username: formData.username,
        email: formData.email,
        fullName: formData.fullName,
        isActive: formData.isActive
      };
      
      // Only include password if it's provided
      if (formData.password) {
        Object.assign(updateData, { password: formData.password });
      }
      
      const response = await fetch(`/api/tenant/users/${userId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      });
      
      const data = await response.json();
      
      if (data.success) {
        toast.success('User updated successfully');
        router.push('/dashboard/tenant/users');
      } else {
        toast.error(data.error || 'Failed to update user');
      }
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading && !user) {
    return (
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">Edit User</h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label className='mb-2' htmlFor="fullName">Full Name</Label>
            <Input
              id="fullName"
              name="fullName"
              value={formData.fullName}
              onChange={handleInputChange}
              required
            />
          </div>
          
          <div>
            <Label className='mb-2' htmlFor="username">Username</Label>
            <Input
              id="username"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              required
            />
          </div>
          
          <div>
            <Label className='mb-2' htmlFor="email">Email</Label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              required
            />
          </div>
          
          <div>
            <Label className='mb-2' htmlFor="password">New Password (leave blank to keep current)</Label>
            <Input
              id="password"
              name="password"
              type="password"
              value={formData.password}
              onChange={handleInputChange}
            />
          </div>
          
          <div>
            <Label className='mb-2' htmlFor="confirmPassword">Confirm New Password</Label>
            <Input
              id="confirmPassword"
              name="confirmPassword"
              type="password"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              disabled={!formData.password}
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="isActive"
              name="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) => 
                setFormData(prev => ({ ...prev, isActive: checked === true }))
              }
            />
            <Label htmlFor="isActive">Active</Label>
          </div>
        </div>
        
        <div className="flex justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/dashboard/tenant/users')}
            className="mr-2 cursor-pointer"
            disabled={isLoading}
          >
            Cancel
          </Button>
          
          <Button type="submit" disabled={isLoading} className="cursor-pointer">
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </form>
    </div>
  );
}
