'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

type MenuPermission = {
  id?: string;
  menuId: string;
  name: string;
  displayName: string;
  path: string;
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
  order?: number;
};

type UserPermissionsFormProps = {
  userId: string;
  menuPermissions: MenuPermission[];
};

export default function UserPermissionsForm({ userId, menuPermissions }: UserPermissionsFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [permissions, setPermissions] = useState<MenuPermission[]>(menuPermissions);

  // Handle permission changes
  const handlePermissionChange = (menuId: string, permissionType: 'canView' | 'canCreate' | 'canEdit' | 'canDelete', checked: boolean) => {
    // If trying to uncheck view permission, check if it's the last one
    if (permissionType === 'canView' && !checked) {
      // Count how many visible menus have view permission
      const visibleWithView = permissions.filter(p =>
        p.menuId !== 'dashboard' && p.canView
      );

      // If this is the last visible menu with view permission, prevent unchecking
      if (visibleWithView.length === 1 && visibleWithView[0].menuId === menuId) {
        toast.error('At least one menu must have view permission');
        return;
      }
    }

    setPermissions(prev => {
      // First update the current permission
      const updatedPermissions = prev.map(permission =>
        permission.menuId === menuId
          ? {
            ...permission,
            [permissionType]: checked,
            // If view permission is turned off, turn off all other permissions
            ...(permissionType === 'canView' && !checked
              ? { canCreate: false, canEdit: false, canDelete: false }
              : {})
          }
          : permission
      );

      // Special case: If products permission is being enabled, also enable categories view permission
      if (checked && permissionType === 'canView') {
        const changedPermission = prev.find(p => p.menuId === menuId);

        // If this is the products menu and view is being enabled
        if (changedPermission?.path === '/dashboard/products') {
          // Find the categories menu and enable its view permission
          const finalPermissions = updatedPermissions.map(permission =>
            permission.path === '/dashboard/products/categories'
              ? { ...permission, canView: true }
              : permission
          );

          // Show a notification to the user
          toast.info('Categories view permission has been automatically enabled because Products permission is enabled');

          return finalPermissions;
        }
      }

      return updatedPermissions;
    });
  };

  // Submit form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Filter out menus without view permission and exclude dashboard
      const activePermissions = permissions.filter(
        permission => permission.canView && permission.menuId !== 'dashboard'
      );

      // Note: We no longer add dashboard permissions to the list sent to the API
      // Dashboard permissions are handled in the session, not stored in the database

      // Check if there are any active permissions
      if (activePermissions.length === 0) {
        toast.error('Please grant view permission to at least one menu');
        setIsLoading(false);
        return;
      }

      // Check if products permission is enabled but categories is not
      const hasProductsPermission = activePermissions.some(
        permission => permission.path === '/dashboard/products'
      );

      const hasCategoriesPermission = activePermissions.some(
        permission => permission.path === '/dashboard/products/categories'
      );

      if (hasProductsPermission && !hasCategoriesPermission) {
        // Auto-enable categories permission
        const updatedPermissions = permissions.map(permission =>
          permission.path === '/dashboard/products/categories'
            ? { ...permission, canView: true }
            : permission
        );

        setPermissions(updatedPermissions);

        // Update activePermissions to include the categories permission
        const categoriesPermission = permissions.find(
          permission => permission.path === '/dashboard/products/categories'
        );

        if (categoriesPermission) {
          // Create a new copy of the permission with view enabled
          const updatedCategoryPermission = {
            ...categoriesPermission,
            canView: true
          };

          // Add to active permissions
          activePermissions.push(updatedCategoryPermission);

          // Sort active permissions again after adding the new one
          activePermissions.sort((a, b) => (a.order || 999) - (b.order || 999));
        }

        toast.info('Categories view permission has been automatically enabled because Products permission is enabled');
      }

      // Sort active permissions by order before sending
      const sortedActivePermissions = [...activePermissions].sort(
        (a, b) => (a.order || 999) - (b.order || 999)
      );

      const response = await fetch('/api/tenant/users/permissions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId,
          permissions: sortedActivePermissions
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success('User permissions updated successfully');
        router.push('/dashboard/tenant/users');
      } else {
        // Show a more detailed error message
        if (data.error && data.error.includes('Foreign key constraint')) {
          toast.error(data.error);

          // Show a helpful message about running the seed-menus script
          toast.info('Run "npm run seed:menus" to populate the menus table', {
            duration: 10000, // Show for 10 seconds
            action: {
              label: 'Learn More',
              onClick: () => {
                // Open a new window with instructions
                window.open('/dashboard/tenant/users', '_blank');
              }
            }
          });
        } else {
          toast.error(data.error || 'Failed to update permissions');
        }
      }
    } catch (error) {
      console.error('Error updating permissions:', error);
      toast.error('An unexpected error occurred. Try running "npm run seed:menus" first.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <h3 className="text-lg font-medium mb-4">Menu Permissions</h3>

          {permissions.length === 0 ? (
            <div className="border rounded-md p-8 text-center">
              <p className="text-gray-500 mb-4">No menu data available. Please initialize menu data first.</p>
              <Button
                type="button"
                onClick={() => window.location.href = '/dashboard/tenant/users/add'}
                className="bg-blue-500 hover:bg-blue-600"
              >
                Go to Add User Page
              </Button>
            </div>
          ) : (
            <div className="border rounded-md p-4">
              <div className="flex justify-between items-center mb-4">
                <div className="text-sm text-gray-500">Manage user permissions</div>
                <div className="flex items-center space-x-4">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Select all permissions for all menus
                      setPermissions(prev => prev.map(permission => ({
                        ...permission,
                        canView: true,
                        canCreate: true,
                        canEdit: true,
                        canDelete: true
                      })));
                      toast.success('All permissions enabled');
                    }}
                  >
                    Select All
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Select only view permissions for all menus
                      setPermissions(prev => prev.map(permission => ({
                        ...permission,
                        canView: true,
                        // Set all to view-only
                        canCreate: false,
                        canEdit: false,
                        canDelete: false
                      })));
                      toast.success('View-only permissions enabled');
                    }}
                  >
                    View Only
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Get visible permissions (excluding dashboard)
                      const visiblePermissions = permissions.filter(p => p.menuId !== 'dashboard');

                      // Sort permissions by order
                      const sortedPermissions = [...visiblePermissions].sort((a, b) => (a.order || 999) - (b.order || 999));

                      // Find the first menu that's not dashboard
                      const firstMenu = sortedPermissions.length > 0 ? sortedPermissions[0] : null;

                      if (!firstMenu) {
                        toast.error('No menus available to deselect');
                        return;
                      }

                      // Keep only the first menu with view permission
                      setPermissions(prev => prev.map(permission => {
                        // Skip dashboard
                        if (permission.menuId === 'dashboard') {
                          return permission;
                        }

                        // First menu in the sorted list gets view permission, others get none
                        const isFirstMenu = permission.menuId === firstMenu.menuId;
                        return {
                          ...permission,
                          canView: isFirstMenu,
                          canCreate: false,
                          canEdit: false,
                          canDelete: false
                        };
                      }));

                      toast.success('All permissions deselected except one required view permission');
                    }}
                  >
                    Deselect All
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-5 gap-4 font-medium mb-2 pb-2 border-b">
                <div>Menu</div>
                <div className="text-center">View</div>
                <div className="text-center">Create</div>
                <div className="text-center">Edit</div>
                <div className="text-center">Delete</div>
              </div>

              {/* Sort permissions by order before rendering and filter out dashboard */}
              {[...permissions]
                .filter(permission => permission.menuId !== 'dashboard') // Remove dashboard from the list
                .sort((a, b) => (a.order || 999) - (b.order || 999))
                .map((permission) => (
                  <div key={permission.menuId} className="grid grid-cols-5 gap-4 py-2 border-b last:border-0">
                    <div>{permission.displayName}</div>

                    <div className="flex justify-center">
                      <Checkbox
                        checked={permission.canView}
                        onCheckedChange={(checked) =>
                          handlePermissionChange(permission.menuId, 'canView', checked === true)
                        }
                      />
                    </div>

                    <div className="flex justify-center">
                      <Checkbox
                        checked={permission.canCreate}
                        onCheckedChange={(checked) =>
                          handlePermissionChange(permission.menuId, 'canCreate', checked === true)
                        }
                        disabled={!permission.canView}
                      />
                    </div>

                    <div className="flex justify-center">
                      <Checkbox
                        checked={permission.canEdit}
                        onCheckedChange={(checked) =>
                          handlePermissionChange(permission.menuId, 'canEdit', checked === true)
                        }
                        disabled={!permission.canView}
                      />
                    </div>

                    <div className="flex justify-center">
                      <Checkbox
                        checked={permission.canDelete}
                        onCheckedChange={(checked) =>
                          handlePermissionChange(permission.menuId, 'canDelete', checked === true)
                        }
                        disabled={!permission.canView}
                      />
                    </div>
                  </div>
                ))}
            </div>
          )}
        </div>

        <div className="flex justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            className="mr-2 cursor-pointer"
            disabled={isLoading}
          >
            Cancel
          </Button>

          <Button type="submit" disabled={isLoading} className="cursor-pointer">
            {isLoading ? 'Saving...' : 'Save Permissions'}
          </Button>
        </div>
      </form>
    </div>
  );
}
