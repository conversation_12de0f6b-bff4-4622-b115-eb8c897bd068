import { refundReasons } from "../db/schema";
import { v4 as uuidv4 } from "uuid";
import { createClient } from '@libsql/client';
import { drizzle } from 'drizzle-orm/libsql';
import * as schema from '../db/schema';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Database client
const client = createClient({
  url: process.env.TURSO_DATABASE_URL as string,
  authToken: process.env.TURSO_AUTH_TOKEN as string,
});

// Create Drizzle ORM instance
const db = drizzle(client, { schema });

async function seedRefundReasons() {
  console.log("Seeding refund reasons...");

  // Define initial refund reasons
  const initialReasons = [
    {
      id: uuidv4(),
      name: "Damaged Product",
      description: "Product was damaged during handling or shipping",
      isActive: true
    },
    {
      id: uuidv4(),
      name: "Customer Dissatisfaction",
      description: "Customer was not satisfied with the product",
      isActive: true
    },
    {
      id: uuidv4(),
      name: "Wrong Product",
      description: "Customer received the wrong product",
      isActive: true
    },
    {
      id: uuidv4(),
      name: "Quality Issue",
      description: "Product has quality issues or defects",
      isActive: true
    },
    {
      id: uuidv4(),
      name: "Other",
      description: "Other reason for refund",
      isActive: true
    }
  ];

  try {
    // Check if reasons already exist
    const existingReasons = await db.query.refundReasons.findMany();

    if (existingReasons.length === 0) {
      // Insert reasons if none exist
      await db.insert(refundReasons).values(initialReasons);
      console.log(`Added ${initialReasons.length} refund reasons`);
    } else {
      console.log(`Refund reasons already exist. Found ${existingReasons.length} reasons.`);
    }

    console.log("Refund reasons seeding completed successfully");
  } catch (error) {
    console.error("Error seeding refund reasons:", error);
  }
}

// Run the seeding function
seedRefundReasons()
  .then(() => {
    console.log("Refund reasons seeding completed");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Error during refund reasons seeding:", error);
    process.exit(1);
  });
