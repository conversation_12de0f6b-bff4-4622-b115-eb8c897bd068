'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { use } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';

interface Vendor {
    id: string;
    name: string;
    email: string | null;
    phone: string | null;
    address: string | null;
    isActive: boolean;
    vendorCode: string | null;
    notes: string | null;
    createdAt: string;
    updatedAt: string;
    userId: string;
    tenantId: string | null;
    user?: {
        id: string;
        username: string;
        fullName: string;
        email: string;
    };
}

export default function VendorViewPage() {
    const router = useRouter();
    const params = useParams();
    const [vendor, setVendor] = useState<Vendor | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [userRole, setUserRole] = useState<string | null>(null);

    useEffect(() => {
        fetchUserRole();
        fetchVendor();
    }, []);

    const fetchUserRole = async () => {
        try {
            const response = await fetch('/api/auth/me');
            if (response.ok) {
                const data = await response.json();
                setUserRole(data.user?.role || null);
            }
        } catch (error) {
            console.error('Error fetching user role:', error);
        }
    };
    const fetchVendor = async () => {

        if (!params || !params.id) return;

        try {
            setLoading(true);
            const response = await fetch(`/api/vendors?id=${params.id}`);

            if (!response.ok) {
                throw new Error('Failed to fetch vendor details');
            }

            const data = await response.json();
            console.log(data);
            if (data.success) {
                setVendor(data.vendor);
            } else {
                setError(data.error || 'Failed to load vendor details');
            }
        } catch (err) {
            setError('An error occurred while fetching vendor details');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    const formatDate = (dateString: string) => {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
    };

    const handleGoBack = () => {
        router.push('/dashboard/vendors');
    };

    const handleEdit = () => {
        if (!params || !params.id) return;
        router.push(`/dashboard/vendors/edit/${params.id}`);
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">Error: </strong>
                    <span className="block sm:inline">{error}</span>
                </div>
                <Button onClick={handleGoBack} variant="outline" className='cursor-pointer'>Back to Vendors</Button>
            </div>
        );
    }
    console.log(vendor);
    if (!vendor) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">Notice: </strong>
                    <span className="block sm:inline">Vendor not found</span>
                </div>
                <Button onClick={handleGoBack} variant="outline" className='cursor-pointer'>Back to Vendors</Button>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold">Vendor Details</h1>
                <div className="space-x-2">
                    <Button onClick={handleEdit} variant="default" className="bg-blue-600 hover:bg-blue-700 text-white cursor-pointer">
                        Edit Vendor
                    </Button>
                    <Button onClick={handleGoBack} variant="outline" className='cursor-pointer'>
                        Back to Vendors
                    </Button>
                </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="col-span-1 md:col-span-2">
                    <CardHeader>
                        <CardTitle>General Information</CardTitle>
                        <CardDescription>Basic details about the vendor</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <Label className="text-sm font-medium text-gray-500 dark:text-gray-400">Name</Label>
                                <p className="mt-1 text-lg font-semibold">{vendor.name}</p>
                            </div>
                            <div>
                                <Label className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</Label>
                                <p className="mt-1">
                                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${vendor.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                        {vendor.isActive ? 'Active' : 'Inactive'}
                                    </span>
                                </p>
                            </div>
                            <div>
                                <Label className="text-sm font-medium text-gray-500 dark:text-gray-400">Vendor Code</Label>
                                <p className="mt-1">{vendor.vendorCode || 'Not assigned'}</p>
                            </div>
                            <div>
                                <Label className="text-sm font-medium text-gray-500 dark:text-gray-400">Created</Label>
                                <p className="mt-1">{formatDate(vendor.createdAt)}</p>
                            </div>
                            {vendor.updatedAt && (
                                <div>
                                    <Label className="text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</Label>
                                    <p className="mt-1">{formatDate(vendor.updatedAt)}</p>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                <Card className="col-span-1">
                    <CardHeader>
                        <CardTitle>Contact Information</CardTitle>
                        <CardDescription>How to reach this vendor</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        {userRole === 'admin' && (
                            <>
                                <div>
                                    <Label className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</Label>
                                    <p className="mt-1">{vendor.email || 'Not specified'}</p>
                                </div>
                                <div>
                                    <Label className="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</Label>
                                    <p className="mt-1">{vendor.phone || 'Not specified'}</p>
                                </div>
                            </>
                        )}
                        <div>
                            <Label className="text-sm font-medium text-gray-500 dark:text-gray-400">Address</Label>
                            <p className="mt-1">{vendor.address || 'Not specified'}</p>
                        </div>
                    </CardContent>
                </Card>

                {vendor.notes && (
                    <Card className="col-span-1 md:col-span-3">
                        <CardHeader>
                            <CardTitle>Notes</CardTitle>
                            <CardDescription>Additional information</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <p className="whitespace-pre-line">{vendor.notes}</p>
                        </CardContent>
                    </Card>
                )}

                {vendor.user && (
                    <Card className="col-span-1 md:col-span-3">
                        <CardHeader>
                            <CardTitle>Associated User</CardTitle>
                            <CardDescription>User connected to this vendor</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <Label className="text-sm font-medium text-gray-500 dark:text-gray-400">Name</Label>
                                    <p className="mt-1">{vendor.user.fullName}</p>
                                </div>
                                <div>
                                    <Label className="text-sm font-medium text-gray-500 dark:text-gray-400">Username</Label>
                                    <p className="mt-1">{vendor.user.username}</p>
                                </div>
                                <div>
                                    <Label className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</Label>
                                    <p className="mt-1">{vendor.user.email}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </div>
    );
}