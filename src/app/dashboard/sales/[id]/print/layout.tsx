import './global.css'

export const metadata = {
    title: 'Print Invoice',
}

// Force dynamic rendering
export const dynamic = "force-dynamic";

// This layout completely replaces the dashboard layout (not nested)
export default function PrintLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return (
        <html lang="en" className="print-page">
            <head>
                <style dangerouslySetInnerHTML={{
                    __html: `
                    /* Reset any parent layout styles */
                    #__next, body > div {
                        all: unset !important;
                        display: block !important;
                    }
                    .sidebar, header, nav, footer, aside {
                        display: none !important;
                    }
                `}} />
            </head>
            <body style={{ margin: 0, padding: 0, display: 'block', width: '100%', background: 'white' }}>
                {children}
            </body>
        </html>
    );
} 