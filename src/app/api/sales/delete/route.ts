import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { orders, orderItems, payments, inventoryTransactions, royaltyTransactions } from "@/db/schema";
import { auth } from "../../../../../auth";
import { eq, and } from "drizzle-orm";
import { withMenuPermission } from "@/middleware/menuPermissions";

async function postHandler(
  request: NextRequest,
  context: any,
  permissionCheck: any
) {
  console.log('POST handler called for sale deletion');
  try {
    const session = await auth();
    console.log('Session:', session?.user?.id ? 'Authenticated' : 'Not authenticated', 'Role:', session?.user?.role);

    if (!session?.user?.id) {
      console.log('Unauthorized: No user ID in session');
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Check if user has delete permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      console.log('Checking permissions for tenant_sale user');
      const menuPermission = permissionCheck.permissions.find(
        (p: any) => p.menu.path === '/dashboard/sales'
      );
      console.log('Menu permission found:', menuPermission ? 'Yes' : 'No', 'Can delete:', menuPermission?.canDelete);

      if (!menuPermission?.canDelete) {
        console.log('Permission denied: User does not have delete permission');
        return NextResponse.json(
          { success: false, error: "You do not have permission to delete sales" },
          { status: 403 }
        );
      }
    }

    const tenantId = session.user.tenantId || session.user.id;
    console.log('Tenant ID:', tenantId);

    if (!tenantId) {
      console.log('Tenant not found');
      return NextResponse.json(
        { message: "Tenant not found" },
        { status: 404 }
      );
    }

    // Get the order ID from the request body
    const body = await request.json();
    const { orderId } = body;
    console.log('Sale ID to delete:', orderId);

    if (!orderId) {
      console.log('Order ID is required but not provided');
      return NextResponse.json(
        { success: false, message: "Order ID is required" },
        { status: 400 }
      );
    }

    // Check if order exists and belongs to this tenant
    console.log('Checking if order exists for tenant:', tenantId);
    const order = await db.query.orders.findFirst({
      where: and(eq(orders.id, orderId), eq(orders.tenantId, tenantId)),
      with: {
        items: true
      }
    });

    if (!order) {
      console.log('Order not found for this tenant');
      return NextResponse.json({ success: false, message: "Order not found" }, { status: 404 });
    }

    console.log('Order found, proceeding with deletion');

    try {
      // Use a transaction to ensure all related records are deleted
      await db.transaction(async (tx) => {
        console.log('Starting transaction for deletion');

        // 1. First, delete any payment records associated with this order
        console.log('Deleting payment records for order:', orderId);
        await tx.delete(payments).where(eq(payments.orderId, orderId));

        // 2. Delete any royalty transactions related to this sale
        console.log('Deleting royalty transactions for order:', orderId);
        // Find inventory transactions related to this sale
        const inventoryTxs = await tx.query.inventoryTransactions.findMany({
          where: and(
            eq(inventoryTransactions.type, 'sale'),
            eq(inventoryTransactions.notes, `Sale: ${order.memoNo}`)
          )
        });
        
        // Delete royalty transactions for each inventory transaction
        for (const invTx of inventoryTxs) {
          await tx.delete(royaltyTransactions).where(eq(royaltyTransactions.transactionId, invTx.id));
        }

        // 3. Delete inventory transactions related to this sale
        console.log('Deleting inventory transactions for order:', orderId);
        await tx.delete(inventoryTransactions)
          .where(
            and(
              eq(inventoryTransactions.type, 'sale'),
              eq(inventoryTransactions.notes, `Sale: ${order.memoNo}`)
            )
          );

        // 4. Delete order items
        console.log('Deleting order items for order:', orderId);
        await tx.delete(orderItems).where(eq(orderItems.orderId, orderId));

        // 5. Finally, delete the order
        console.log('Deleting order:', orderId);
        await tx.delete(orders).where(eq(orders.id, orderId));
      });

      console.log('Order and all related records deleted successfully');
      return NextResponse.json({ success: true, message: "Sale deleted successfully" });
    } catch (deleteError) {
      console.error('Error during delete operation:', deleteError);
      throw deleteError; // Re-throw to be caught by the outer try/catch
    }
  } catch (error) {
    console.error("Error deleting order:", error);

    // Log detailed error information
    if (error instanceof Error) {
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
    } else {
      console.error("Unknown error type:", error);
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to delete sale",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}

// Export the route handlers with permission middleware
export const POST = withMenuPermission(postHandler, '/dashboard/sales');

// For debugging purposes, log when the module is loaded
console.log('Sales delete route handler registered - POST');
