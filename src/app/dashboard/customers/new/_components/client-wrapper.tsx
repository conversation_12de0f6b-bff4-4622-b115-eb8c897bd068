'use client';

import { useEffect } from 'react';
import { usePermissionCheck } from '@/lib/check-permissions';
import { CustomerForm } from './customer-form';

interface ClientWrapperProps {
  tenantId: string;
}

export default function ClientWrapper({ tenantId }: ClientWrapperProps) {
  // Check if user has permission to create customers
  const { isLoading } = usePermissionCheck('/dashboard/customers', 'create');
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }
  
  return <CustomerForm tenantId={tenantId} />;
}
