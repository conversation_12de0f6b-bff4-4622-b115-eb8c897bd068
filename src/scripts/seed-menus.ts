import { userMenuPermissions } from '../db/schema';
import { createClient } from '@libsql/client';
import { drizzle } from 'drizzle-orm/libsql';
import * as schema from '../db/schema';
import * as dotenv from 'dotenv';
import { ALL_MENUS } from '../lib/menu-list';
import { eq } from 'drizzle-orm';

// Load environment variables
dotenv.config({ path: '.env' });

// Database client
const client = createClient({
  url: process.env.TURSO_DATABASE_URL as string,
  authToken: process.env.TURSO_AUTH_TOKEN as string,
});

// Create Drizzle ORM instance
const db = drizzle(client, { schema });

// This script is now used to update existing permissions to use the file-based menu IDs
async function updateMenuPermissions() {
  try {
    console.log('Updating menu permissions to use file-based menu IDs...');

    // Get all menu permissions
    const permissions = await db.select().from(userMenuPermissions).all();

    console.log(`Found ${permissions.length} permissions to update.`);

    // Create a map of menu IDs from the file
    const menuMap = new Map();
    ALL_MENUS.forEach(menu => {
      menuMap.set(menu.name, menu.id);
    });

    // Update each permission to use the file-based menu ID
    let updatedCount = 0;
    for (const permission of permissions) {
      try {
        // Find the menu in ALL_MENUS by name (assuming menuId might be a name or path)
        const menuName = permission.menuId.toLowerCase().replace(/[^a-z0-9_]/g, '_');
        const fileMenuId = menuMap.get(menuName);

        if (fileMenuId) {
          // Update the permission to use the file-based menu ID
          await db.update(userMenuPermissions)
            .set({ menuId: fileMenuId })
            .where(eq(userMenuPermissions.id, permission.id));

          updatedCount++;
        }
      } catch (err) {
        console.error(`Error updating permission ${permission.id}:`, err);
      }
    }

    console.log(`Successfully updated ${updatedCount} permissions to use file-based menu IDs.`);
  } catch (error) {
    console.error('Error updating menu permissions:', error);
  }
}

// Run the update function
updateMenuPermissions()
  .then(() => {
    console.log('Menu permissions update complete.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error during menu permissions update:', error);
    process.exit(1);
  });
