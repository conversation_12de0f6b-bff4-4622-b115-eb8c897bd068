'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { formatDate, formatCurrency } from '@/lib/utils';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface RefundItem {
  id: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  total: number;
  isDamaged: boolean;
  product: {
    id: string;
    name: string;
    code: string | null;
  };
}

interface Refund {
  id: string;
  tenantId: string;
  branchId: string;
  orderId: string | null;
  customerId: string | null;
  reasonId: string;
  refundDate: string;
  totalAmount: number;
  notes: string | null;
  isDamaged: boolean;
  performedBy: string;
  createdAt: string;
  updatedAt: string;
  customer: {
    id: string;
    name: string;
  } | null;
  order: {
    id: string;
    memoNo: string;
  } | null;
  reason: {
    id: string;
    name: string;
  };
  items: RefundItem[];
}

export default function RefundsPage() {
  const router = useRouter();
  const [refunds, setRefunds] = useState<Refund[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filteredRefunds, setFilteredRefunds] = useState<Refund[]>([]);

  useEffect(() => {
    fetchRefunds();
  }, []);

  useEffect(() => {
    filterRefunds();
  }, [searchTerm, refunds]);

  const fetchRefunds = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/refunds');

      if (!response.ok) {
        throw new Error(`Failed to fetch refunds: ${response.statusText}`);
      }

      try {
        const data = await response.json();

        if (data.success) {
          setRefunds(data.refunds || []);
        } else {
          setError(data.error || 'Failed to load refunds');
        }
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError);
        setError('Invalid response format from server');
      }
    } catch (error) {
      console.error('Error fetching refunds:', error);
      setError('An error occurred while fetching refunds');
    } finally {
      setLoading(false);
    }
  };

  const filterRefunds = () => {
    if (!searchTerm) {
      setFilteredRefunds(refunds);
      return;
    }

    const lowerSearchTerm = searchTerm.toLowerCase();
    const filtered = refunds.filter(refund => {
      // Search by customer name
      const customerMatch = refund.customer?.name.toLowerCase().includes(lowerSearchTerm);

      // Search by order memo number
      const orderMatch = refund.order?.memoNo.toLowerCase().includes(lowerSearchTerm);

      // Search by reason
      const reasonMatch = refund.reason.name.toLowerCase().includes(lowerSearchTerm);

      // Search by product name in items
      const productMatch = refund.items.some(item =>
        item.product.name.toLowerCase().includes(lowerSearchTerm) ||
        (item.product.code && item.product.code.toLowerCase().includes(lowerSearchTerm))
      );

      return customerMatch || orderMatch || reasonMatch || productMatch;
    });

    setFilteredRefunds(filtered);
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const viewRefundDetails = (refundId: string) => {
    router.push(`/dashboard/refunds/${refundId}`);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Refunds Management</h1>
        <div className="flex space-x-2">
          <Link href="/dashboard/refunds/new">
            <Button className="bg-blue-500 hover:bg-blue-600 text-white cursor-pointer">
              Process New Refund
            </Button>
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Search Refunds</CardTitle>
          <CardDescription>
            Search by customer name, order number, reason, or product
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <input
              type="text"
              placeholder="Search refunds..."
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Refund Transactions</CardTitle>
          <CardDescription>
            {searchTerm
              ? `Search results for "${searchTerm}"`
              : "Displaying all refund transactions"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-4">Loading refunds...</div>
          ) : filteredRefunds.length === 0 ? (
            <div className="text-center py-4">
              {searchTerm ? "No refunds found matching your search." : "No refunds have been processed yet."}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Order #</TableHead>
                    <TableHead>Reason</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                    <TableHead className="text-center">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRefunds.map((refund) => (
                    <TableRow key={refund.id}>
                      <TableCell>{formatDate(refund.refundDate)}</TableCell>
                      <TableCell>{refund.customer?.name || 'N/A'}</TableCell>
                      <TableCell>{refund.order?.memoNo || 'N/A'}</TableCell>
                      <TableCell>{refund.reason.name}</TableCell>
                      <TableCell>
                        <Badge
                          className={refund.isDamaged ? "bg-red-500" : "bg-green-500"}
                        >
                          {refund.isDamaged ? 'Damaged' : 'Refunded'}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">{formatCurrency(refund.totalAmount)}</TableCell>
                      <TableCell className="text-center">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => viewRefundDetails(refund.id)}
                        >
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
