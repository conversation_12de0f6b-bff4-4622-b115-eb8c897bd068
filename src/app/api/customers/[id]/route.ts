import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { customers, userMenuPermissions, orders } from "@/db/schema";
import { auth } from "~/auth";
import { eq, and, or } from "drizzle-orm";
import { withMenuPermission, checkMenuPermission } from "@/middleware/menuPermissions";

async function getHandler(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
  permissionCheck: any
) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Check if user has view permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      const menuPermission = permissionCheck.permissions.find(
        (p: any) => p.menu.path === '/dashboard/customers'
      );

      if (!menuPermission?.canView) {
        return NextResponse.json(
          { success: false, error: "You do not have permission to view customers" },
          { status: 403 }
        );
      }
    }

    const { id: customerId } = await params;
    const tenantId = session.user.tenantId || session.user.id;

    const customer = await db.query.customers.findFirst({
      where: (customers, { eq, and }) =>
        and(eq(customers.id, customerId), eq(customers.tenantId, tenantId)),
    });

    if (!customer) {
      return NextResponse.json(
        { message: "Customer not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(customer);
  } catch (error) {
    console.error("Error getting customer:", error);
    return NextResponse.json(
      { message: "Failed to get customer", error: (error as Error).message },
      { status: 500 }
    );
  }
}

async function putHandler(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
  permissionCheck: any
) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Check if user has edit permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      const menuPermission = permissionCheck.permissions.find(
        (p: any) => p.menu.path === '/dashboard/customers'
      );

      if (!menuPermission?.canEdit) {
        return NextResponse.json(
          { success: false, error: "You do not have permission to edit customers" },
          { status: 403 }
        );
      }
    }

    const { id: customerId } = await params;
    const tenantId = session.user.tenantId || session.user.id;
    const data = await request.json();

    // Validate required fields
    if (!data.name || !data.type) {
      return NextResponse.json(
        { message: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if customer exists and belongs to tenant
    const existingCustomer = await db.query.customers.findFirst({
      where: (customers, { eq, and }) =>
        and(eq(customers.id, customerId), eq(customers.tenantId, tenantId)),
    });

    if (!existingCustomer) {
      return NextResponse.json(
        { message: "Customer not found" },
        { status: 404 }
      );
    }

    // Check if customer code is provided and if it's unique
    if (data.code && data.code.trim() !== '') {
      // Check if a customer with the same code already exists for this tenant (excluding the current customer)
      const duplicateCodeCustomer = await db.query.customers.findFirst({
        where: (customers, { eq, and, ne }) =>
          and(
            eq(customers.tenantId, tenantId),
            eq(customers.code, data.code),
            ne(customers.id, customerId) // Exclude the current customer
          ),
      });

      if (duplicateCodeCustomer) {
        return NextResponse.json(
          {
            message: "Customer code must be unique",
            code: "DUPLICATE_CODE"
          },
          { status: 400 }
        );
      }
    }

    // Update the customer
    await db
      .update(customers)
      .set({
        name: data.name,
        code: data.code || null,
        phone: data.phone || null,
        email: data.email || null,
        address: data.address || null,
        type: data.type,
        creditLimit: data.creditLimit || 0,
        extraCommission: data.extraCommission || 0,
        notes: data.notes || null,
        updatedAt: new Date(),
      })
      .where(
        and(eq(customers.id, customerId), eq(customers.tenantId, tenantId))
      );

    return NextResponse.json(
      { message: "Customer updated successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating customer:", error);
    return NextResponse.json(
      { message: "Failed to update customer", error: (error as Error).message },
      { status: 500 }
    );
  }
}

async function deleteHandler(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
  permissionCheck: any
) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Check if user has delete permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      const menuPermission = permissionCheck.permissions.find(
        (p: any) => p.menu.path === '/dashboard/customers'
      );

      if (!menuPermission?.canDelete) {
        return NextResponse.json(
          { success: false, error: "You do not have permission to delete customers" },
          { status: 403 }
        );
      }
    }

    const { id: customerId } = await params;
    const tenantId = session.user.tenantId || session.user.id;

    // Check if customer exists and belongs to tenant
    const existingCustomer = await db.query.customers.findFirst({
      where: (customers, { eq, and }) =>
        and(eq(customers.id, customerId), eq(customers.tenantId, tenantId)),
    });

    if (!existingCustomer) {
      return NextResponse.json(
        { message: "Customer not found" },
        { status: 404 }
      );
    }

    // Check if customer has balance due (positive balance)
    // Negative balance means customer has advanced payment, which is okay for deletion
    if (
      existingCustomer.currentBalance &&
      existingCustomer.currentBalance > 0
    ) {
      return NextResponse.json(
        { message: "Cannot delete customer with outstanding balance" },
        { status: 400 }
      );
    }

    // Check if customer has advanced payment (negative balance)
    if (
      existingCustomer.currentBalance &&
      existingCustomer.currentBalance < 0
    ) {
      return NextResponse.json(
        { message: "Cannot delete customer with advanced payment balance" },
        { status: 400 }
      );
    }

    // Soft delete the customer by setting isActive to false
    await db
      .update(customers)
      .set({
        isActive: false,
        updatedAt: new Date()
      })
      .where(
        and(eq(customers.id, customerId), eq(customers.tenantId, tenantId))
      );

    return NextResponse.json(
      { message: "Customer deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting customer:", error);
    return NextResponse.json(
      { message: "Failed to delete customer", error: (error as Error).message },
      { status: 500 }
    );
  }
}

// Export the route handlers with permission middleware
export const GET = withMenuPermission(getHandler, '/dashboard/customers');
export const PUT = withMenuPermission(putHandler, '/dashboard/customers');
export const DELETE = withMenuPermission(deleteHandler, '/dashboard/customers');