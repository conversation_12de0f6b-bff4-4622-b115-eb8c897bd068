import { NextRequest, NextResponse } from "next/server";
import { auth } from "~/auth";
import { db } from "@/lib/db";
import { products, inventoryTransactions, branches } from "@/db/schema";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import { eq, and, sum } from "drizzle-orm";

// Schema for validating stock transfers
const transferSchema = z.object({
  productId: z.string().min(1, "Product ID is required"),
  quantity: z.number().int().min(1, "Quantity must be at least 1"),
  notes: z.string().nullable().optional(),
});

// POST /api/inventory/transfers - Transfer stock from warehouse to store
export async function POST(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Parse and validate the request body
    const body = await req.json();
    console.log("Received transfer request:", body);

    const validationResult = transferSchema.safeParse(body);

    if (!validationResult.success) {
      console.error("Validation failed:", validationResult.error.errors);
      return NextResponse.json(
        { success: false, error: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { productId, quantity, notes } = validationResult.data;

    // Get tenant ID from session based on user role
    let tenantId: string;
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      // For sales users, use their associated tenant's ID
      tenantId = session.user.tenantId;
    } else {
      // For tenant users, use their own ID
      tenantId = session.user.id;
    }
    console.log('POST /api/inventory/transfers - Using tenant ID:', tenantId, 'for user role:', session.user.role);

    // Check if product exists and belongs to the tenant
    const product = await db.query.products.findFirst({
      where: and(eq(products.id, productId), eq(products.tenantId, tenantId)),
    });

    if (!product) {
      return NextResponse.json(
        {
          success: false,
          error: "Product not found or does not belong to this tenant",
        },
        { status: 404 }
      );
    }

    // Calculate current warehouse stock
    const inStock = await db
      .select({
        warehouseTotal: sum(inventoryTransactions.warehouseQuantity),
      })
      .from(inventoryTransactions)
      .where(
        and(
          eq(inventoryTransactions.type, "in"),
          eq(inventoryTransactions.productId, productId),
          eq(inventoryTransactions.tenantId, tenantId)
        )
      );

    const outStock = await db
      .select({
        warehouseTotal: sum(inventoryTransactions.warehouseQuantity),
      })
      .from(inventoryTransactions)
      .where(
        and(
          eq(inventoryTransactions.type, "out"),
          eq(inventoryTransactions.productId, productId),
          eq(inventoryTransactions.tenantId, tenantId)
        )
      );

    const warehouseIn = Number(inStock[0]?.warehouseTotal || 0);
    const warehouseOut = Number(outStock[0]?.warehouseTotal || 0);
    const currentWarehouseStock = warehouseIn - warehouseOut;

    // Check if there's enough stock in warehouse for transfer
    if (quantity > currentWarehouseStock) {
      return NextResponse.json(
        {
          success: false,
          error: `Not enough stock in warehouse. Available: ${currentWarehouseStock}`,
        },
        { status: 400 }
      );
    }

    // Create two transactions: one to reduce warehouse stock, one to increase store stock
    const transactionId = uuidv4();

    // Get the branches for this tenant
    const tenantBranches = await db
      .select()
      .from(branches)
      .where(eq(branches.tenantId, tenantId));

    // Find warehouse and store branches
    let warehouseBranch = tenantBranches.find(b =>
      b.name.toLowerCase().includes('warehouse') ||
      b.code.toLowerCase().includes('wh'));

    let storeBranch = tenantBranches.find(b =>
      b.name.toLowerCase().includes('store') ||
      b.code.toLowerCase().includes('st'));

    // If no specific branches found, use the first branch for both (or create them if needed)
    if (!warehouseBranch || !storeBranch) {
      if (tenantBranches.length === 0) {
        // No branches exist, create default branches
        const warehouseBranchId = uuidv4();
        const storeBranchId = uuidv4();

        // Create warehouse branch
        await db.insert(branches).values({
          id: warehouseBranchId,
          tenantId,
          name: "Warehouse",
          code: "WH",
          isMain: false,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        // Create store branch
        await db.insert(branches).values({
          id: storeBranchId,
          tenantId,
          name: "Store",
          code: "ST",
          isMain: true,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        warehouseBranch = {
          id: warehouseBranchId,
          tenantId,
          name: "Warehouse",
          code: "WH",
          address: null,
          phone: null,
          email: null,
          isMain: false,
          isActive: true,
          notes: null,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        storeBranch = {
          id: storeBranchId,
          tenantId,
          name: "Store",
          code: "ST",
          address: null,
          phone: null,
          email: null,
          isMain: true,
          isActive: true,
          notes: null,
          createdAt: new Date(),
          updatedAt: new Date()
        };
      } else {
        // Use the first branch for both if specific branches not found
        warehouseBranch = tenantBranches[0];
        storeBranch = tenantBranches[0];
      }
    }

    // First, decrease warehouse stock (out transaction for warehouse)
    await db.insert(inventoryTransactions).values({
      id: transactionId,
      tenantId,
      branchId: warehouseBranch.id, // Use actual warehouse branch ID
      productId,
      quantity: 0, // Total remains unchanged
      warehouseQuantity: quantity, // Reduce warehouse by quantity
      storeQuantity: 0, // Store unchanged in this transaction
      unitPrice: product.costPrice,
      type: "out", // Out from warehouse
      date: new Date(),
      notes: notes || `Transfer from ${warehouseBranch.name || 'Warehouse'} to ${storeBranch.name || 'Store'}`,
      performedBy: session.user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Second, increase store stock (in transaction for store)
    const inTransactionId = uuidv4();
    await db.insert(inventoryTransactions).values({
      id: inTransactionId,
      tenantId,
      branchId: storeBranch.id, // Use actual store branch ID
      productId,
      quantity: 0, // Total remains unchanged
      warehouseQuantity: 0, // Warehouse unchanged in this transaction
      storeQuantity: quantity, // Increase store by quantity
      unitPrice: product.costPrice,
      type: "in", // In to store
      date: new Date(),
      notes: notes || `Transfer from ${warehouseBranch.name || 'Warehouse'} to ${storeBranch.name || 'Store'}`,
      performedBy: session.user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return NextResponse.json({
      success: true,
      message: "Stock successfully transferred from warehouse to store",
      quantity,
      transactionId,
      inTransactionId,
    });
  } catch (error) {
    console.error("Error transferring stock:", error);
    return NextResponse.json(
      { success: false, error: "Failed to transfer stock" },
      { status: 500 }
    );
  }
}
