import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users, tenants, tenantUsers } from "@/db/schema";
import { eq } from "drizzle-orm";

export async function POST(request: Request) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { success: false, error: "Email is required" },
        { status: 400 }
      );
    }

    // Find user by email
    const user = await db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .get();

    if (!user) {
      return NextResponse.json(
        { success: false, error: "User not found" },
        { status: 404 }
      );
    }

    // Check if user is active regardless of role
    if (!user.isActive) {
      return NextResponse.json(
        {
          success: false,
          error: "Your account has been deactivated. Please contact your administrator.",
          status: "inactive"
        },
        { status: 403 }
      );
    }

    // Check tenant status based on user role
    if (user.role === "tenant") {
      // Get tenant directly
      const tenant = await db
        .select()
        .from(tenants)
        .where(eq(tenants.userId, user.id))
        .get();

      if (!tenant) {
        return NextResponse.json(
          { success: false, error: "Tenant not found" },
          { status: 404 }
        );
      }

      if (!tenant.isActive) {
        return NextResponse.json(
          {
            success: false,
            error: "Your tenant account has been deactivated. Please contact admin for assistance.",
            status: "inactive"
          },
          { status: 403 }
        );
      }

      // Check subscription
      const now = new Date();
      if (tenant.subscriptionEndDate && tenant.subscriptionEndDate < now) {
        return NextResponse.json(
          {
            success: false,
            error: "Your subscription has expired. Please contact admin to renew your subscription.",
            status: "expired"
          },
          { status: 403 }
        );
      }
    } else if (user.role === "tenant_sale") {
      // Find the associated tenant
      const tenantUser = await db
        .select()
        .from(tenantUsers)
        .where(eq(tenantUsers.userId, user.id))
        .get();

      if (!tenantUser) {
        return NextResponse.json(
          { success: false, error: "Tenant association not found" },
          { status: 404 }
        );
      }

      // Get the tenant owner
      const tenantOwner = await db
        .select()
        .from(users)
        .where(eq(users.id, tenantUser.tenantId))
        .get();

      if (!tenantOwner) {
        return NextResponse.json(
          { success: false, error: "Tenant owner not found" },
          { status: 404 }
        );
      }

      // Get tenant details
      const tenant = await db
        .select()
        .from(tenants)
        .where(eq(tenants.userId, tenantOwner.id))
        .get();

      if (!tenant) {
        return NextResponse.json(
          { success: false, error: "Tenant details not found" },
          { status: 404 }
        );
      }

      if (!tenant.isActive) {
        return NextResponse.json(
          {
            success: false,
            error: "Your tenant account has been deactivated. Please contact admin for assistance.",
            status: "inactive"
          },
          { status: 403 }
        );
      }

      // Check subscription
      const now = new Date();
      if (tenant.subscriptionEndDate && tenant.subscriptionEndDate < now) {
        return NextResponse.json(
          {
            success: false,
            error: "Your subscription has expired. Please contact admin to renew your subscription.",
            status: "expired"
          },
          { status: 403 }
        );
      }
    }

    // If we get here, the user is active and subscription is valid
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error checking user status:", error);
    return NextResponse.json(
      { success: false, error: "Failed to check user status" },
      { status: 500 }
    );
  }
}
