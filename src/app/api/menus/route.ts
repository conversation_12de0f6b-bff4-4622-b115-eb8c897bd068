import { NextRequest, NextResponse } from "next/server";
import { auth } from "~/auth";
import { ALL_MENUS } from "@/lib/menu-list";

export async function GET(request: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await auth();

    // Only tenant users can access this endpoint
    if (!session?.user || session.user.role !== 'tenant') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Only tenant admins can access menus.' },
        { status: 403 }
      );
    }

    // Get all menus from file
    const activeMenus = ALL_MENUS.filter(menu => menu.isActive)
      .sort((a, b) => (a.order || 999) - (b.order || 999));

    return NextResponse.json({
      success: true,
      menus: activeMenus
    });
  } catch (error) {
    console.error('Error fetching menus:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch menus' },
      { status: 500 }
    );
  }
}
