import { NextRequest, NextResponse } from "next/server";
import { auth } from "~/auth";
import { ALL_MENUS } from "@/lib/menu-list";

// GET /api/debug/menus - Get all menus for debugging
export async function GET(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get all menus from file
    const allMenus = ALL_MENUS.sort((a, b) => (a.order || 999) - (b.order || 999));

    return NextResponse.json({
      success: true,
      menus: allMenus
    });
  } catch (error) {
    console.error("Error fetching menus:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch menus" },
      { status: 500 }
    );
  }
}
