'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useToast } from '@/components/ui/use-toast';
import { useSession } from 'next-auth/react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Toast } from '@/components/ui/toast';
import { toast as sonnerToast } from 'sonner';
import { Eye, Edit, History, Trash } from 'lucide-react';

interface Category {
    id: string;
    name: string;
}

interface Product {
    id: string;
    name: string;
    code: string | null;
    description: string | null;
    costPrice: number;
    sellingPrice: number;
    discount: number;
    unit: string;
    stock?: number; // From inventory
    categoryId: string | null;
    category: Category | null;
    vendorId: string | null;
    royaltyType: 'none' | 'fixed' | 'percentage';
    royaltyValue: number;
    isActive: boolean;
    lastStockUpdate?: Date | null;
    createdAt: string;
    updatedAt: string;
}

export default function ProductsPage() {
    const router = useRouter();
    const { toast } = useToast();
    const { data: session } = useSession();
    const [userPermissions, setUserPermissions] = useState<{
        canView: boolean;
        canCreate: boolean;
        canEdit: boolean;
        canDelete: boolean;
    }>({ canView: false, canCreate: false, canEdit: false, canDelete: false });
    const [products, setProducts] = useState<Product[]>([]);
    const [categories, setCategories] = useState<Category[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string>('');
    const [searchTerm, setSearchTerm] = useState<string>('');
    const [selectedCategory, setSelectedCategory] = useState<string>('');
    const [showInactiveProducts, setShowInactiveProducts] = useState<boolean>(false);

    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [productToDelete, setProductToDelete] = useState<Product | null>(null);

    useEffect(() => {
        fetchProducts();

        // Only fetch categories if user is not tenant_sale or has permission
        if (session?.user?.role !== 'tenant_sale' ||
            (session?.user?.menuPermissions?.some(
                (p: any) => p.path === '/dashboard/products/categories' && p.canView
            ))
        ) {
            fetchCategories();
        }
    }, [session]);

    useEffect(() => {
        if (session) {
            fetchUserPermissions();
        }
    }, [session]);

    // Fetch user permissions
    const fetchUserPermissions = async () => {
        try {
            if (!session) {
                return; // Wait for session to be loaded
            }

            if (session?.user?.role === 'admin' || session?.user?.role === 'tenant') {
                // Admin and tenant users have full permissions
                setUserPermissions({
                    canView: true,
                    canCreate: true,
                    canEdit: true,
                    canDelete: true
                });
                return;
            }

            // For tenant_sale users, first check if permissions are in the session
            if (session.user.role === 'tenant_sale' && session.user.menuPermissions && session.user.menuPermissions.length > 0) {
                console.log('Using menu permissions from session for products page');
                const menuPermission = session.user.menuPermissions.find(
                    (p: any) => p.path === '/dashboard/products'
                );

                if (menuPermission) {
                    setUserPermissions({
                        canView: menuPermission.canView || false,
                        canCreate: menuPermission.canCreate || false,
                        canEdit: menuPermission.canEdit || false,
                        canDelete: menuPermission.canDelete || false
                    });
                    return;
                }
            }

            // Fallback to API call if permissions are not in session
            const response = await fetch(`/api/user/permissions?menuPath=/dashboard/products`);
            const data = await response.json();

            if (data.success) {
                setUserPermissions(data.permissions);
            } else {
                console.error('Error fetching permissions:', data.error);
                sonnerToast.error('Failed to load permissions');
            }
        } catch (error) {
            console.error('Error fetching user permissions:', error);
            sonnerToast.error('Failed to load user permissions');
        }
    };

    const fetchProducts = async (categoryId?: string, search?: string, showInactive?: boolean) => {
        try {
            setLoading(true);
            setError('');

            let url = '/api/products';
            const params = new URLSearchParams();

            if (categoryId) {
                params.append('categoryId', categoryId);
            }

            if (search) {
                params.append('search', search);
            }

            // If showInactive is true, we want to show only inactive products
            // If showInactive is false, we want to show only active products
            if (showInactive) {
                params.append('isInactiveOnly', 'true');
            } else {
                params.append('isActiveOnly', 'true');
            }

            if (params.toString()) {
                url += `?${params.toString()}`;
            }

            console.log('Fetching products from:', url, 'showInactive:', showInactive);

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache',
                },
                credentials: 'include', // Ensure cookies are sent with the request
            });

            console.log('Products API response status:', response.status);

            // Get the response text for debugging
            const responseText = await response.text();
            console.log('Response text length:', responseText.length);

            if (!responseText || responseText.trim() === '') {
                throw new Error('Empty response received from products API');
            }

            // Try to parse as JSON or log the text if it fails
            let data;
            try {
                data = JSON.parse(responseText);
                console.log('Products response data structure:', Object.keys(data));
            } catch (e) {
                console.error('Failed to parse products response as JSON:', responseText);
                throw new Error('Invalid response format from products API');
            }

            if (data.success) {
                setProducts(data.products || []);
                console.log('Loaded products count:', data.products?.length || 0);
            } else {
                // Handle specific error cases with user-friendly messages
                if (data.error.includes("permission")) {
                    // Permission-related errors
                    setError(`Access Denied: ${data.error}. Please contact your administrator to request access.`);
                    sonnerToast.error(`Access Denied: ${data.error}`, {
                        description: "Please contact your administrator to request access."
                    });
                } else if (response.status === 401) {
                    // Authentication errors
                    setError("Your session has expired. Please log in again.");
                    sonnerToast.error("Authentication Error", {
                        description: "Your session has expired. Please log in again."
                    });
                } else {
                    // Generic errors
                    setError(data.error || 'Failed to load products data');
                    sonnerToast.error("Error", {
                        description: data.error || 'Failed to load products data'
                    });
                }
                console.error('API returned error:', data.error);
            }
        } catch (err) {
            console.error('Error fetching products:', err);
            const errorMessage = typeof err === 'object' && err !== null && 'message' in err
                ? (err as Error).message
                : 'An error occurred while fetching products data';

            setError(errorMessage);
            sonnerToast.error("Error Loading Products", {
                description: errorMessage
            });
        } finally {
            setLoading(false);
        }
    };

    const fetchCategories = async () => {
        try {
            // Skip categories fetch for tenant_sale users if they don't have permission
            // This is a workaround to avoid the error message
            if (session?.user?.role === 'tenant_sale') {
                // Check if user has permission to access categories
                const hasCategoriesPermission = session.user.menuPermissions?.some(
                    (p: any) => p.path === '/dashboard/products/categories' && p.canView
                );

                if (!hasCategoriesPermission) {
                    console.log('Tenant_sale user does not have permission to access categories, skipping fetch');
                    return;
                }
            }

            console.log('Fetching categories');
            const response = await fetch('/api/products/categories', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache',
                },
                credentials: 'include', // Ensure cookies are sent with the request
            });

            console.log('Categories API response status:', response.status);

            // Get the response text for debugging
            const responseText = await response.text();
            console.log('Categories response text length:', responseText.length);

            if (!responseText || responseText.trim() === '') {
                console.error('Empty response received from categories API');
                return;
            }

            // Try to parse as JSON or log the text if it fails
            let data;
            try {
                data = JSON.parse(responseText);
                console.log('Categories response data structure:', Object.keys(data));
            } catch (e) {
                console.error('Failed to parse categories response as JSON:', e, 'Response:', responseText);
                return; // Don't throw an error for categories as it's not critical
            }

            if (data.success) {
                setCategories(data.categories || []);
                console.log('Loaded categories count:', data.categories?.length || 0);
            } else {
                console.error('Categories API returned error:', data.error);

                // Only show a toast for permission errors, not a full page error
                if (data.error.includes("permission")) {
                    sonnerToast.error("Categories Access Restricted", {
                        description: "You don't have permission to view product categories. Some filtering options will be limited."
                    });
                }
            }
        } catch (err) {
            console.error('Error fetching categories:', err);
            // Don't show a page error for categories as it's not critical
            // But do show a toast notification
            sonnerToast.error("Categories Error", {
                description: "Could not load product categories. Some filtering options will be limited."
            });
        }
    };

    const handleSearch = () => {
        fetchProducts(selectedCategory, searchTerm, showInactiveProducts);
    };

    const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        const categoryId = e.target.value;
        setSelectedCategory(categoryId);
        fetchProducts(categoryId, searchTerm, showInactiveProducts);
    };

    console.log(products, 'products')

    const handleActiveFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const showInactive = e.target.checked;
        setShowInactiveProducts(showInactive);
        // When checkbox is checked, we want to show only inactive products
        // When unchecked, we want to show only active products
        fetchProducts(selectedCategory, searchTerm, showInactive);
    };

    // We're now handling this directly in the onKeyDown event of the input

    const handleAddInventory = (productId: string) => {
        router.push(`/dashboard/inventory/new?productId=${productId}`);
    };

    const handleViewInventory = (productId: string) => {
        router.push(`/dashboard/inventory?productId=${productId}`);
    };

    const handleDeleteClick = (product: Product) => {
        setProductToDelete(product);
        setIsDeleteDialogOpen(true);
    };

    const handleDeleteProduct = async () => {
        if (!productToDelete) return;

        setIsSubmitting(true);

        try {
            const response = await fetch(`/api/products?id=${productToDelete.id}`, {
                method: 'DELETE',
            });

            const data = await response.json();

            if (data.success) {
                if (data.softDelete) {
                    // Product was soft deleted (marked as inactive)
                    sonnerToast.success(`${productToDelete.name} has been marked as inactive because it has inventory data`);
                } else {
                    // Product was hard deleted
                    sonnerToast.success(`${productToDelete.name} has been deleted successfully`);
                }

                // Refresh the products list
                await fetchProducts(selectedCategory, searchTerm, showInactiveProducts);
                setIsDeleteDialogOpen(false);
            } else {
                // Handle specific error cases with user-friendly messages
                if (data.error.includes("permission")) {
                    // Permission-related errors
                    setError(`Access Denied: ${data.error}. Please contact your administrator to request access.`);
                    sonnerToast.error(`Access Denied`, {
                        description: `${data.error}. Please contact your administrator to request access.`
                    });
                } else if (response.status === 401) {
                    // Authentication errors
                    setError("Your session has expired. Please log in again.");
                    sonnerToast.error("Authentication Error", {
                        description: "Your session has expired. Please log in again."
                    });
                } else {
                    // Generic errors
                    setError(data.error || 'Failed to delete product');
                    sonnerToast.error("Delete Error", {
                        description: data.error || 'Failed to delete product'
                    });
                }
            }
        } catch (err) {
            console.error('Error deleting product:', err);
            const errorMessage = typeof err === 'object' && err !== null && 'message' in err
                ? (err as Error).message
                : 'An unexpected error occurred while deleting the product';

            setError(errorMessage);
            sonnerToast.error("Delete Error", {
                description: errorMessage
            });
        } finally {
            setIsSubmitting(false);
            setProductToDelete(null);
        }
    };

    const formatCurrency = (amount: number) => {
        return `৳ ${new Intl.NumberFormat('en-US', {
            style: 'decimal',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }).format(amount)}`;
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold">Products</h1>
                <div className="flex space-x-2">
                    {userPermissions.canView && (
                        session?.user?.role !== 'tenant_sale' ||
                        (session?.user?.role === 'tenant_sale' &&
                            session?.user?.menuPermissions?.some((p: any) => p.path === '/dashboard/products/categories' && p.canView))
                    ) && (
                            <Link
                                href="/dashboard/products/categories"
                                className="bg-purple-500 hover:bg-purple-600 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50"
                            >
                                Manage Categories
                            </Link>
                        )}
                    {userPermissions.canCreate && (
                        <Link
                            href="/dashboard/products/new"
                            className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                        >
                            Add New Product
                        </Link>
                    )}
                </div>
            </div>

            {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">Error: </strong>
                    <span className="block sm:inline">{error}</span>
                </div>
            )}

            {/* Filters */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-6">
                <div className="flex flex-col md:flex-row md:items-end space-y-4 md:space-y-0 md:space-x-4">
                    <div className="flex-1">
                        <label htmlFor="search" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Search Products
                        </label>
                        <input
                            type="text"
                            id="search"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                    e.preventDefault();
                                    handleSearch();
                                }
                            }}
                            placeholder="Search by name or code"
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                    </div>
                    {/* Only show category filter if categories are available */}
                    {categories.length > 0 && (
                        <div className="w-full md:w-1/4">
                            <label htmlFor="category" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Filter by Category
                            </label>
                            <select
                                id="category"
                                value={selectedCategory}
                                onChange={handleCategoryChange}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                            >
                                <option value="">All Categories</option>
                                {categories.map((category) => (
                                    <option key={category.id} value={category.id}>
                                        {category.name}
                                    </option>
                                ))}
                            </select>
                        </div>
                    )}
                    <div className="flex items-center">
                        <input
                            type="checkbox"
                            id="showInactive"
                            checked={showInactiveProducts}
                            onChange={handleActiveFilterChange}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="showInactive" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                            Show Only Inactive Products
                        </label>
                    </div>
                    <div>
                        <button
                            onClick={handleSearch}
                            className="w-full md:w-auto px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 cursor-pointer"
                        >
                            Search
                        </button>
                    </div>
                </div>
            </div>

            {/* Products List */}
            {products.length === 0 ? (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 text-center">
                    <p className="text-gray-600 dark:text-gray-400 mb-4">No products found. Add your first product to get started.</p>
                    <Link
                        href="/dashboard/products/new"
                        className="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                        </svg>
                        Create New Product
                    </Link>
                </div>
            ) : (
                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead className="bg-gray-50 dark:bg-gray-800">
                            <tr>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    Product
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    Product Code
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    Category
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    Cost
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    Price
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    Stock
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    Status
                                </th>
                                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                            {products.map((product) => (
                                <tr key={product.id} className={!product.isActive ? 'bg-gray-50 dark:bg-gray-800/50' : ''}>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex items-center">
                                            <div>
                                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                                    {product.name}
                                                </div>
                                                {/* <div className="text-sm text-gray-500 dark:text-gray-400">
                                                    {product.code || 'No code'}
                                                </div> */}

                                                {userPermissions.canEdit && (
                                                    <Link
                                                        href={`/dashboard/products/${product.id}`}
                                                        className="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 mr-2 inline-flex items-center"
                                                    >
                                                        <Edit className="h-3 w-3 mr-1" />
                                                        Edit
                                                    </Link>
                                                )}
                                                {userPermissions.canCreate && (
                                                    <button
                                                        onClick={() => handleAddInventory(product.id)}
                                                        className="text-green-500 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 cursor-pointer inline-flex items-center"
                                                    >
                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                                        </svg>
                                                        Add Stock
                                                    </button>
                                                )}
                                            </div>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-500 dark:text-gray-400">
                                            {product.code || 'No code'}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900 dark:text-white">
                                            {product.category?.name || 'No category'}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900 dark:text-white">
                                            {formatCurrency(product.costPrice)}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900 dark:text-white">
                                            {formatCurrency(product.sellingPrice)}
                                        </div>
                                        {product.discount > 0 && (
                                            <div className="text-xs text-red-500">
                                                Discount: {product.discount}%
                                            </div>
                                        )}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900 dark:text-white">
                                            {product.stock !== undefined ? `${product.stock} ${product.unit}` : 'Unknown'}
                                        </div>
                                        <div className="text-xs text-gray-500 dark:text-gray-400">
                                            {product.lastStockUpdate
                                                ? `Updated: ${new Date(product.lastStockUpdate).toLocaleDateString()}`
                                                : 'Never updated'}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <span
                                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${product.isActive
                                                ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                                                : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                                                }`}
                                        >
                                            {product.isActive ? 'Active' : 'Inactive'}
                                        </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div className="flex justify-end space-x-2">
                                            <Link
                                                href={`/dashboard/products/view/${product.id}`}
                                                className="text-indigo-500 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300 cursor-pointer inline-flex items-center"
                                            >
                                                <Eye className="h-4 w-4 mr-1" />
                                                View
                                            </Link>
                                            <button
                                                onClick={() => handleViewInventory(product.id)}
                                                className="text-purple-500 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 cursor-pointer inline-flex items-center"
                                            >
                                                <History className="h-4 w-4 mr-1" />
                                                History
                                            </button>
                                            <button
                                                onClick={() => handleDeleteClick(product)}
                                                className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 cursor-pointer inline-flex items-center"
                                                title={product.stock ? "Mark as inactive" : "Delete product"}
                                            >
                                                <Trash className="h-4 w-4 mr-1" />
                                                {product.stock ? "Remove" : "Delete"}
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Delete Product</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete {productToDelete?.name}?
                            <br /><br />
                            <span className="text-amber-600 font-medium">Note:</span> If this product has inventory data, it will be marked as inactive instead of being permanently deleted.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => setIsDeleteDialogOpen(false)}
                            disabled={isSubmitting}
                            className='cursor-pointer'
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={handleDeleteProduct}
                            disabled={isSubmitting}
                            className='cursor-pointer'
                        >
                            {isSubmitting ? 'Processing...' : (productToDelete?.stock ? 'Remove' : 'Delete')}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}
