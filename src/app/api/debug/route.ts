import { NextRequest, NextResponse } from "next/server";
import { auth } from "../../../../auth";
import { db } from "@/lib/db";
import { refundReasons, users } from "@/db/schema";
import { ALL_MENUS } from "@/lib/menu-list";

// GET /api/debug - Get debug information
export async function GET(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get database tables information
    const tables = {
      refundReasons: await db.query.refundReasons.findMany(),
      menus: ALL_MENUS,
      users: await db.query.users.findMany({
        columns: {
          id: true,
          email: true,
          role: true,
        },
      }),
    };

    // Get session information
    const sessionInfo = {
      user: session.user,
    };

    return NextResponse.json({
      success: true,
      tables,
      session: sessionInfo,
    });
  } catch (error) {
    console.error("Error fetching debug information:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch debug information",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
