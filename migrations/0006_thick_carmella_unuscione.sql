CREATE TABLE `dealer_payments` (
	`id` text PRIMARY KEY NOT NULL,
	`tenant_id` text NOT NULL,
	`branch_id` text NOT NULL,
	`dealer_id` text NOT NULL,
	`purchase_id` text,
	`amount` real NOT NULL,
	`payment_method` text DEFAULT 'cash' NOT NULL,
	`payment_reference` text,
	`payment_type` text DEFAULT 'purchase_payment' NOT NULL,
	`date` integer NOT NULL,
	`notes` text,
	`performed_by` text,
	`created_at` integer,
	`updated_at` integer,
	FOREIGN KEY (`tenant_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`branch_id`) REFERENCES `branches`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`dealer_id`) REFERENCES `dealers`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`purchase_id`) REFERENCES `dealer_purchases`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIG<PERSON> KEY (`performed_by`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `dealer_purchase_items` (
	`id` text PRIMARY KEY NOT NULL,
	`purchase_id` text NOT NULL,
	`product_id` text NOT NULL,
	`quantity` integer NOT NULL,
	`unit_price` real NOT NULL,
	`discount_percentage` real DEFAULT 0,
	`discount_amount` real DEFAULT 0,
	`tax_percentage` real DEFAULT 0,
	`tax_amount` real DEFAULT 0,
	`total` real NOT NULL,
	`created_at` integer,
	`updated_at` integer,
	FOREIGN KEY (`purchase_id`) REFERENCES `dealer_purchases`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `dealer_purchases` (
	`id` text PRIMARY KEY NOT NULL,
	`tenant_id` text NOT NULL,
	`branch_id` text NOT NULL,
	`dealer_id` text NOT NULL,
	`purchase_no` text NOT NULL,
	`date` integer NOT NULL,
	`sub_total` real NOT NULL,
	`discount_percentage` real DEFAULT 0,
	`discount_amount` real DEFAULT 0,
	`tax_amount` real DEFAULT 0,
	`total_amount` real NOT NULL,
	`paid_amount` real DEFAULT 0,
	`due_amount` real DEFAULT 0,
	`payment_method` text DEFAULT 'cash' NOT NULL,
	`payment_status` text DEFAULT 'unpaid' NOT NULL,
	`status` text DEFAULT 'completed' NOT NULL,
	`notes` text,
	`performed_by` text,
	`created_at` integer,
	`updated_at` integer,
	FOREIGN KEY (`tenant_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`branch_id`) REFERENCES `branches`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`dealer_id`) REFERENCES `dealers`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`performed_by`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `dealers` (
	`id` text PRIMARY KEY NOT NULL,
	`tenant_id` text NOT NULL,
	`dealer_code` text,
	`name` text NOT NULL,
	`email` text,
	`phone` text,
	`address` text,
	`contact_person` text,
	`current_balance` real DEFAULT 0,
	`credit_limit` real DEFAULT 0,
	`payment_terms` text,
	`is_active` integer DEFAULT true,
	`notes` text,
	`created_at` integer,
	`updated_at` integer,
	FOREIGN KEY (`tenant_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
