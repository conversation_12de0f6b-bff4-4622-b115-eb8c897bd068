'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { <PERSON><PERSON><PERSON><PERSON>, Save, Plus, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import { usePermissionCheck } from '@/lib/check-permissions';
import Link from 'next/link';
import { usePaymentMethods, getDefaultPaymentMethod } from '@/hooks/usePaymentMethods';

interface Dealer {
  id: string;
  name: string;
  dealerCode?: string;
}

interface Product {
  id: string;
  name: string;
  code?: string;
  costPrice: number;
  unit: string;
}

interface Branch {
  id: string;
  name: string;
  code?: string;
}

interface PurchaseItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  warehouseQuantity: number;
  storeQuantity: number;
  unitPrice: number;
  discountPercentage: number;
  discountAmount: number;
  taxPercentage: number;
  taxAmount: number;
  total: number;
}

export default function NewPurchasePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [dealers, setDealers] = useState<Dealer[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);

  // Check permissions
  usePermissionCheck('/dashboard/dealers', 'create');

  // Fetch payment methods
  const { paymentMethods, isLoading: isLoadingPaymentMethods } = usePaymentMethods();

  // Form state
  const [formData, setFormData] = useState({
    dealerId: searchParams?.get('dealerId') || '',
    branchId: '',
    purchaseNo: '',
    date: new Date().toISOString().split('T')[0],
    discountPercentage: 0,
    discountAmount: 0,
    taxAmount: 0,
    paymentMethod: '',
    paidAmount: 0,
    notes: '',
  });

  const [items, setItems] = useState<PurchaseItem[]>([]);
  const [newItem, setNewItem] = useState({
    productId: '',
    quantity: 1,
    warehouseQuantity: 0,
    storeQuantity: 1,
    unitPrice: 0,
    discountPercentage: 0,
    taxPercentage: 0,
  });

  // State for product search
  const [productSearchOpen, setProductSearchOpen] = useState(false);
  const [productSearchValue, setProductSearchValue] = useState('');

  // State for dealer search
  const [dealerSearchOpen, setDealerSearchOpen] = useState(false);
  const [dealerSearchValue, setDealerSearchValue] = useState('');

  // State for current inventory
  const [currentInventory, setCurrentInventory] = useState<{
    quantity: number;
    warehouseStock: number;
    storeStock: number;
    lastStockUpdate: string | null;
  } | null>(null);
  const [fetchingInventory, setFetchingInventory] = useState(false);

  useEffect(() => {
    fetchData();
    generatePurchaseNo();
  }, []);

  // Set default payment method when payment methods are loaded
  useEffect(() => {
    if (paymentMethods.length > 0 && !formData.paymentMethod) {
      const defaultMethod = getDefaultPaymentMethod(paymentMethods);
      if (defaultMethod) {
        setFormData(prev => ({ ...prev, paymentMethod: defaultMethod.code }));
      }
    }
  }, [paymentMethods, formData.paymentMethod]);

  const fetchData = async () => {
    try {
      // Fetch dealers
      const dealersResponse = await fetch('/api/dealers');
      if (dealersResponse.ok) {
        const dealersData = await dealersResponse.json();
        if (dealersData.success) {
          setDealers(dealersData.dealers || []);
        }
      }

      // Fetch products
      const productsResponse = await fetch('/api/products/list');
      if (productsResponse.ok) {
        const productsData = await productsResponse.json();
        if (productsData.success) {
          setProducts(productsData.products || []);
        }
      }

      // Fetch branches
      const branchesResponse = await fetch('/api/branches');
      if (branchesResponse.ok) {
        const branchesData = await branchesResponse.json();
        if (branchesData.success) {
          setBranches(branchesData.branches || []);
          // Set default branch if user has a specific branch
          if (session?.user?.branchId) {
            setFormData(prev => ({ ...prev, branchId: session.user.branchId }));
          }
        }
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load data');
    }
  };

  const generatePurchaseNo = () => {
    const timestamp = Date.now().toString().slice(-6);
    const purchaseNo = `PUR-${timestamp}`;
    setFormData(prev => ({ ...prev, purchaseNo }));
  };

  const fetchCurrentInventory = async (productId: string, branchId?: string) => {
    if (!productId) return;

    try {
      setFetchingInventory(true);
      const url = branchId
        ? `/api/inventory?productId=${productId}&branchId=${branchId}`
        : `/api/inventory?productId=${productId}`;

      const response = await fetch(url);
      if (!response.ok) return;

      const data = await response.json();
      if (data.success && data.inventory && data.inventory.length > 0) {
        if (branchId) {
          // Find inventory for specific branch
          const branchInventory = data.inventory.find((item: any) => item.branchId === branchId);
          if (branchInventory) {
            setCurrentInventory({
              quantity: branchInventory.quantity,
              warehouseStock: branchInventory.warehouseStock,
              storeStock: branchInventory.storeStock,
              lastStockUpdate: branchInventory.lastStockUpdate
            });
          } else {
            setCurrentInventory({
              quantity: 0,
              warehouseStock: 0,
              storeStock: 0,
              lastStockUpdate: null
            });
          }
        } else {
          // Sum across all branches
          const totalQuantity = data.inventory.reduce((sum: number, item: any) => sum + item.quantity, 0);
          const totalWarehouseStock = data.inventory.reduce((sum: number, item: any) => sum + item.warehouseStock, 0);
          const totalStoreStock = data.inventory.reduce((sum: number, item: any) => sum + item.storeStock, 0);

          setCurrentInventory({
            quantity: totalQuantity,
            warehouseStock: totalWarehouseStock,
            storeStock: totalStoreStock,
            lastStockUpdate: data.inventory[0]?.lastStockUpdate || null
          });
        }
      } else {
        setCurrentInventory({
          quantity: 0,
          warehouseStock: 0,
          storeStock: 0,
          lastStockUpdate: null
        });
      }
    } catch (error) {
      console.error('Error fetching inventory:', error);
      setCurrentInventory(null);
    } finally {
      setFetchingInventory(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value,
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));

    // If branch changes and we have a selected product, refresh inventory
    if (name === 'branchId' && newItem.productId) {
      fetchCurrentInventory(newItem.productId, value);
    }
  };

  const handleNewItemChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    const numValue = type === 'number' ? parseFloat(value) || 0 : value;

    setNewItem(prev => {
      const updated = { ...prev, [name]: numValue };

      // Auto-adjust quantities when total quantity changes
      if (name === 'quantity') {
        const totalQty = numValue as number;
        // Keep warehouse the same, adjust store
        updated.storeQuantity = Math.max(0, totalQty - updated.warehouseQuantity);
      }
      // Auto-adjust store quantity when warehouse changes
      else if (name === 'warehouseQuantity') {
        const warehouseQty = numValue as number;
        updated.storeQuantity = Math.max(0, updated.quantity - warehouseQty);
      }
      // Auto-adjust warehouse quantity when store changes
      else if (name === 'storeQuantity') {
        const storeQty = numValue as number;
        updated.warehouseQuantity = Math.max(0, updated.quantity - storeQty);
      }

      return updated;
    });
  };

  const handleProductSelect = (productId: string) => {
    const product = products.find(p => p.id === productId);
    if (product) {
      setNewItem(prev => ({
        ...prev,
        productId,
        unitPrice: product.costPrice,
        // Reset quantities when product changes
        quantity: 1,
        warehouseQuantity: 0,
        storeQuantity: 1,
      }));
      setProductSearchValue(product.name + (product.code ? ` (${product.code})` : ''));
      setProductSearchOpen(false);

      // Fetch current inventory for this product
      fetchCurrentInventory(productId, formData.branchId);
    }
  };

  const getSelectedProductName = () => {
    if (!newItem.productId) return '';
    const product = products.find(p => p.id === newItem.productId);
    return product ? product.name + (product.code ? ` (${product.code})` : '') : '';
  };

  const handleDealerSelect = (dealerId: string) => {
    const dealer = dealers.find(d => d.id === dealerId);
    if (dealer) {
      setFormData(prev => ({ ...prev, dealerId }));
      setDealerSearchValue(dealer.name + (dealer.dealerCode ? ` (${dealer.dealerCode})` : ''));
      setDealerSearchOpen(false);
    }
  };

  const getSelectedDealerName = () => {
    if (!formData.dealerId) return '';
    const dealer = dealers.find(d => d.id === formData.dealerId);
    return dealer ? dealer.name + (dealer.dealerCode ? ` (${dealer.dealerCode})` : '') : '';
  };

  const calculateItemTotal = (item: typeof newItem) => {
    const subtotal = item.quantity * item.unitPrice;
    const discountAmount = (subtotal * item.discountPercentage) / 100;
    const afterDiscount = subtotal - discountAmount;
    const taxAmount = (afterDiscount * item.taxPercentage) / 100;
    return afterDiscount + taxAmount;
  };

  const addItem = () => {
    if (!newItem.productId || newItem.quantity <= 0 || newItem.unitPrice <= 0) {
      toast.error('Please fill all item fields correctly');
      return;
    }

    // Validate quantity allocation
    if (newItem.warehouseQuantity + newItem.storeQuantity !== newItem.quantity) {
      toast.error('Warehouse quantity + Store quantity must equal total quantity');
      return;
    }

    const product = products.find(p => p.id === newItem.productId);
    if (!product) {
      toast.error('Product not found');
      return;
    }

    const subtotal = newItem.quantity * newItem.unitPrice;
    const discountAmount = (subtotal * newItem.discountPercentage) / 100;
    const afterDiscount = subtotal - discountAmount;
    const taxAmount = (afterDiscount * newItem.taxPercentage) / 100;
    const total = afterDiscount + taxAmount;

    const purchaseItem: PurchaseItem = {
      id: Date.now().toString(),
      productId: newItem.productId,
      productName: product.name,
      quantity: newItem.quantity,
      warehouseQuantity: newItem.warehouseQuantity,
      storeQuantity: newItem.storeQuantity,
      unitPrice: newItem.unitPrice,
      discountPercentage: newItem.discountPercentage,
      discountAmount,
      taxPercentage: newItem.taxPercentage,
      taxAmount,
      total,
    };

    setItems(prev => [...prev, purchaseItem]);
    setNewItem({
      productId: '',
      quantity: 1,
      warehouseQuantity: 0,
      storeQuantity: 1,
      unitPrice: 0,
      discountPercentage: 0,
      taxPercentage: 0,
    });
    setProductSearchValue('');
    setProductSearchOpen(false);
  };

  const removeItem = (itemId: string) => {
    setItems(prev => prev.filter(item => item.id !== itemId));
  };

  const calculateTotals = () => {
    const subTotal = items.reduce((sum, item) => sum + item.total, 0);
    const totalAmount = subTotal - formData.discountAmount + formData.taxAmount;
    const dueAmount = totalAmount - formData.paidAmount;
    return { subTotal, totalAmount, dueAmount };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.dealerId || !formData.branchId || !formData.purchaseNo || items.length === 0) {
      toast.error('Please fill all required fields and add at least one item');
      return;
    }

    setLoading(true);

    try {
      const { subTotal, totalAmount } = calculateTotals();

      const response = await fetch('/api/dealers/purchases', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dealerId: formData.dealerId,
          branchId: formData.branchId,
          purchaseNo: formData.purchaseNo,
          date: formData.date,
          items: items.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            warehouseQuantity: item.warehouseQuantity,
            storeQuantity: item.storeQuantity,
            unitPrice: item.unitPrice,
            discountPercentage: item.discountPercentage,
            discountAmount: item.discountAmount,
            taxPercentage: item.taxPercentage,
            taxAmount: item.taxAmount,
            total: item.total,
          })),
          discountPercentage: formData.discountPercentage,
          discountAmount: formData.discountAmount,
          taxAmount: formData.taxAmount,
          paymentMethod: formData.paymentMethod,
          paidAmount: formData.paidAmount,
          notes: formData.notes || null,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Purchase created successfully');
        router.push(`/dashboard/dealers/${formData.dealerId}`);
      } else {
        toast.error(data.error || 'Failed to create purchase');
      }
    } catch (error) {
      console.error('Error creating purchase:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const { subTotal, totalAmount, dueAmount } = calculateTotals();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/dashboard/dealers">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dealers
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">New Purchase from Dealer</h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Purchase Details */}
        <Card>
          <CardHeader>
            <CardTitle>Purchase Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dealerId">Dealer *</Label>
                <Popover open={dealerSearchOpen} onOpenChange={setDealerSearchOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={dealerSearchOpen}
                      className="w-full justify-between"
                    >
                      {formData.dealerId
                        ? getSelectedDealerName()
                        : "Select dealer..."}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0">
                    <Command>
                      <CommandInput
                        placeholder="Search dealers..."
                        value={dealerSearchValue}
                        onValueChange={setDealerSearchValue}
                      />
                      <CommandList>
                        <CommandEmpty>No dealer found.</CommandEmpty>
                        <CommandGroup>
                          {dealers
                            .filter((dealer) =>
                              dealer.name.toLowerCase().includes(dealerSearchValue.toLowerCase()) ||
                              (dealer.dealerCode && dealer.dealerCode.toLowerCase().includes(dealerSearchValue.toLowerCase()))
                            )
                            .map((dealer) => (
                              <CommandItem
                                key={dealer.id}
                                value={dealer.id}
                                onSelect={() => handleDealerSelect(dealer.id)}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    formData.dealerId === dealer.id ? "opacity-100" : "opacity-0"
                                  )}
                                />
                                {dealer.name} {dealer.dealerCode && `(${dealer.dealerCode})`}
                              </CommandItem>
                            ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label htmlFor="branchId">Branch *</Label>
                <Select value={formData.branchId} onValueChange={(value) => handleSelectChange('branchId', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select branch" />
                  </SelectTrigger>
                  <SelectContent>
                    {branches.map((branch) => (
                      <SelectItem key={branch.id} value={branch.id}>
                        {branch.name} {branch.code && `(${branch.code})`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="purchaseNo">Purchase No *</Label>
                <Input
                  id="purchaseNo"
                  name="purchaseNo"
                  value={formData.purchaseNo}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="date">Date *</Label>
                <Input
                  id="date"
                  name="date"
                  type="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>

          </CardContent>
        </Card>

        {/* Add Items */}
        <Card>
          <CardHeader>
            <CardTitle>Add Items</CardTitle>
            <p className="text-sm text-gray-600">
              Products purchased from dealers will be automatically added to inventory based on your warehouse/store allocation.
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              {/* Product Selection Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Product *</Label>
                  <Popover open={productSearchOpen} onOpenChange={setProductSearchOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={productSearchOpen}
                        className="w-full justify-between"
                      >
                        {newItem.productId
                          ? getSelectedProductName()
                          : "Select product..."}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <Command>
                        <CommandInput
                          placeholder="Search products..."
                          value={productSearchValue}
                          onValueChange={setProductSearchValue}
                        />
                        <CommandList>
                          <CommandEmpty>No product found.</CommandEmpty>
                          <CommandGroup>
                            {products
                              .filter((product) =>
                                product.name.toLowerCase().includes(productSearchValue.toLowerCase()) ||
                                (product.code && product.code.toLowerCase().includes(productSearchValue.toLowerCase()))
                              )
                              .map((product) => (
                                <CommandItem
                                  key={product.id}
                                  value={product.id}
                                  onSelect={() => handleProductSelect(product.id)}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      newItem.productId === product.id ? "opacity-100" : "opacity-0"
                                    )}
                                  />
                                  {product.name} {product.code && `(${product.code})`}
                                  <span className="ml-auto text-sm text-gray-500">
                                    {product.costPrice.toFixed(2)} BDT
                                  </span>
                                </CommandItem>
                              ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="unitPrice">Unit Price</Label>
                  <Input
                    id="unitPrice"
                    name="unitPrice"
                    type="number"
                    min="0"
                    step="0.01"
                    value={newItem.unitPrice}
                    onChange={handleNewItemChange}
                  />
                </div>
              </div>

              {/* Current Inventory Display */}
              {newItem.productId && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-800 mb-2">Current Inventory</h4>
                  {fetchingInventory ? (
                    <p className="text-sm text-gray-500">Loading current inventory...</p>
                  ) : currentInventory ? (
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <p className="text-xs text-gray-500">Total Stock</p>
                        <p className="text-lg font-semibold text-gray-900">{currentInventory.quantity}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500">Warehouse</p>
                        <p className="text-lg font-semibold text-blue-600">{currentInventory.warehouseStock}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500">Store</p>
                        <p className="text-lg font-semibold text-green-600">{currentInventory.storeStock}</p>
                      </div>
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">No inventory data available</p>
                  )}
                </div>
              )}

              {/* Quantity Allocation Row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="quantity">Total Quantity *</Label>
                  <Input
                    id="quantity"
                    name="quantity"
                    type="number"
                    min="1"
                    value={newItem.quantity}
                    onChange={handleNewItemChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="warehouseQuantity">Warehouse Qty</Label>
                  <Input
                    id="warehouseQuantity"
                    name="warehouseQuantity"
                    type="number"
                    min="0"
                    max={newItem.quantity}
                    value={newItem.warehouseQuantity}
                    onChange={handleNewItemChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="storeQuantity">Store Qty</Label>
                  <Input
                    id="storeQuantity"
                    name="storeQuantity"
                    type="number"
                    min="0"
                    max={newItem.quantity}
                    value={newItem.storeQuantity}
                    onChange={handleNewItemChange}
                  />
                </div>
              </div>

              {/* Validation Warning */}
              {newItem.warehouseQuantity + newItem.storeQuantity !== newItem.quantity && newItem.quantity > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-3 py-2 rounded text-sm">
                  <strong>Warning:</strong> Warehouse ({newItem.warehouseQuantity}) + Store ({newItem.storeQuantity}) = {newItem.warehouseQuantity + newItem.storeQuantity}, but Total is {newItem.quantity}
                </div>
              )}

              {/* Discount and Tax Row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div className="space-y-2">
                  <Label htmlFor="discountPercentage">Discount %</Label>
                  <Input
                    id="discountPercentage"
                    name="discountPercentage"
                    type="number"
                    min="0"
                    max="100"
                    step="0.01"
                    value={newItem.discountPercentage}
                    onChange={handleNewItemChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="taxPercentage">Tax %</Label>
                  <Input
                    id="taxPercentage"
                    name="taxPercentage"
                    type="number"
                    min="0"
                    max="100"
                    step="0.01"
                    value={newItem.taxPercentage}
                    onChange={handleNewItemChange}
                  />
                </div>
                <Button type="button" onClick={addItem}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Item
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Items Table */}
        {items.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Purchase Items</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead>Total Qty</TableHead>
                    <TableHead>Warehouse</TableHead>
                    <TableHead>Store</TableHead>
                    <TableHead>Unit Price</TableHead>
                    <TableHead>Discount</TableHead>
                    <TableHead>Tax</TableHead>
                    <TableHead>Total</TableHead>
                    <TableHead></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {items.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.productName}</TableCell>
                      <TableCell>{item.quantity}</TableCell>
                      <TableCell className="text-blue-600">{item.warehouseQuantity}</TableCell>
                      <TableCell className="text-green-600">{item.storeQuantity}</TableCell>
                      <TableCell>{item.unitPrice.toFixed(2)}</TableCell>
                      <TableCell>{item.discountPercentage}% ({item.discountAmount.toFixed(2)})</TableCell>
                      <TableCell>{item.taxPercentage}% ({item.taxAmount.toFixed(2)})</TableCell>
                      <TableCell className="font-medium">{item.total.toFixed(2)}</TableCell>
                      <TableCell>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeItem(item.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}

        {/* Totals and Payment */}
        <Card>
          <CardHeader>
            <CardTitle>Payment Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">

              <div className="space-y-2">
                <Label htmlFor="taxAmount">Tax Amount</Label>
                <Input
                  id="taxAmount"
                  name="taxAmount"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.taxAmount}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="paidAmount">Paid Amount</Label>
                <Input
                  id="paidAmount"
                  name="paidAmount"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.paidAmount}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="paymentMethod">Payment Method</Label>
                <Select value={formData.paymentMethod} onValueChange={(value) => handleSelectChange('paymentMethod', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder={isLoadingPaymentMethods ? "Loading..." : "Select payment method"} />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingPaymentMethods ? (
                      <SelectItem value="" disabled>Loading payment methods...</SelectItem>
                    ) : paymentMethods.length > 0 ? (
                      paymentMethods.map((method) => (
                        <SelectItem key={method.id} value={method.code}>
                          {method.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="" disabled>No payment methods available</SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="border-t pt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-lg">
                <div>
                  <strong>Subtotal: {subTotal.toFixed(2)} BDT</strong>
                </div>
                <div>
                  <strong>Total Amount: {totalAmount.toFixed(2)} BDT</strong>
                </div>
                <div className={`${dueAmount > 0 ? 'text-red-600' : 'text-green-600'}`}>
                  <strong>Due Amount: {dueAmount.toFixed(2)} BDT</strong>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submit */}
        <div className="flex gap-4">
          <Button type="submit" disabled={loading || items.length === 0}>
            <Save className="h-4 w-4 mr-2" />
            {loading ? 'Creating...' : 'Create Purchase'}
          </Button>
          <Link href="/dashboard/dealers">
            <Button type="button" variant="outline">
              Cancel
            </Button>
          </Link>
        </div>
      </form>
    </div>
  );
}
