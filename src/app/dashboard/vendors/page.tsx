'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { DotsHorizontalIcon, PlusIcon, ReloadIcon, MagnifyingGlassIcon } from '@radix-ui/react-icons';
import { toast } from "sonner";

interface Vendor {
    id: string;
    name: string;
    email: string;
    phone: string | null;
    address: string | null;
    isActive: boolean;
    vendorCode: string | null;
    notes: string | null;
    createdAt: string;
    updatedAt: string;
    userId: string;
    tenantId: string | null;
    user?: {
        id: string;
        username: string;
        fullName: string;
        email: string;
    };
}

export default function VendorsPage() {
    const router = useRouter();
    const [vendors, setVendors] = useState<Vendor[]>([]);
    const [filteredVendors, setFilteredVendors] = useState<Vendor[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [userRole, setUserRole] = useState<string | null>(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [vendorToDelete, setVendorToDelete] = useState<Vendor | null>(null);
    const [isSubmitting, setIsSubmitting] = useState(false);

    useEffect(() => {
        fetchUserRole();
        fetchVendors();
    }, []);

    const fetchUserRole = async () => {
        try {
            const response = await fetch('/api/auth/me');
            if (response.ok) {
                const data = await response.json();
                setUserRole(data.user?.role || null);
            }
        } catch (error) {
            console.error('Error fetching user role:', error);
        }
    };

    const fetchVendors = async () => {
        try {
            setLoading(true);
            console.log('Fetching all vendors...');

            // Try the regular API endpoint first
            try {
                const response = await fetch('/api/vendors/all');
                console.log('Response status from /api/vendors/all:', response.status);

                if (response.ok) {
                    const data = await response.json();
                    console.log('Vendors API response from /api/vendors/all:', data);

                    if (data.success && data.vendors && data.vendors.length > 0) {
                        console.log(`Received ${data.vendors.length} vendors from /api/vendors/all`);
                        setVendors(data.vendors);
                        setFilteredVendors(data.vendors);
                        setLoading(false);
                        return;
                    } else {
                        console.warn('No vendors found from /api/vendors/all, trying direct endpoint');
                    }
                }
            } catch (error) {
                console.error('Error with /api/vendors/all endpoint:', error);
            }

            // If the first attempt failed or returned no vendors, try the direct endpoint
            console.log('Trying direct database query endpoint...');
            const directResponse = await fetch('/api/vendors/direct');
            console.log('Response status from /api/vendors/direct:', directResponse.status);

            if (!directResponse.ok) {
                throw new Error(`Failed to fetch vendors: ${directResponse.status}`);
            }

            const directData = await directResponse.json();
            console.log('Vendors API response from direct endpoint:', directData);

            if (directData.success) {
                console.log(`Received ${directData.vendors?.length || 0} vendors from direct endpoint`);
                setVendors(directData.vendors || []);
                setFilteredVendors(directData.vendors || []);
            } else {
                console.error('API returned error:', directData.error);
                setError(directData.error || 'Failed to load vendors');
                toast.error(directData.error || 'Failed to load vendors');
            }
        } catch (err) {
            console.error('Error fetching vendors:', err);
            setError('An error occurred while fetching vendors');
            toast.error('An error occurred while fetching vendors');
        } finally {
            setLoading(false);
        }
    };

    const handleCreateVendor = () => {
        router.push('/dashboard/vendors/create');
    };

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(e.target.value);
    };

    const handleSearchSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Just prevent default, no need to fetch as filtering is client-side
    };

    const handleEditVendor = (vendorId: string) => {
        router.push(`/dashboard/vendors/edit/${vendorId}`);
    };

    const handleViewVendor = (vendorId: string) => {
        router.push(`/dashboard/vendors/${vendorId}`);
    };

    const handleDeleteClick = (vendor: Vendor) => {
        setVendorToDelete(vendor);
        setIsDeleteDialogOpen(true);
    };

    const handleDeleteConfirm = async () => {
        if (!vendorToDelete) return;

        setIsSubmitting(true);

        try {
            const response = await fetch(`/api/vendors?id=${vendorToDelete.id}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            const data = await response.json();

            if (data.success) {
                toast.success(`${vendorToDelete.name} has been deleted successfully`);
                fetchVendors();
                setIsDeleteDialogOpen(false);
            } else {
                toast.error(data.error || 'Failed to delete vendor');
            }
        } catch (error) {
            console.error('Error deleting vendor:', error);
            toast.error('An unexpected error occurred while deleting the vendor');
        } finally {
            setIsSubmitting(false);
        }
    };

    const formatDate = (dateString: string) => {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
    };

    // Filter vendors when search query changes
    useEffect(() => {
        if (!searchQuery.trim()) {
            setFilteredVendors(vendors);
            return;
        }

        const lowerCaseSearch = searchQuery.toLowerCase();
        const filtered = vendors.filter(vendor =>
            vendor.name.toLowerCase().includes(lowerCaseSearch) ||
            (vendor.email && vendor.email.toLowerCase().includes(lowerCaseSearch)) ||
            (vendor.phone && vendor.phone.toLowerCase().includes(lowerCaseSearch)) ||
            (vendor.address && vendor.address.toLowerCase().includes(lowerCaseSearch)) ||
            (vendor.vendorCode && vendor.vendorCode.toLowerCase().includes(lowerCaseSearch)) ||
            (vendor.user?.fullName && vendor.user.fullName.toLowerCase().includes(lowerCaseSearch)) ||
            (vendor.user?.email && vendor.user.email.toLowerCase().includes(lowerCaseSearch)) ||
            (vendor.isActive ? 'active' : 'inactive').includes(lowerCaseSearch)
        );

        setFilteredVendors(filtered);
    }, [searchQuery, vendors]);

    const handleReset = () => {
        setSearchQuery('');
    };

    if (loading) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="flex items-center justify-between mb-6">
                    <h1 className="text-2xl font-bold">Vendor Management</h1>
                </div>
                <Card>
                    <CardHeader>
                        <CardTitle>Loading vendors...</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex justify-center items-center min-h-[200px]">
                            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold">Vendor Management</h1>
                <Button onClick={handleCreateVendor} className="gap-1 cursor-pointer">
                    <PlusIcon className="h-4 w-4" />
                    Add New Vendor
                </Button>
            </div>

            <Card>
                <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                        <CardTitle>Vendors</CardTitle>
                        <form onSubmit={(e) => {
                            e.preventDefault();
                            handleSearchSubmit(e);
                        }} className="flex gap-2">
                            <div className="relative">
                                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
                                <Input
                                    type="search"
                                    placeholder="Search vendors..."
                                    className="pl-8 w-[250px]"
                                    value={searchQuery}
                                    onChange={handleSearchChange}
                                />
                                {loading && (
                                    <div className="absolute right-2.5 top-2.5">
                                        <ReloadIcon className="h-4 w-4 animate-spin text-gray-500" />
                                    </div>
                                )}
                            </div>
                            <Button type="submit" variant="outline" size="icon" className='cursor-pointer'>
                                <MagnifyingGlassIcon className="h-4 w-4" />
                            </Button>
                            {searchQuery && (
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="icon"
                                    onClick={() => handleReset()}
                                    className="cursor-pointer"
                                >
                                    <ReloadIcon className="h-4 w-4" />
                                </Button>
                            )}
                        </form>
                    </div>
                </CardHeader>
                <CardContent>
                    {error && (
                        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                            <strong className="font-bold">Error: </strong>
                            <span className="block sm:inline">{error}</span>
                        </div>
                    )}

                    {filteredVendors.length === 0 ? (
                        <div className="text-center py-8">
                            <p className="text-gray-600 dark:text-gray-400">
                                {searchQuery
                                    ? `No vendors found matching "${searchQuery}"`
                                    : "No vendors found."}
                            </p>
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Name</TableHead>
                                        {userRole === 'admin' && (
                                            <>
                                                <TableHead>Contact</TableHead>
                                                <TableHead>Associated User</TableHead>
                                            </>
                                        )}
                                        <TableHead>Status</TableHead>
                                        <TableHead>Created</TableHead>
                                        {userRole === 'admin' && (
                                            <TableHead>Vendor Code</TableHead>
                                        )}
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredVendors.map((vendor) => (
                                        <TableRow key={vendor.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                                            <TableCell>
                                                <div className="font-medium">{vendor.name}</div>
                                            </TableCell>
                                            {userRole === 'admin' && (
                                                <>
                                                    <TableCell>
                                                        <div>{vendor.email || 'N/A'}</div>
                                                        <div className="text-sm text-gray-500 dark:text-gray-400">{vendor.phone || 'N/A'}</div>
                                                        {vendor.address && (
                                                            <div className="text-sm text-gray-500 dark:text-gray-400">{vendor.address}</div>
                                                        )}
                                                    </TableCell>
                                                    <TableCell>
                                                        <div>{vendor.user?.fullName || 'N/A'}</div>
                                                        <div className="text-sm text-gray-500 dark:text-gray-400">{vendor.user?.email || 'N/A'}</div>
                                                    </TableCell>
                                                </>
                                            )}
                                            <TableCell>
                                                <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${vendor.isActive
                                                        ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                                                        : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                                                    }`}>
                                                    {vendor.isActive ? 'Active' : 'Inactive'}
                                                </span>
                                            </TableCell>
                                            <TableCell>
                                                <div>{formatDate(vendor.createdAt)}</div>
                                            </TableCell>
                                            {userRole === 'admin' && (
                                                <TableCell>
                                                    <div>{vendor.vendorCode || 'N/A'}</div>
                                                </TableCell>
                                            )}
                                            <TableCell className="text-right">
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" size="icon" className="h-8 w-8 p-0 cursor-pointer">
                                                            <DotsHorizontalIcon className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                                        <DropdownMenuSeparator />
                                                        <DropdownMenuItem onClick={() => handleViewVendor(vendor.id)} className='cursor-pointer'>
                                                            View Details
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem onClick={() => handleEditVendor(vendor.id)} className='cursor-pointer'>
                                                            Edit
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            className="text-red-600 focus:text-red-600 cursor-pointer"
                                                            onClick={() => handleDeleteClick(vendor)}
                                                        >
                                                            Delete
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Delete Confirmation Dialog */}
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Confirm Deletion</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete vendor "{vendorToDelete?.name}"?
                            This action cannot be undone.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => setIsDeleteDialogOpen(false)}
                            disabled={isSubmitting}
                            className="cursor-pointer"
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={handleDeleteConfirm}
                            disabled={isSubmitting}
                            className="cursor-pointer"
                        >
                            {isSubmitting ? (
                                <>
                                    <ReloadIcon className="h-4 w-4 mr-2 animate-spin" />
                                    Deleting...
                                </>
                            ) : (
                                "Delete"
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}