import * as React from "react";

type ToastProps = {
    title?: string;
    description?: string;
    variant?: "default" | "destructive";
};

type ToastActionElement = React.ReactElement;

type ToastContextValue = {
    toast: (props: ToastProps) => void;
};

const ToastContext = React.createContext<ToastContextValue | undefined>(
    undefined
);

export function ToastProvider({ children }: { children: React.ReactNode }) {
    const [toasts, setToasts] = React.useState<ToastProps[]>([]);

    const toast = React.useCallback((props: ToastProps) => {
        setToasts((prev) => [...prev, props]);
        // Remove toast after 3 seconds
        setTimeout(() => {
            setToasts((prev) => prev.slice(1));
        }, 3000);
    }, []);

    return (
        <ToastContext.Provider value={{ toast }}>
            {children}
            <div className="fixed bottom-0 right-0 z-50 p-4 flex flex-col gap-2">
                {toasts.map((t, i) => (
                    <div
                        key={i}
                        className={`p-4 rounded-md shadow-md ${t.variant === "destructive"
                                ? "bg-red-500 text-white"
                                : "bg-white text-gray-800"
                            }`}
                    >
                        {t.title && <div className="font-semibold">{t.title}</div>}
                        {t.description && <div>{t.description}</div>}
                    </div>
                ))}
            </div>
        </ToastContext.Provider>
    );
}

export const useToast = (): {
    toast: (props: ToastProps) => void;
} => {
    const context = React.useContext(ToastContext);

    if (context === undefined) {
        console.warn("useToast must be used within a ToastProvider");
        return {
            toast: () => {
                console.warn("Toast provider not found");
            },
        };
    }

    return context;
}; 