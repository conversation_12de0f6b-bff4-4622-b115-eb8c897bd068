'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { ReloadIcon } from '@radix-ui/react-icons';

interface Tenant {
  id: string;
  companyName: string;
  domain?: string;
  isActive: boolean;
  subscription: {
    plan: string; // Changed from object to string as per new API response
    startDate: string;
    endDate: string;
    isActive: boolean;
    daysRemaining: number;
    paymentStatus?: 'pending' | 'paid' | 'overdue';
    lastPaymentDate?: string;
    paymentDueDate?: string;
  };
  user: {
    id: string;
    username: string;
    email: string;
    fullName: string;
    phone?: string;
    address?: string;
    role: string;
    lastLogin?: string;
  };
}

export default function TenantsPage() {
  const router = useRouter();
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [filteredTenants, setFilteredTenants] = useState<Tenant[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [tenantToDelete, setTenantToDelete] = useState<Tenant | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingTenant, setDeletingTenant] = useState(false);

  useEffect(() => {
    fetchTenants();
  }, []);

  // Filter tenants when search term changes
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredTenants(tenants);
      return;
    }

    const lowerCaseSearch = searchTerm.toLowerCase();
    const filtered = tenants.filter(tenant =>
      tenant.companyName.toLowerCase().includes(lowerCaseSearch) ||
      tenant.user.fullName?.toLowerCase().includes(lowerCaseSearch) ||
      tenant.user.email?.toLowerCase().includes(lowerCaseSearch) ||
      tenant.subscription.plan.toLowerCase().includes(lowerCaseSearch) ||
      (tenant.isActive ? 'active' : 'inactive').includes(lowerCaseSearch)
    );

    setFilteredTenants(filtered);
  }, [searchTerm, tenants]);

  // Helper function to map user data to tenant data
  const mapUserToTenant = (user: any): Tenant => {
    return {
      id: user.tenant.id,
      companyName: user.tenant.companyName,
      domain: user.tenant.domain,
      isActive: user.isActive,
      subscription: {
        plan: user.tenant.subscription.plan,
        startDate: user.tenant.subscription.startDate,
        endDate: user.tenant.subscription.endDate,
        isActive: user.tenant.subscription.isActive,
        daysRemaining: user.tenant.subscription.daysRemaining,
        paymentStatus: user.tenant.subscription.paymentStatus,
        lastPaymentDate: user.tenant.subscription.lastPaymentDate,
        paymentDueDate: user.tenant.subscription.paymentDueDate,
      },
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.fullName,
        phone: user.phone,
        address: user.address,
        role: user.role,
        lastLogin: user.lastLogin,
      }
    };
  };

  const fetchTenants = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/tenants');

      if (!response.ok) {
        throw new Error('Failed to fetch tenants');
      }

      const data = await response.json();

      if (data.success) {
        // Filter out users without tenant data before mapping
        const usersWithTenants = data.users.filter((user: any) => {
          if (!user.tenant) {
            // Log a more informative message without stringifying the entire user object
            console.log(`Tenant data is missing for user ID: ${user.id || 'unknown'}, email: ${user.email || 'unknown'}`);
            return false;
          }
          return true;
        });

        // Map filtered users to tenant objects
        const mappedTenants = usersWithTenants.map(mapUserToTenant);

        setTenants(mappedTenants);
        setFilteredTenants(mappedTenants);
      } else {
        setError(data.error || 'Failed to load tenants');
      }
    } catch (err) {
      setError('An error occurred while fetching tenants');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleActivateDeactivate = async (userId: string, currentStatus: boolean) => {
    try {
      setSuccessMessage('');
      setError('');

      const response = await fetch(`/api/tenants/${userId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isActive: !currentStatus
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccessMessage(`Tenant ${currentStatus ? 'deactivated' : 'activated'} successfully`);

        // Refresh the entire tenants list to get the most up-to-date data
        await fetchTenants();
      } else {
        setError(data.error || `Failed to ${currentStatus ? 'deactivate' : 'activate'} tenant`);
      }
    } catch (err) {
      setError(`An error occurred while ${currentStatus ? 'deactivating' : 'activating'} the tenant`);
      console.error(err);
    }
  };

  const handleDeleteClick = (tenant: Tenant) => {
    setTenantToDelete(tenant);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteTenant = async () => {
    if (!tenantToDelete) return;

    setDeletingTenant(true);
    setSuccessMessage('');
    setError('');

    try {
      // Use the actual tenant ID for deletion
      const response = await fetch(`/api/tenants/${tenantToDelete.id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        setSuccessMessage(`Tenant "${tenantToDelete.companyName}" deleted successfully`);
        // Refresh the tenants list
        await fetchTenants();
        setIsDeleteDialogOpen(false);
      } else {
        setError(data.error || 'Failed to delete tenant');
      }
    } catch (err) {
      setError('An error occurred while deleting the tenant');
      console.error(err);
    } finally {
      setDeletingTenant(false);
    }
  };

  const handleViewDetails = (tenantId: string) => {
    router.push(`/dashboard/tenants/${tenantId}`);
  };

  const handleManageSubscription = (tenantId: string) => {
    router.push(`/dashboard/tenants/${tenantId}/subscription`);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <h1 className="text-2xl font-bold">Tenant Management</h1>

        {/* Search input */}
        <div className="relative w-full md:w-64">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <svg className="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
              <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
            </svg>
          </div>
          <input
            type="search"
            className="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
            placeholder="Search tenants..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{successMessage}</span>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {tenants.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 text-center">
          <p className="text-gray-600 dark:text-gray-400">No tenants found.</p>
        </div>
      ) : filteredTenants.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 text-center">
          <p className="text-gray-600 dark:text-gray-400">No tenants match your search criteria.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white dark:bg-gray-800 rounded-lg shadow">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Company/Name</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">User</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Subscription</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Expiry</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredTenants.map((tenant) => (
                <tr key={tenant.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">{tenant.companyName}</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">Created: {formatDate(tenant.subscription.startDate)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">{tenant.user.fullName || 'N/A'}</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">{tenant.user.email || 'N/A'}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">{tenant.subscription.plan}</div> {/* Updated to reflect new API response */}
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {tenant.subscription.paymentStatus ? tenant.subscription.paymentStatus : 'N/A'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${tenant.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                      {tenant.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">{formatDate(tenant.subscription.endDate)}</div>
                    <div className={`text-sm ${(tenant.subscription.daysRemaining || 0) < 7 ? 'text-red-500' : 'text-gray-500 dark:text-gray-400'}`}>
                      {tenant.subscription.daysRemaining !== undefined ? `${tenant.subscription.daysRemaining} days left` : 'N/A'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => handleViewDetails(tenant.user.id)}
                      className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3 cursor-pointer"
                    >
                      Details
                    </button>
                    <button
                      onClick={() => handleManageSubscription(tenant.user.id)}
                      className="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300 mr-3 cursor-pointer"
                    >
                      Subscription
                    </button>
                    <button
                      onClick={() => handleActivateDeactivate(tenant.user.id, tenant.isActive)}
                      className={`${tenant.isActive ? 'text-amber-600 hover:text-amber-900 dark:text-amber-400 dark:hover:text-amber-300 cursor-pointer' : 'text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 cursor-pointer'} mr-3`}
                    >
                      {tenant.isActive ? 'Deactivate' : 'Activate'}
                    </button>
                    <button
                      onClick={() => handleDeleteClick(tenant)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 cursor-pointer"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete tenant "{tenantToDelete?.companyName}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="cursor-pointer">Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteTenant}
              className="bg-red-600 hover:bg-red-700 cursor-pointer"
            >
              {deletingTenant ? (
                <>
                  <ReloadIcon className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}