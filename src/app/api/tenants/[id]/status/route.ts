import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { tenants, users } from "@/db/schema";
import { auth } from "~/auth";
import { eq } from "drizzle-orm";

// PATCH /api/tenants/[id]/status - Update tenant active status (admin only)
export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const userId = (await params).id;
    const session = await auth();

    // Only admin can change tenant status
    if (!session?.user || session.user.role !== "admin") {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 403 }
      );
    }

    // Find the tenant associated with the user ID
    const tenant = await db.query.tenants.findFirst({
      where: eq(tenants.userId, userId),
    });

    if (!tenant) {
      return NextResponse.json(
        { success: false, error: "Tenant not found for this user" },
        { status: 404 }
      );
    }

    // Parse request body
    const { isActive } = await request.json();

    // Validate required fields
    if (isActive === undefined) {
      return NextResponse.json(
        { success: false, error: "isActive field is required" },
        { status: 400 }
      );
    }

    // Update tenant status
    await db
      .update(tenants)
      .set({
        isActive: isActive,
        updatedAt: new Date(),
      })
      .where(eq(tenants.userId, userId));

    // Also update the user's active status
    await db
      .update(users)
      .set({
        isActive: isActive,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId));

    return NextResponse.json({
      success: true,
      message: `Tenant ${isActive ? "activated" : "deactivated"} successfully`,
      tenant: {
        id: tenant.id,
        userId: tenant.userId,
        isActive: isActive,
      },
    });
  } catch (error) {
    console.error("Error updating tenant status:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update tenant status" },
      { status: 500 }
    );
  }
}
