import { db } from "../src/lib/db";
import { users, paymentMethods } from "../src/db/schema";
import { eq } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

async function seedPaymentMethods() {
  try {
    console.log("Starting to seed payment methods...");

    // Get all tenant users
    const tenants = await db.query.users.findMany({
      where: eq(users.role, 'tenant'),
    });

    console.log(`Found ${tenants.length} tenant users`);

    for (const tenant of tenants) {
      console.log(`Creating payment methods for tenant: ${tenant.fullName} (${tenant.username})`);

      // Check if tenant already has payment methods
      const existingMethods = await db.query.paymentMethods.findMany({
        where: eq(paymentMethods.tenantId, tenant.id),
      });

      if (existingMethods.length > 0) {
        console.log(`  - Tenant already has ${existingMethods.length} payment methods, skipping...`);
        continue;
      }

      // Create default payment methods
      const defaultMethods = [
        {
          id: uuidv4(),
          tenantId: tenant.id,
          name: 'Cash',
          code: 'cash',
          description: 'Cash payment',
          isActive: true,
          isDefault: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: uuidv4(),
          tenantId: tenant.id,
          name: 'Card',
          code: 'card',
          description: 'Credit/Debit card payment',
          isActive: true,
          isDefault: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: uuidv4(),
          tenantId: tenant.id,
          name: 'Bank Transfer',
          code: 'bank_transfer',
          description: 'Bank transfer payment',
          isActive: true,
          isDefault: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: uuidv4(),
          tenantId: tenant.id,
          name: 'Mobile Banking',
          code: 'mobile_banking',
          description: 'Mobile banking payment (bKash, Nagad, etc.)',
          isActive: true,
          isDefault: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      await db.insert(paymentMethods).values(defaultMethods);
      console.log(`  - Created ${defaultMethods.length} payment methods`);
    }

    console.log("Payment methods seeding completed successfully!");
  } catch (error) {
    console.error("Error seeding payment methods:", error);
    process.exit(1);
  }
}

// Run the seeding function
seedPaymentMethods();
