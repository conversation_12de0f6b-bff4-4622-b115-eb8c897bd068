"use client";

import { Button } from "@/components/ui/button";
import { Printer } from "lucide-react";
import { useState } from "react";

interface DirectPrintButtonProps {
    id: string;
    size?: "default" | "sm" | "lg" | "icon";
}

export default function DirectPrintButton({ id, size = "sm" }: DirectPrintButtonProps) {
    const [loading, setLoading] = useState(false);

    const handleDirectPrint = () => {
        setLoading(true);

        // Create hidden iframe
        const iframe = document.createElement('iframe');
        iframe.style.position = 'absolute';
        iframe.style.top = '-9999px';
        iframe.style.left = '-9999px';
        iframe.style.width = '0';
        iframe.style.height = '0';
        iframe.onload = () => {
            setTimeout(() => {
                if (iframe.contentWindow) {
                    // Add event listener for afterprint
                    const cleanup = () => {
                        document.body.removeChild(iframe);
                        setLoading(false);
                        // Remove the afterprint event listener
                        if (iframe.contentWindow) {
                            iframe.contentWindow.removeEventListener('afterprint', cleanup);
                        }
                    };

                    // Add afterprint event listener
                    iframe.contentWindow.addEventListener('afterprint', cleanup);

                    // Open print dialog
                    iframe.contentWindow.print();

                    // Backup timeout in case afterprint event doesn't fire
                    setTimeout(() => {
                        if (document.body.contains(iframe)) {
                            cleanup();
                        }
                    }, 2000);
                }
            }, 500);
        };
        iframe.src = `/print-invoice/${id}`;
        document.body.appendChild(iframe);
    };

    return (
        <Button
            onClick={handleDirectPrint}
            variant="outline"
            size={size}
            disabled={loading}
            className={`cursor-pointer ${size === "sm" ? "h-7 w-7 p-0" : ""}`}
        >
            <Printer className={size === "sm" ? "h-3 w-3" : "h-4 w-4 mr-2"} />
            {size === "sm" || size === "icon" ? "" : loading ? "Printing..." : "Print"}
        </Button>
    );
}
