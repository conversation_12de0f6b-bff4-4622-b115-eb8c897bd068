import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { orders, payments, inventoryTransactions, userMenuPermissions } from "@/db/schema";
import { auth } from "../../../../../../auth";
import { eq, and } from "drizzle-orm";
import { withMenuPermission, checkMenuPermission } from "@/middleware/menuPermissions";

async function postHandler(
  request: NextRequest,
  context: { params: Promise<{ id: string }> },
  permissionCheck: any
) {
  console.log('POST handler called for sale deletion');
  try {
    const session = await auth();
    console.log('Session:', session?.user?.id ? 'Authenticated' : 'Not authenticated', 'Role:', session?.user?.role);

    if (!session?.user?.id) {
      console.log('Unauthorized: No user ID in session');
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Check if user has delete permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      console.log('Checking permissions for tenant_sale user');
      const menuPermission = permissionCheck.permissions.find(
        (p: any) => p.menu.path === '/dashboard/sales'
      );
      console.log('Menu permission found:', menuPermission ? 'Yes' : 'No', 'Can delete:', menuPermission?.canDelete);

      if (!menuPermission?.canDelete) {
        console.log('Permission denied: User does not have delete permission');
        return NextResponse.json(
          { success: false, error: "You do not have permission to delete sales" },
          { status: 403 }
        );
      }
    }

    const tenantId = session.user.tenantId || session.user.id;
    console.log('Tenant ID:', tenantId);

    if (!tenantId) {
      console.log('Tenant not found');
      return NextResponse.json(
        { message: "Tenant not found" },
        { status: 404 }
      );
    }

    const { id } = await context.params;
    console.log('Sale ID to delete:', id);

    if (!id) {
      console.log('Order ID is required but not provided');
      return NextResponse.json(
        { message: "Order ID is required" },
        { status: 400 }
      );
    }

    // Check if order exists and belongs to this tenant
    console.log('Checking if order exists for tenant:', tenantId);
    const order = await db.query.orders.findFirst({
      where: and(eq(orders.id, id), eq(orders.tenantId, tenantId)),
    });

    if (!order) {
      console.log('Order not found for this tenant');
      return NextResponse.json({ message: "Order not found" }, { status: 404 });
    }

    console.log('Order found, proceeding with deletion');

    try {
      // Use a transaction to ensure all related records are deleted
      await db.transaction(async (tx) => {
        console.log('Starting transaction for deletion');

        // 1. First, delete any payment records associated with this order
        console.log('Deleting payment records for order:', id);
        const paymentResult = await tx.delete(payments).where(eq(payments.orderId, id));
        console.log('Payment deletion result:', paymentResult);

        // 2. Delete any inventory transactions related to this sale
        console.log('Deleting inventory transactions for order:', id);
        const inventoryResult = await tx.delete(inventoryTransactions)
          .where(
            and(
              eq(inventoryTransactions.type, 'sale'),
              eq(inventoryTransactions.notes, `Sale: ${order.memoNo}`)
            )
          );
        console.log('Inventory transaction deletion result:', inventoryResult);

        // 3. Finally, delete the order (cascade will delete order items)
        console.log('Deleting order:', id);
        const orderResult = await tx.delete(orders).where(eq(orders.id, id));
        console.log('Order deletion result:', orderResult);
      });

      console.log('Order and all related records deleted successfully');
    } catch (deleteError) {
      console.error('Error during delete operation:', deleteError);
      throw deleteError; // Re-throw to be caught by the outer try/catch
    }

    // Redirect back to the sales page with a success message
    console.log('Redirecting to sales page with success message');
    return NextResponse.redirect(
      new URL("/dashboard/sales?deleted=true", request.url)
    );
  } catch (error) {
    console.error("Error deleting order:", error);

    // Log detailed error information
    if (error instanceof Error) {
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
    } else {
      console.error("Unknown error type:", error);
    }

    // Try to return a more informative error
    try {
      return NextResponse.redirect(
        new URL(
          `/dashboard/sales?error=${encodeURIComponent(
            error instanceof Error ? error.message : "Unknown error during sale deletion"
          )}`,
          request.url
        )
      );
    } catch (redirectError) {
      console.error("Error during redirect:", redirectError);
      // Fallback to a simple JSON response if redirect fails
      return NextResponse.json(
        {
          success: false,
          message: "Failed to delete order",
          error: error instanceof Error ? error.message : "Unknown error"
        },
        { status: 500 }
      );
    }
  }
}

// Export the route handlers with permission middleware
export const POST = withMenuPermission(postHandler, '/dashboard/sales');

// For debugging purposes, log when the module is loaded
console.log('Sales [id]/delete route handler registered - POST');
