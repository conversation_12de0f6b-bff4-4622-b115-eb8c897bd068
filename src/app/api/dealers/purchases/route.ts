import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { dealerPurchases, dealerPurchaseItems, dealers, inventoryTransactions, products } from "@/db/schema";
import { auth } from "~/auth";
import { v4 as uuidv4 } from "uuid";
import { eq, and, desc, sql } from "drizzle-orm";

// GET /api/dealers/purchases - Get all purchases for a dealer or all purchases
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get tenant ID based on user role
    const tenantId = session.user.role === 'tenant_sale' && session.user.tenantId
      ? session.user.tenantId
      : session.user.id;

    const { searchParams } = new URL(request.url);
    const dealerId = searchParams.get("dealerId");
    const purchaseId = searchParams.get("id");

    // Case 1: Get specific purchase by ID
    if (purchaseId) {
      const purchase = await db.query.dealerPurchases.findFirst({
        where: and(
          eq(dealerPurchases.id, purchaseId),
          eq(dealerPurchases.tenantId, tenantId)
        ),
        with: {
          dealer: true,
          branch: true,
          items: {
            with: {
              product: true,
            },
          },
          performer: {
            columns: {
              id: true,
              fullName: true,
            },
          },
        },
      });

      if (!purchase) {
        return NextResponse.json(
          { success: false, error: "Purchase not found" },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        purchase,
      });
    }

    // Case 2: Get all purchases for a specific dealer
    if (dealerId) {
      const purchases = await db.query.dealerPurchases.findMany({
        where: and(
          eq(dealerPurchases.dealerId, dealerId),
          eq(dealerPurchases.tenantId, tenantId)
        ),
        orderBy: [desc(dealerPurchases.createdAt)],
        with: {
          dealer: true,
          branch: true,
          items: {
            with: {
              product: true,
            },
          },
          performer: {
            columns: {
              id: true,
              fullName: true,
            },
          },
        },
      });

      return NextResponse.json({
        success: true,
        purchases,
      });
    }

    // Case 3: Get all purchases for the tenant
    const purchases = await db.query.dealerPurchases.findMany({
      where: eq(dealerPurchases.tenantId, tenantId),
      orderBy: [desc(dealerPurchases.createdAt)],
      with: {
        dealer: true,
        branch: true,
        items: {
          with: {
            product: true,
          },
        },
        performer: {
          columns: {
            id: true,
            fullName: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      purchases,
    });
  } catch (error) {
    console.error("Error fetching dealer purchases:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch purchases" },
      { status: 500 }
    );
  }
}

// POST /api/dealers/purchases - Create a new purchase from dealer
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const data = await request.json();
    const {
      dealerId,
      branchId,
      purchaseNo,
      date,
      items, // Array of { productId, quantity, warehouseQuantity, storeQuantity, unitPrice, discountPercentage, discountAmount, taxPercentage, taxAmount, total }
      discountPercentage = 0,
      discountAmount = 0,
      taxAmount = 0,
      paymentMethod = "cash",
      paidAmount = 0,
      notes = null,
    } = data;

    // Validate required fields
    if (!dealerId || !branchId || !purchaseNo || !items || items.length === 0) {
      return NextResponse.json(
        { success: false, error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Get tenant ID based on user role
    const tenantId = session.user.role === 'tenant_sale' && session.user.tenantId
      ? session.user.tenantId
      : session.user.id;

    // Calculate totals
    const subTotal = items.reduce((sum: number, item: any) => sum + (item.total || 0), 0);
    const totalAmount = subTotal - (discountAmount || 0) + (taxAmount || 0);
    const dueAmount = totalAmount - (paidAmount || 0);

    // Determine payment status
    let paymentStatus: "paid" | "partial" | "unpaid" = "unpaid";
    if (paidAmount >= totalAmount) {
      paymentStatus = "paid";
    } else if (paidAmount > 0) {
      paymentStatus = "partial";
    }

    const purchaseId = uuidv4();

    await db.transaction(async (tx) => {
      // Create the purchase record
      await tx.insert(dealerPurchases).values({
        id: purchaseId,
        tenantId,
        branchId,
        dealerId,
        purchaseNo,
        date: new Date(date || new Date()),
        subTotal,
        discountPercentage: discountPercentage || 0,
        discountAmount: discountAmount || 0,
        taxAmount: taxAmount || 0,
        totalAmount,
        paidAmount: paidAmount || 0,
        dueAmount,
        paymentMethod: paymentMethod as "cash" | "card" | "bank_transfer" | "credit",
        paymentStatus,
        status: "completed",
        notes: notes || null,
        performedBy: session.user.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Create purchase items and update inventory
      for (const item of items) {
        const itemId = uuidv4();

        // Create purchase item
        await tx.insert(dealerPurchaseItems).values({
          id: itemId,
          purchaseId,
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          discountPercentage: item.discountPercentage || 0,
          discountAmount: item.discountAmount || 0,
          taxPercentage: item.taxPercentage || 0,
          taxAmount: item.taxAmount || 0,
          total: item.total,
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        // Validate quantity allocation
        const warehouseQty = item.warehouseQuantity || 0;
        const storeQty = item.storeQuantity || 0;

        if (warehouseQty + storeQty !== item.quantity) {
          throw new Error(`Invalid quantity allocation for product ${item.productId}: warehouse (${warehouseQty}) + store (${storeQty}) must equal total (${item.quantity})`);
        }

        // Create inventory transaction (stock in)
        const inventoryId = uuidv4();
        await tx.insert(inventoryTransactions).values({
          id: inventoryId,
          tenantId,
          branchId,
          productId: item.productId,
          quantity: item.quantity,
          warehouseQuantity: warehouseQty,
          storeQuantity: storeQty,
          unitPrice: item.unitPrice,
          type: "in",
          reference: `Purchase from dealer: ${purchaseNo}`,
          performedBy: session.user.id,
          date: new Date(date || new Date()),
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }

      // Update dealer balance if there's a due amount
      if (dueAmount > 0) {
        await tx
          .update(dealers)
          .set({
            currentBalance: sql`${dealers.currentBalance} + ${dueAmount}`,
            updatedAt: new Date(),
          })
          .where(eq(dealers.id, dealerId));
      }
    });

    return NextResponse.json({
      success: true,
      message: "Purchase created successfully",
      purchaseId,
    });
  } catch (error) {
    console.error("Error creating dealer purchase:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to create purchase",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
