import { db } from '@/lib/db';
import { users, tenants } from '@/db/schema';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcryptjs';
import { eq } from 'drizzle-orm';
import { NextResponse } from 'next/server';
import { createMainBranchForTenant } from '@/lib/branch-utils';

export async function POST(request: Request) {
  try {
    const { email, password, name, companyName } = await request.json();

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { success: false, error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await db.query.users.findFirst({
      where: eq(users.email, email)
    });

    if (existingUser) {
      return NextResponse.json(
        { success: false, error: 'User with this email already exists' },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new user
    const userId = uuidv4();
    const username = email.split('@')[0];
    const fullName = name || username;

    await db.insert(users).values({
      id: userId,
      username,
      email,
      password: hashedPassword,
      fullName,
      role: 'tenant', // Set role as tenant by default
      isActive: true,
    });

    // Calculate subscription end date (7 days from now)
    const subscriptionEndDate = new Date();
    subscriptionEndDate.setDate(subscriptionEndDate.getDate() + 7);

    // Use company name or generate one if not provided
    const tenantCompanyName = companyName || `${fullName}'s Company`;

    // Create tenant with free trial subscription
    await db.insert(tenants).values({
      id: uuidv4(),
      userId: userId,
      companyName: tenantCompanyName,
      subscriptionType: 'free-trial',
      subscriptionStartDate: new Date(),
      subscriptionEndDate,
      paymentStatus: 'pending',
      paymentDueDate: subscriptionEndDate,
      isActive: true,
      settings: JSON.stringify({
        theme: 'light',
        notifications: true,
        language: 'en'
      })
    });

    // Create a main branch for the tenant
    const mainBranch = await createMainBranchForTenant(userId, tenantCompanyName);

    if (!mainBranch) {
      console.error('Failed to create main branch for tenant:', userId);
    } else {
      console.log('Created main branch for tenant:', mainBranch.id);
    }

    return NextResponse.json({
      success: true,
      message: 'User registered successfully with 7-day free trial',
      userId
    });
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to register user' },
      { status: 500 }
    );
  }
}