import { auth } from "~/auth";
import { db } from "@/lib/db";
import { users, userMenuPermissions } from "@/db/schema";
import { redirect } from "next/navigation";
import { eq, and } from "drizzle-orm";
import UserPermissionsForm from "@/app/components/UserPermissionsForm";
import { ALL_MENUS } from "@/lib/menu-list";

export default async function UserPermissionsPage({ params }: { params: Promise<{ id: string }> }) {
  const session = await auth();

  // Redirect to login if not authenticated
  if (!session?.user) {
    redirect("/");
  }

  // Redirect if not tenant
  if (session.user.role !== 'tenant') {
    redirect("/dashboard");
  }

  // Get user details
  const user = await db.query.users.findFirst({
    where: eq(users.id, (await params).id)
  });

  if (!user || user.role !== 'tenant_sale') {
    redirect("/dashboard/tenant/users");
  }

  // Get all available menus from file
  const availableMenus = ALL_MENUS.filter(menu => menu.isActive)
    .sort((a, b) => (a.order || 999) - (b.order || 999));

  // Get user's current permissions
  const userPermissions = await db.query.userMenuPermissions.findMany({
    where: and(
      eq(userMenuPermissions.userId, (await params).id),
      eq(userMenuPermissions.tenantId, session.user.id)
    )
  });

  // Prepare data for the form
  const menuPermissions = availableMenus
    .filter(menu => menu.id !== 'dashboard') // Filter out dashboard menu
    .map(menu => {
      const existingPermission = userPermissions.find(p => p.menuId === menu.id);

      // If no permissions exist yet, default to all permissions enabled
      const hasNoPermissions = userPermissions.length === 0;

      return {
        id: existingPermission?.id || '',
        menuId: menu.id,
        name: menu.name,
        displayName: menu.displayName,
        path: menu.path || '',
        canView: hasNoPermissions ? true : (existingPermission?.canView || false),
        canCreate: hasNoPermissions ? true : (existingPermission?.canCreate || false),
        canEdit: hasNoPermissions ? true : (existingPermission?.canEdit || false),
        canDelete: hasNoPermissions ? true : (existingPermission?.canDelete || false),
        order: menu.order || 999 // Include the order property for sorting
      };
    });

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">
        Menu Permissions for {user.fullName}
      </h1>

      <UserPermissionsForm
        userId={(await params).id}
        menuPermissions={menuPermissions}
      />
    </div>
  );
}
