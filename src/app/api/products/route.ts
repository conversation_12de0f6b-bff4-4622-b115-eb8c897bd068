import { NextRequest, NextResponse } from "next/server";
import { auth } from "~/auth";
import { db } from "@/lib/db";
import {
  products,
  productCategories,
  inventoryTransactions,
  userMenuPermissions
} from "@/db/schema";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import {
  eq,
  and,
  like,
  desc,
  asc,
  or,
  isNull,
  sum,
  max,
  count,
} from "drizzle-orm";
import { withMenuPermission, checkMenuPermission } from "@/middleware/menuPermissions";

// Schema for validating product creation and updates
const productSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  code: z.string().nullable().optional(),
  description: z.string().nullable().optional(),
  costPrice: z.coerce.number().min(0, "Cost price must be a positive number"),
  sellingPrice: z.coerce
    .number()
    .min(0, "Selling price must be a positive number"),
  discount: z.coerce.number().min(0).max(100).default(0),
  unit: z.string().min(1, "Unit is required"),
  categoryId: z.string().nullable().optional(),
  vendorId: z.string().nullable().optional(),
  royaltyType: z.enum(["none", "fixed", "percentage"]).default("none"),
  royaltyValue: z.coerce.number().min(0).default(0),
  isActive: z.boolean().default(true),
  sku: z.string().nullable().optional(),
  weight: z.coerce.number().min(0).nullable().optional(),
});

// GET /api/products - Get all products for the tenant
async function getHandler(req: NextRequest, context: any, permissionCheck: any) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get tenant ID directly from session
    console.log('Session user (GET):', JSON.stringify(session.user, null, 2));

    // For both tenant and tenant_sale users, use the same tenant ID logic
    // If tenantId is in the session, use that; otherwise use the user's ID
    let tenantId = session.user.tenantId || session.user.id;
    console.log('Using tenantId:', tenantId, 'for user role:', session.user.role);

    // Check if user has view permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      // First check if permissions are in the session
      if (session.user.menuPermissions && session.user.menuPermissions.length > 0) {
        console.log('Using menu permissions from session for products API');
        const menuPermission = session.user.menuPermissions.find(
          (p: any) => p.path === '/dashboard/products'
        );

        if (!menuPermission?.canView) {
          return NextResponse.json(
            {
              success: false,
              error: "You do not have permission to view products. Please contact your administrator to request access.",
              code: "PERMISSION_DENIED"
            },
            { status: 403 }
          );
        }
      }
      // If not in session, check from permissionCheck (which comes from middleware)
      else if (permissionCheck && permissionCheck.permissions) {
        const menuPermission = permissionCheck.permissions.find(
          (p: any) => p.menu?.path === '/dashboard/products'
        );

        if (!menuPermission?.canView) {
          return NextResponse.json(
            {
              success: false,
              error: "You do not have permission to view products. Please contact your administrator to request access.",
              code: "PERMISSION_DENIED"
            },
            { status: 403 }
          );
        }
      }
      // If neither is available, deny access
      else {
        return NextResponse.json(
          {
            success: false,
            error: "You do not have permission to view products. Please contact your administrator to request access.",
            code: "PERMISSION_DENIED"
          },
          { status: 403 }
        );
      }
    }

    // Parse query parameters
    const url = new URL(req.url);
    const productId = url.searchParams.get("id");
    console.log('GET /api/products - Product ID from URL:', productId);

    // If product ID is provided, return single product
    if (productId) {
      console.log('GET /api/products - Searching for product with ID:', productId, 'for tenant:', tenantId);
      const product = await db.query.products.findFirst({
        where: and(eq(products.id, productId), eq(products.tenantId, tenantId)),
        with: {
          category: {
            columns: {
              id: true,
              name: true,
            },
          },
        },
      });

      console.log('GET /api/products - Product found:', product ? 'Yes' : 'No');

      if (!product) {
        return NextResponse.json(
          { success: false, error: "Product not found" },
          { status: 404 }
        );
      }

      // Calculate current stock from transactions
      const inStock = await db
        .select({
          total: sum(inventoryTransactions.quantity),
        })
        .from(inventoryTransactions)
        .where(
          and(
            eq(inventoryTransactions.type, "in"),
            eq(inventoryTransactions.productId, productId),
            eq(inventoryTransactions.tenantId, tenantId)
          )
        );

      const outStock = await db
        .select({
          total: sum(inventoryTransactions.quantity),
        })
        .from(inventoryTransactions)
        .where(
          and(
            eq(inventoryTransactions.type, "out"),
            eq(inventoryTransactions.productId, productId),
            eq(inventoryTransactions.tenantId, tenantId)
          )
        );

      // Calculate current stock
      const totalIn = Number(inStock[0]?.total || 0);
      const totalOut = Number(outStock[0]?.total || 0);
      const currentStock = totalIn - totalOut;

      // Get latest transaction date
      const lastTransaction = await db
        .select({
          lastUpdate: max(inventoryTransactions.date),
        })
        .from(inventoryTransactions)
        .where(
          and(
            eq(inventoryTransactions.productId, productId),
            eq(inventoryTransactions.tenantId, tenantId)
          )
        );

      return NextResponse.json({
        success: true,
        product: {
          ...product,
          stock: currentStock,
          lastStockUpdate: lastTransaction[0]?.lastUpdate || null,
        },
      });
    }

    // For listing all products
    const categoryId = url.searchParams.get("categoryId");
    const search = url.searchParams.get("search");
    const sortBy = url.searchParams.get("sortBy") || "createdAt";
    const sortOrder = url.searchParams.get("sortOrder") || "desc";
    const isActiveOnly = url.searchParams.get("isActiveOnly") === "true";
    const isInactiveOnly = url.searchParams.get("isInactiveOnly") === "true";

    // Build the query conditions
    const conditions = [];

    // Always filter by tenant ID
    conditions.push(eq(products.tenantId, tenantId));

    if (categoryId) {
      conditions.push(eq(products.categoryId, categoryId));
    }

    // Handle active/inactive filtering
    if (isActiveOnly) {
      conditions.push(eq(products.isActive, true));
    } else if (isInactiveOnly) {
      conditions.push(eq(products.isActive, false));
    }

    if (search) {
      conditions.push(
        or(
          like(products.name, `%${search}%`),
          like(products.code || "", `%${search}%`),
          like(products.description || "", `%${search}%`)
        )
      );
    }

    // Build the sort options
    let orderBy;
    if (sortBy === "name") {
      orderBy = sortOrder === "asc" ? asc(products.name) : desc(products.name);
    } else if (sortBy === "price") {
      orderBy =
        sortOrder === "asc"
          ? asc(products.sellingPrice)
          : desc(products.sellingPrice);
    } else {
      orderBy =
        sortOrder === "asc"
          ? asc(products.createdAt)
          : desc(products.createdAt);
    }

    // Get products with their categories
    const productsData = await db.query.products.findMany({
      where: and(...conditions),
      orderBy: orderBy,
      with: {
        category: {
          columns: {
            id: true,
            name: true,
          },
        },
      },
    });

    console.log(`Found ${productsData.length} products for tenant ${tenantId}`);

    // Fetch inventory transactions for each product
    const productsWithStock = await Promise.all(
      productsData.map(async (product) => {
        // Calculate current stock from transactions
        const inStock = await db
          .select({
            total: sum(inventoryTransactions.quantity),
          })
          .from(inventoryTransactions)
          .where(
            and(
              eq(inventoryTransactions.type, "in"),
              eq(inventoryTransactions.productId, product.id),
              eq(inventoryTransactions.tenantId, tenantId)
            )
          );

        const outStock = await db
          .select({
            total: sum(inventoryTransactions.quantity),
          })
          .from(inventoryTransactions)
          .where(
            and(
              eq(inventoryTransactions.type, "out"),
              eq(inventoryTransactions.productId, product.id),
              eq(inventoryTransactions.tenantId, tenantId)
            )
          );

        // Calculate current stock
        const totalIn = Number(inStock[0]?.total || 0);
        const totalOut = Number(outStock[0]?.total || 0);
        const currentStock = totalIn - totalOut;

        // Get latest transaction date
        const lastTransaction = await db
          .select({
            lastUpdate: max(inventoryTransactions.date),
          })
          .from(inventoryTransactions)
          .where(
            and(
              eq(inventoryTransactions.productId, product.id),
              eq(inventoryTransactions.tenantId, tenantId)
            )
          );

        return {
          ...product,
          stock: currentStock,
          lastStockUpdate: lastTransaction[0]?.lastUpdate || null,
        };
      })
    );

    return NextResponse.json({
      success: true,
      products: productsWithStock,
    });
  } catch (error) {
    console.error("Error fetching products:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch products data" },
      { status: 500 }
    );
  }
}

// POST /api/products - Create a new product
async function postHandler(req: NextRequest, context: any, permissionCheck: any) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get tenant ID directly from session
    console.log('Session user:', JSON.stringify(session.user, null, 2));

    // For both tenant and tenant_sale users, use the same tenant ID logic
    // If tenantId is in the session, use that; otherwise use the user's ID
    let tenantId = session.user.tenantId || session.user.id;
    console.log('Using tenantId:', tenantId, 'for user role:', session.user.role);

    // Check if user has create permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      console.log('Checking permissions for tenant_sale user');

      // First check if permissions are in the session
      if (session.user.menuPermissions && session.user.menuPermissions.length > 0) {
        console.log('Using menu permissions from session for products API (POST)');
        const menuPermission = session.user.menuPermissions.find(
          (p: any) => p.path === '/dashboard/products'
        );

        console.log('Found menu permission from session:', menuPermission);

        if (!menuPermission?.canCreate) {
          return NextResponse.json(
            {
              success: false,
              error: "You do not have permission to create products. Please contact your administrator to request access.",
              code: "PERMISSION_DENIED"
            },
            { status: 403 }
          );
        }
      }
      // If not in session, check from permissionCheck (which comes from middleware)
      else if (permissionCheck && permissionCheck.permissions) {
        console.log('Using permissions from middleware:', permissionCheck);
        const menuPermission = permissionCheck.permissions.find(
          (p: any) => p.menu?.path === '/dashboard/products'
        );

        console.log('Found menu permission from middleware:', menuPermission);

        if (!menuPermission?.canCreate) {
          return NextResponse.json(
            {
              success: false,
              error: "You do not have permission to create products. Please contact your administrator to request access.",
              code: "PERMISSION_DENIED"
            },
            { status: 403 }
          );
        }
      }
      // If neither is available, deny access
      else {
        return NextResponse.json(
          {
            success: false,
            error: "You do not have permission to create products. Please contact your administrator to request access.",
            code: "PERMISSION_DENIED"
          },
          { status: 403 }
        );
      }
    }

    // Parse and validate the request body
    const body = await req.json();
    let validatedData;

    try {
      validatedData = productSchema.parse(body);
    } catch (validationError) {
      return NextResponse.json(
        {
          success: false,
          error: "Validation failed",
          details: validationError,
        },
        { status: 400 }
      );
    }

    if (validatedData.costPrice === undefined || validatedData.sellingPrice === undefined) {
      return NextResponse.json(
        { success: false, error: "Cost and selling prices are required" },
        { status: 400 }
      );
    }

    // Generate unique product ID
    const productId = uuidv4();

    // Generate product code if not provided
    let productCode = validatedData.code;
    if (!productCode) {
      // Get count of existing products for this tenant
      const productsCountResult = await db
        .select({ countVal: count() })
        .from(products)
        .where(eq(products.tenantId, tenantId));

      const productsCount = Number(productsCountResult[0]?.countVal || 0);
      productCode = `P${String(productsCount + 1).padStart(4, "0")}`;
    }

    // Create new product
    const newProduct = {
      id: productId,
      tenantId: tenantId,
      code: productCode,
      name: validatedData.name,
      description: validatedData.description || null,
      categoryId: validatedData.categoryId && validatedData.categoryId !== "" ? validatedData.categoryId : null,
      vendorId: validatedData.vendorId && validatedData.vendorId !== "" ? validatedData.vendorId : null,
      costPrice: validatedData.costPrice,
      sellingPrice: validatedData.sellingPrice,
      discount: validatedData.discount || 0,
      unit: validatedData.unit,
      royaltyType: validatedData.royaltyType || "none",
      royaltyValue: validatedData.royaltyValue || 0,
      isActive: validatedData.isActive !== undefined ? validatedData.isActive : true,
      sku: validatedData.sku || null,
      weight: validatedData.weight || null
    };

    try {
      await db.insert(products).values(newProduct);
    } catch (insertError) {
      console.error("Database insertion error:", insertError);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to create product",
          details: insertError instanceof Error ? insertError.message : String(insertError),
          product: newProduct
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      product: {
        ...newProduct,
        stock: 0, // Initially no stock
      },
    });
  } catch (error) {
    console.error("Error creating product:", error);
    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : '';
    console.error("Error details:", { message: errorMessage, stack: errorStack });

    return NextResponse.json(
      { success: false, error: "Failed to create product", details: errorMessage },
      { status: 500 }
    );
  }
}

// PUT /api/products - Update an existing product
async function putHandler(req: NextRequest, context: any, permissionCheck: any) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get tenant ID directly from session
    console.log('Session user (PUT):', JSON.stringify(session.user, null, 2));

    // For both tenant and tenant_sale users, use the same tenant ID logic
    // If tenantId is in the session, use that; otherwise use the user's ID
    let tenantId = session.user.tenantId || session.user.id;
    console.log('Using tenantId:', tenantId, 'for user role:', session.user.role);

    // Check if user has edit permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      // First check if permissions are in the session
      if (session.user.menuPermissions && session.user.menuPermissions.length > 0) {
        console.log('Using menu permissions from session for products API (PUT)');
        const menuPermission = session.user.menuPermissions.find(
          (p: any) => p.path === '/dashboard/products'
        );

        if (!menuPermission?.canEdit) {
          return NextResponse.json(
            { success: false, error: "You do not have permission to edit products" },
            { status: 403 }
          );
        }
      }
      // If not in session, check from permissionCheck (which comes from middleware)
      else if (permissionCheck && permissionCheck.permissions) {
        const menuPermission = permissionCheck.permissions.find(
          (p: any) => p.menu?.path === '/dashboard/products'
        );

        if (!menuPermission?.canEdit) {
          return NextResponse.json(
            { success: false, error: "You do not have permission to edit products" },
            { status: 403 }
          );
        }
      }
      // If neither is available, deny access
      else {
        return NextResponse.json(
          { success: false, error: "You do not have permission to edit products" },
          { status: 403 }
        );
      }
    }

    // Parse the URL to get the product ID
    const url = new URL(req.url);
    const urlId = url.searchParams.get("id");

    // Parse request body
    const body = await req.json();
    const { id: bodyId } = body;

    // Use URL ID if available, otherwise use body ID
    const id = urlId || bodyId;

    if (!id) {
      return NextResponse.json(
        { success: false, error: "Product ID is required" },
        { status: 400 }
      );
    }

    // Check if product exists and belongs to the tenant
    const existingProduct = await db.query.products.findFirst({
      where: and(eq(products.id, id), eq(products.tenantId, tenantId)),
    });

    if (!existingProduct) {
      return NextResponse.json(
        {
          success: false,
          error: "Product not found or does not belong to this tenant",
        },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: Record<string, any> = {
      updatedAt: new Date(),
    };

    // Only update fields that are provided
    if (body.name !== undefined) updateData.name = body.name;
    if (body.code !== undefined) updateData.code = body.code;
    if (body.description !== undefined)
      updateData.description = body.description;
    if (body.costPrice !== undefined)
      updateData.costPrice = parseFloat(body.costPrice);
    if (body.sellingPrice !== undefined)
      updateData.sellingPrice = parseFloat(body.sellingPrice);
    if (body.discount !== undefined)
      updateData.discount = parseFloat(body.discount);
    if (body.unit !== undefined) updateData.unit = body.unit;
    if (body.categoryId !== undefined) {
      // Check if categoryId is null or empty string, in which case we can directly set it to null
      if (body.categoryId === null || body.categoryId === "") {
        updateData.categoryId = null;
      } else {
        // If not null or empty, verify that the category exists and belongs to this tenant
        const category = await db.query.productCategories.findFirst({
          where: and(
            eq(productCategories.id, body.categoryId),
            eq(productCategories.tenantId, tenantId)
          ),
        });

        if (!category) {
          return NextResponse.json(
            { success: false, error: "Invalid category" },
            { status: 400 }
          );
        }

        updateData.categoryId = body.categoryId;
      }
    }
    if (body.vendorId !== undefined) {
      // Handle empty string as null
      updateData.vendorId = (body.vendorId === "") ? null : body.vendorId;
    }
    if (body.royaltyType !== undefined)
      updateData.royaltyType = body.royaltyType;
    if (body.royaltyValue !== undefined)
      updateData.royaltyValue = parseFloat(body.royaltyValue);
    if (body.isActive !== undefined) updateData.isActive = body.isActive;
    if (body.sku !== undefined) updateData.sku = body.sku;
    if (body.weight !== undefined) updateData.weight = parseFloat(body.weight);

    // Update the product
    await db
      .update(products)
      .set(updateData)
      .where(and(eq(products.id, id), eq(products.tenantId, tenantId)));

    return NextResponse.json({
      success: true,
      message: "Product updated successfully",
    });
  } catch (error) {
    console.error("Error updating product:", error);
    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : '';
    console.error("Error details:", { message: errorMessage, stack: errorStack });

    return NextResponse.json(
      { success: false, error: "Failed to update product", details: errorMessage },
      { status: 500 }
    );
  }
}

// DELETE /api/products - Delete a product
async function deleteHandler(req: NextRequest, context: any, permissionCheck: any) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get tenant ID directly from session
    console.log('Session user (DELETE):', JSON.stringify(session.user, null, 2));

    // For both tenant and tenant_sale users, use the same tenant ID logic
    // If tenantId is in the session, use that; otherwise use the user's ID
    let tenantId = session.user.tenantId || session.user.id;
    console.log('Using tenantId:', tenantId, 'for user role:', session.user.role);

    // Check if user has delete permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      // First check if permissions are in the session
      if (session.user.menuPermissions && session.user.menuPermissions.length > 0) {
        console.log('Using menu permissions from session for products API (DELETE)');
        const menuPermission = session.user.menuPermissions.find(
          (p: any) => p.path === '/dashboard/products'
        );

        if (!menuPermission?.canDelete) {
          return NextResponse.json(
            { success: false, error: "You do not have permission to delete products" },
            { status: 403 }
          );
        }
      }
      // If not in session, check from permissionCheck (which comes from middleware)
      else if (permissionCheck && permissionCheck.permissions) {
        const menuPermission = permissionCheck.permissions.find(
          (p: any) => p.menu?.path === '/dashboard/products'
        );

        if (!menuPermission?.canDelete) {
          return NextResponse.json(
            { success: false, error: "You do not have permission to delete products" },
            { status: 403 }
          );
        }
      }
      // If neither is available, deny access
      else {
        return NextResponse.json(
          { success: false, error: "You do not have permission to delete products" },
          { status: 403 }
        );
      }
    }

    // Parse the URL to get the product ID
    const url = new URL(req.url);
    const id = url.searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { success: false, error: "Product ID is required" },
        { status: 400 }
      );
    }

    // Check if product exists and belongs to the tenant
    const existingProduct = await db.query.products.findFirst({
      where: and(eq(products.id, id), eq(products.tenantId, tenantId)),
    });

    if (!existingProduct) {
      return NextResponse.json(
        {
          success: false,
          error: "Product not found or does not belong to this tenant",
        },
        { status: 404 }
      );
    }

    // Check if the product has inventory transactions
    const hasTransactions = await db.query.inventoryTransactions.findFirst({
      where: eq(inventoryTransactions.productId, id),
    });

    if (hasTransactions) {
      // Soft delete the product by setting isActive to false
      await db
        .update(products)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(and(eq(products.id, id), eq(products.tenantId, tenantId)));

      return NextResponse.json({
        success: true,
        message: "Product has been soft deleted (marked as inactive) because it has inventory transactions",
        softDelete: true
      });
    }

    // Hard delete the product if it has no inventory transactions
    await db
      .delete(products)
      .where(and(eq(products.id, id), eq(products.tenantId, tenantId)));

    return NextResponse.json({
      success: true,
      message: "Product deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting product:", error);
    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : '';
    console.error("Error details:", { message: errorMessage, stack: errorStack });

    return NextResponse.json(
      { success: false, error: "Failed to delete product", details: errorMessage },
      { status: 500 }
    );
  }
}

// Export the route handlers with permission middleware
export const GET = withMenuPermission(getHandler, '/dashboard/products');
export const POST = withMenuPermission(postHandler, '/dashboard/products');
export const PUT = withMenuPermission(putHandler, '/dashboard/products');
export const DELETE = withMenuPermission(deleteHandler, '/dashboard/products');