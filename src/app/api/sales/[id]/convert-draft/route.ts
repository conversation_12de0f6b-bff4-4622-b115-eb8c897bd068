import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import {
  orders,
  orderItems,
  products,
  inventoryTransactions,
  customers,
  royaltyTransactions,
  payments,
  userMenuPermissions
} from "@/db/schema";
import { auth } from "~/auth";
import { v4 as uuidv4 } from "uuid";
import { eq, and, sum } from "drizzle-orm";
import { withMenuPermission } from "@/middleware/menuPermissions";

async function putHandler(request: NextRequest, context: any, permissionCheck: any) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Check if user has edit permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      const menuPermission = permissionCheck.permissions.find(
        (p: any) => p.menu.path === '/dashboard/sales'
      );

      if (!menuPermission?.canEdit) {
        return NextResponse.json(
          { success: false, error: "You do not have permission to edit sales" },
          { status: 403 }
        );
      }
    }

    const { params } = context;
    const { id } = params;
    const tenantId = session.user.tenantId || session.user.id;

    // Get the draft order
    const draftOrder = await db.query.orders.findFirst({
      where: and(
        eq(orders.id, id),
        eq(orders.tenantId, tenantId),
        eq(orders.status, "draft")
      ),
      with: {
        items: true,
        branch: true,
        customer: true
      }
    });

    if (!draftOrder) {
      return NextResponse.json(
        { message: "Draft order not found" },
        { status: 404 }
      );
    }

    // Check if the draft order has items
    if (draftOrder.items.length === 0) {
      return NextResponse.json(
        { message: "Cannot convert a draft order with no items. Please add items first." },
        { status: 400 }
      );
    }

    // Check inventory for all items
    for (const item of draftOrder.items) {
      // Calculate current store stock
      const inStockConditions = [
        eq(inventoryTransactions.type, "in"),
        eq(inventoryTransactions.productId, item.productId),
        eq(inventoryTransactions.tenantId, tenantId),
        eq(inventoryTransactions.branchId, draftOrder.branchId)
      ];

      const outStockConditions = [
        eq(inventoryTransactions.type, "out"),
        eq(inventoryTransactions.productId, item.productId),
        eq(inventoryTransactions.tenantId, tenantId),
        eq(inventoryTransactions.branchId, draftOrder.branchId)
      ];

      const inStock = await db
        .select({
          storeTotal: sum(inventoryTransactions.storeQuantity),
        })
        .from(inventoryTransactions)
        .where(and(...inStockConditions));

      const outStock = await db
        .select({
          storeTotal: sum(inventoryTransactions.storeQuantity),
        })
        .from(inventoryTransactions)
        .where(and(...outStockConditions));

      const storeIn = Number(inStock[0]?.storeTotal || 0);
      const storeOut = Number(outStock[0]?.storeTotal || 0);
      const currentStoreStock = storeIn - storeOut;

      // Get product details for error message
      const product = await db.query.products.findFirst({
        where: and(
          eq(products.id, item.productId),
          eq(products.tenantId, tenantId)
        ),
      });

      // Check if there's enough stock in store for the sale
      if (item.quantity > currentStoreStock) {
        return NextResponse.json(
          {
            message: `Insufficient stock for ${product?.name}. Available: ${currentStoreStock}, Requested: ${item.quantity}`,
            product: product?.name,
            available: currentStoreStock,
            requested: item.quantity,
          },
          { status: 400 }
        );
      }
    }

    // Update the order status and process inventory
    await db.transaction(async (tx) => {
      // Update order status to completed
      await tx
        .update(orders)
        .set({
          status: "completed",
          updatedAt: new Date()
        })
        .where(eq(orders.id, id));

      // Create inventory transactions for each item
      for (const item of draftOrder.items) {
        // Create inventory transaction for the sale
        const transactionId = uuidv4();

        await tx.insert(inventoryTransactions).values({
          id: transactionId,
          tenantId: tenantId,
          branchId: draftOrder.branchId,
          productId: item.productId,
          quantity: item.quantity,
          warehouseQuantity: 0, // Only deduct from store, not warehouse
          storeQuantity: item.quantity, // Deduct from store
          unitPrice: item.unitPrice,
          type: "out",
          date: new Date(),
          notes: `Sale - Memo #${draftOrder.memoNo} (Converted from draft)`,
          performedBy: session.user.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        // Get product to check for royalties
        const product = await tx.query.products.findFirst({
          where: (products, { eq }) => eq(products.id, item.productId),
          with: {
            vendor: true,
          },
        });

        // If the product has royalties and a vendor, create royalty transaction
        if (
          product &&
          product.royaltyType !== "none" &&
          product.royaltyValue &&
          product.royaltyValue > 0 &&
          product.vendorId
        ) {
          const royaltyAmount =
            product.royaltyType === "fixed"
              ? (product.royaltyValue || 0) * item.quantity
              : (item.total * (product.royaltyValue || 0)) / 100;

          await tx.insert(royaltyTransactions).values({
            id: uuidv4(),
            vendorId: product.vendorId,
            productId: item.productId,
            transactionId,
            saleAmount: item.total,
            royaltyAmount,
            isPaid: false,
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }
      }

      // Handle payments if any
      if (draftOrder.paidAmount > 0) {
        // Create a payment record for the order
        await tx.insert(payments).values({
          id: uuidv4(),
          tenantId: tenantId,
          branchId: draftOrder.branchId,
          customerId: draftOrder.customerId,
          orderId: draftOrder.id,
          amount: draftOrder.paidAmount,
          paymentMethod: draftOrder.paymentMethod,
          paymentType: "sale",
          notes: `Payment for order #${draftOrder.memoNo} (Converted from draft)`,
          performedBy: session.user.id,
          date: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }

      // Update customer balance if applicable
      if (draftOrder.customerId) {
        const customer = await tx.query.customers.findFirst({
          where: (customers, { eq }) => eq(customers.id, draftOrder.customerId as string),
        });

        if (customer) {
          const currentBalance = customer.currentBalance || 0;
          const newOrderDue = draftOrder.dueAmount || 0;
          let newBalance = currentBalance + newOrderDue;

          await tx
            .update(customers)
            .set({
              currentBalance: newBalance,
              updatedAt: new Date(),
            })
            .where(eq(customers.id, draftOrder.customerId));
        }
      }
    });

    return NextResponse.json(
      { message: "Draft order converted to regular order successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error converting draft order:", error);
    return NextResponse.json(
      { message: "Failed to convert draft order", error: (error as Error).message },
      { status: 500 }
    );
  }
}

// Export the route handler with permission middleware
export const PUT = withMenuPermission(putHandler, '/dashboard/sales');
