import { NextResponse } from 'next/server';
import { auth } from '~/auth';
import { ALL_MENUS } from '@/lib/menu-list';

// POST /api/seed-menus - Return menu data from file
export async function POST(_request: Request) {
  try {
    // Check authentication and authorization
    const session = await auth();

    // Only tenant users can access this endpoint
    if (!session?.user || session.user.role !== 'tenant') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Only tenant admins can access menus.' },
        { status: 403 }
      );
    }

    // Get menus from file
    const availableMenus = ALL_MENUS.filter(menu => menu.isActive)
      .sort((a, b) => (a.order || 999) - (b.order || 999));

    return NextResponse.json({
      success: true,
      message: `Successfully loaded ${availableMenus.length} menus from file.`,
      menus: availableMenus
    });
  } catch (error) {
    console.error('Error seeding menus:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to seed menus' },
      { status: 500 }
    );
  }
}
