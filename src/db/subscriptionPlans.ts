/**
 * Simple subscription plans for the application.
 * Only two plans are supported: free-trial and paid
 */

// Simple function to get plan by ID
export function getPlanById(planId: string) {
  if (planId === "free-trial") {
    return {
      id: "free-trial",
      name: "Free Trial",
      description: "7 days of basic access",
      price: 0,
      isActive: true,
    };
  }

  // Default to paid plan
  return {
    id: "paid",
    name: "Paid Plan",
    description: "Full featured access",
    price: 29.99,
    isActive: true,
  };
}

// Get free trial plan
export function getFreeTrial() {
  return getPlanById("free-trial");
}

// Array of available plans
export const subscriptionPlans = [
  getPlanById("free-trial"),
  getPlanById("paid"),
];
