import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users, tenantUsers, userMenuPermissions, branchUsers, branches } from "@/db/schema";
import { auth } from "~/auth";
import { v4 as uuidv4 } from "uuid";
import bcrypt from "bcryptjs";
import { eq } from "drizzle-orm";
import { ALL_MENUS } from "@/lib/menu-list";

// Define interface for menu permission
interface MenuPermission {
  menuId: string;
  canView?: boolean;
  canCreate?: boolean;
  canEdit?: boolean;
  canDelete?: boolean;
  order?: number;
  [key: string]: any; // For any additional properties
}

// POST /api/tenant/users/create - Create a new tenant_sale user with menu permissions
export async function POST(request: Request) {
  try {
    // Check authentication and authorization
    const session = await auth();

    // Only tenant users can create tenant_sale users
    if (!session?.user || session.user.role !== 'tenant') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Only tenant admins can create tenant_sale users.' },
        { status: 403 }
      );
    }

    // Parse request body
    const {
      username,
      email,
      fullName,
      password,
      isActive = true,
      branchId,
      menuPermissions = []
    } = await request.json();

    // Validate required fields
    if (!username || !email || !fullName || !password || !branchId) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Verify that the branch exists and belongs to this tenant
    const branch = await db.query.branches.findFirst({
      where: (branches, { eq, and }) => and(
        eq(branches.id, branchId),
        eq(branches.tenantId, session.user.id)
      )
    });

    if (!branch) {
      return NextResponse.json(
        { success: false, error: 'Invalid branch selected' },
        { status: 400 }
      );
    }

    // Check if user with email already exists
    const existingUser = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.email, email)
    });

    if (existingUser) {
      return NextResponse.json(
        { success: false, error: 'User with this email already exists' },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new user with tenant_sale role
    const userId = uuidv4();
    const newUser = {
      id: userId,
      username,
      email,
      fullName,
      password: hashedPassword,
      role: "tenant_sale" as const,
      isActive: isActive !== undefined ? isActive : true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await db.insert(users).values(newUser);

    // Create tenant-user relationship
    await db.insert(tenantUsers).values({
      id: uuidv4(),
      tenantId: session.user.id, // Using tenant's user ID as tenantId
      userId: userId,
      role: "staff" as const, // Default role for tenant_sale users
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Create branch-user relationship
    await db.insert(branchUsers).values({
      id: uuidv4(),
      branchId: branchId,
      userId: userId,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Process menu permissions
    try {
      if (menuPermissions && menuPermissions.length > 0) {
        // Use the provided menu permissions
        // First, ensure they have order information from ALL_MENUS
        const enhancedPermissions = menuPermissions.map((permission: MenuPermission) => {
          const menuFromFile = ALL_MENUS.find(m => m.id === permission.menuId);
          return {
            ...permission,
            order: menuFromFile?.order || 999
          };
        });

        // Sort by order
        const sortedPermissions = enhancedPermissions.sort((a: MenuPermission, b: MenuPermission) =>
          ((a.order || 999) - (b.order || 999))
        );

        // Filter out dashboard permissions
        const filteredPermissions = sortedPermissions.filter(
          (permission: MenuPermission) => permission.menuId !== 'dashboard'
        );

        // Create permissions to insert
        const permissionsToInsert = filteredPermissions.map((permission: MenuPermission) => ({
          id: uuidv4(),
          userId: userId,
          menuId: permission.menuId,
          tenantId: session.user.id, // Using tenant's user ID as tenantId
          canView: permission.canView !== undefined ? permission.canView : true,
          canCreate: permission.canCreate !== undefined ? permission.canCreate : true,
          canEdit: permission.canEdit !== undefined ? permission.canEdit : true,
          canDelete: permission.canDelete !== undefined ? permission.canDelete : true,
          createdAt: new Date(),
          updatedAt: new Date()
        }));

        await db.insert(userMenuPermissions).values(permissionsToInsert);
        console.log(`Inserted ${permissionsToInsert.length} menu permissions for user ${userId}`);
      } else {
        // If no menu permissions provided, use ALL_MENUS from file and grant full permissions
        if (ALL_MENUS.length > 0) {
          // Sort menus by order
          const sortedMenus = [...ALL_MENUS].sort((a, b) => ((a.order || 999) - (b.order || 999)));

          // Filter out dashboard menu
          const filteredMenus = sortedMenus.filter(menu => menu.id !== 'dashboard');

          const allPermissions = filteredMenus.map(menu => ({
            id: uuidv4(),
            userId: userId,
            menuId: menu.id,
            tenantId: session.user.id,
            canView: true,
            canCreate: true,
            canEdit: true,
            canDelete: true,
            createdAt: new Date(),
            updatedAt: new Date()
          }));

          await db.insert(userMenuPermissions).values(allPermissions);
          console.log(`Inserted ${allPermissions.length} default menu permissions for user ${userId}`);
        }
      }
    } catch (error) {
      console.error('Error inserting menu permissions:', error);
      // Continue with user creation even if permissions fail
      // This allows the user to be created and permissions can be set later
    }

    // Return success without password
    const { password: _, ...userWithoutPassword } = newUser;

    return NextResponse.json({
      success: true,
      user: userWithoutPassword,
      message: 'Tenant sale user created successfully'
    });
  } catch (error) {
    console.error('Error creating tenant_sale user:', error);

    // Provide a more detailed error message
    let errorMessage = 'Failed to create tenant_sale user';

    // Check if it's a foreign key constraint error
    if (error instanceof Error && error.message.includes('FOREIGN KEY constraint failed')) {
      errorMessage = 'Foreign key constraint error. This might be because the menus table is not populated. Run the seed-menus script first.';
    }

    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}
