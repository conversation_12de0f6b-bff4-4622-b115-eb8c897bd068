import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { customers } from "@/db/schema";
import { auth } from "~/auth";
import { eq } from "drizzle-orm";

export async function GET(request: NextRequest) {
  try {
    // Get authenticated session
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user?.id) {
      return NextResponse.json({ success: false, error: "Unauthorized" }, { status: 401 });
    }

    // Extract tenant ID from session
    const tenantId = session.user.tenantId || session.user.id;

    // Fetch all customers for the tenant
    const allCustomers = await db
      .select()
      .from(customers)
      .where(eq(customers.tenantId, tenantId))
      .orderBy(customers.name);

    // Return successful response
    return NextResponse.json({ 
      success: true, 
      customers: allCustomers 
    });
  } catch (error) {
    // Handle errors properly
    console.error("Error fetching all customers:", error);

    return NextResponse.json(
      { success: false, error: "Failed to fetch customers" },
      { status: 500 }
    );
  }
}
