import { NextRequest, NextResponse } from "next/server";
import { auth } from "~/auth";
import { pathRequiresPermission } from "@/lib/menu-list";

// GET /api/user/permissions - Get current user's menu permissions
export async function GET(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get the menu path from the query parameters
    const url = new URL(req.url);
    const menuPath = url.searchParams.get('menuPath');

    if (!menuPath) {
      return NextResponse.json(
        { success: false, error: "Menu path is required" },
        { status: 400 }
      );
    }

    // Check if the path requires permission
    const requiresPermission = pathRequiresPermission(menuPath);

    // If path doesn't require permission (like dashboard) or is not a menu path
    if (!requiresPermission) {
      console.log(`Path ${menuPath} doesn't require permission check`);
      return NextResponse.json({
        success: true,
        permissions: {
          canView: true,
          canCreate: true,
          canEdit: true,
          canDelete: true
        }
      });
    }

    // Admin and tenant users have full permissions for all menus
    if (session.user.role === 'admin' || session.user.role === 'tenant') {
      return NextResponse.json({
        success: true,
        permissions: {
          canView: true,
          canCreate: true,
          canEdit: true,
          canDelete: true
        }
      });
    }

    // For tenant_sale users, check permissions from session
    if (session.user.role === 'tenant_sale') {
      console.log(`Checking permissions for tenant_sale user for path: ${menuPath}`);

      // If no menu permissions in session, deny access
      if (!session.user.menuPermissions || session.user.menuPermissions.length === 0) {
        console.log(`No menu permissions found in session for tenant_sale user`);
        return NextResponse.json({
          success: true,
          permissions: {
            canView: false,
            canCreate: false,
            canEdit: false,
            canDelete: false
          }
        });
      }

      // Find the menu permission for this path
      const menuPermission = session.user.menuPermissions.find(
        (p: any) => p.path === menuPath
      );

      if (!menuPermission) {
        console.log(`No permission found for path ${menuPath}`);
        return NextResponse.json({
          success: true,
          permissions: {
            canView: false,
            canCreate: false,
            canEdit: false,
            canDelete: false
          }
        });
      }

      // Return the user's permissions for this menu
      return NextResponse.json({
        success: true,
        permissions: {
          canView: menuPermission.canView,
          canCreate: menuPermission.canCreate,
          canEdit: menuPermission.canEdit,
          canDelete: menuPermission.canDelete
        }
      });
    }

    // Default permissions (no access)
    return NextResponse.json({
      success: true,
      permissions: {
        canView: false,
        canCreate: false,
        canEdit: false,
        canDelete: false
      }
    });
  } catch (error) {
    console.error("Error fetching user permissions:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch user permissions" },
      { status: 500 }
    );
  }
}
