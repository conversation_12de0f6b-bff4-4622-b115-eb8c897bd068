import React from "react";
import { db } from "@/lib/db";
import { customers } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { notFound, redirect } from "next/navigation";
import { Metadata } from "next";
import { auth } from "~/auth";
import ClientWrapper from "./_components/client-wrapper";

export const metadata: Metadata = {
    title: "Customer Details",
    description: "View customer details",
};

export default async function CustomerDetailPage({
    params,
}: {
    params: Promise<{ id: string }>;
}) {
    const session = await auth();
    if (!session?.user) {
        redirect("/api/auth/signin");
    }

    // Get tenant ID based on user role
    const tenantId = session.user.tenantId
    if (!tenantId) {
        redirect("/dashboard");
    }

    const { id } = await params;
    const customerId = id;

    // Fetch customer details
    const customer = await db.query.customers.findFirst({
        where: (customers, { eq, and }) =>
            and(eq(customers.id, customerId), eq(customers.tenantId, tenantId)),
    });

    // Handle case when customer not found
    if (!customer) {
        notFound();
    }

    // Format dates for display
    const formattedCreatedAt = customer.createdAt ? new Date(customer.createdAt).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
    }) : "—";

    return (
        <div className="container mx-auto py-10">
            <ClientWrapper
                customer={customer}
                formattedCreatedAt={formattedCreatedAt}
            />
        </div>
    );
}