'use client';

import { useEffect } from 'react';
import { DEFAULT_TIMEZONE } from '@/lib/timezone';

/**
 * TimezoneProvider component that sets the default timezone for the application
 * This component doesn't render anything, it just sets the timezone
 */
export function TimezoneProvider() {
  useEffect(() => {
    // Set timezone for the application
    // This is a client-side effect that will run once when the component mounts
    try {
      // This is just to log the timezone for debugging purposes
      console.log(`Setting timezone to ${DEFAULT_TIMEZONE}`);
      
      // The actual timezone is handled by our utility functions
      // This is just for documentation purposes
    } catch (error) {
      console.error('Error setting timezone:', error);
    }
  }, []);

  // This component doesn't render anything
  return null;
}
