import { auth } from "../../auth";
import { ALL_MENUS, getAllMenusWithPermissions, pathRequiresPermission } from "./menu-list";

/**
 * Checks if the current user has permission to access a specific menu
 * @param menuPath The path of the menu to check
 * @returns Boolean indicating if the user has permission
 */
export async function hasMenuPermission(menuPath: string): Promise<boolean> {
  const session = await auth();

  if (!session?.user) {
    return false;
  }

  // Check if the path requires permission
  const requiresPermission = pathRequiresPermission(menuPath);

  // If path doesn't require permission (like dashboard) or is not a menu path
  if (!requiresPermission) {
    return true;
  }

  // Admin and tenant users have access to all menus
  if (session.user.role === 'admin' || session.user.role === 'tenant') {
    return true;
  }

  // For tenant_sale users, check permissions from session
  if (session.user.role === 'tenant_sale') {
    // If no menu permissions in session, deny access
    if (!session.user.menuPermissions || session.user.menuPermissions.length === 0) {
      return false;
    }

    // Check if user has permission to access this menu
    return session.user.menuPermissions.some(
      (p: any) => p.path === menuPath && p.canView
    );
  }

  return false;
}

/**
 * Gets all menu permissions for the current user
 * @returns Array of menu permissions with menu details
 */
export async function getUserMenuPermissions() {
  const session = await auth();

  if (!session?.user) {
    return [];
  }

  // For tenant_sale users, get permissions from session or database
  if (session.user.role === 'tenant_sale') {
    // If permissions are in session, use them
    if (session.user.menuPermissions && session.user.menuPermissions.length > 0) {
      console.log('Using menu permissions from session');

      // Ensure dashboard is always accessible
      const dashboardMenu = ALL_MENUS.find(menu => menu.name === 'dashboard');
      const hasDashboard = session.user.menuPermissions.some(
        (p: any) => p.name === 'dashboard'
      );

      let permissions = [...session.user.menuPermissions];

      // Add dashboard if not already present
      if (!hasDashboard && dashboardMenu) {
        permissions.push({
          menuId: dashboardMenu.id,
          name: dashboardMenu.name,
          displayName: dashboardMenu.displayName,
          path: dashboardMenu.path,
          userId: session.user.id,
          tenantId: session.user.tenantId || '',
          canView: true,
          canCreate: true,
          canEdit: true,
          canDelete: true,
          order: dashboardMenu.order
        });
      }

      // Add menu details to each permission and add order property
      const permissionsWithMenus = permissions.map(permission => {
        const menu = ALL_MENUS.find(m => m.id === permission.menuId || m.name === permission.name);

        // Get the order from the menu or use a high number if menu not found
        const order = menu ? menu.order : 999;

        return {
          ...permission,
          order,
          menu: menu || {
            id: permission.menuId,
            name: permission.name,
            displayName: permission.displayName,
            path: permission.path,
            isActive: true
          }
        };
      });

      // Sort permissions by menu order
      return permissionsWithMenus.sort((a, b) => a.order - b.order);
    }

    // If no permissions in session, return only dashboard
    console.log('No menu permissions in session, returning only dashboard');
    const dashboardMenu = ALL_MENUS.find(menu => menu.name === 'dashboard');
    if (dashboardMenu) {
      return [{
        menuId: dashboardMenu.id,
        name: dashboardMenu.name,
        displayName: dashboardMenu.displayName,
        path: dashboardMenu.path,
        userId: session.user.id,
        tenantId: session.user.tenantId || '',
        canView: true,
        canCreate: true,
        canEdit: true,
        canDelete: true,
        order: dashboardMenu.order,
        menu: dashboardMenu
      }];
    }

    return [];
  }

  // For admin and tenant users, return all menus with full permissions
  const menuPermissions = getAllMenusWithPermissions(
    session.user.id,
    session.user.role === 'tenant' ? session.user.id : undefined
  );

  // Add menu details to each permission
  const permissionsWithMenus = menuPermissions.map(permission => {
    const menu = ALL_MENUS.find(m => m.id === permission.menuId || m.name === permission.name);

    return {
      ...permission,
      menu: menu || {
        id: permission.menuId,
        name: permission.name,
        displayName: permission.displayName,
        path: permission.path,
        isActive: true
      }
    };
  });

  // Menu permissions are already sorted by order from getAllMenusWithPermissions
  return permissionsWithMenus;
}
