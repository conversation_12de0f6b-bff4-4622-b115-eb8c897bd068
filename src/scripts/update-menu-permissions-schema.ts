import { createClient } from '@libsql/client';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env' });

// Database client
const client = createClient({
  url: process.env.TURSO_DATABASE_URL as string,
  authToken: process.env.TURSO_AUTH_TOKEN as string,
});

async function updateSchema() {
  try {
    console.log('Updating user_menu_permissions table schema...');

    // Execute each SQL statement separately
    // Turn off foreign keys
    await client.execute(`PRAGMA foreign_keys=OFF;`);
    console.log('Foreign keys disabled');

    // Create a new table without the foreign key constraint
    await client.execute(`
      CREATE TABLE __new_user_menu_permissions (
        id text PRIMARY KEY NOT NULL,
        user_id text NOT NULL,
        menu_id text NOT NULL,
        tenant_id text NOT NULL,
        can_view integer DEFAULT true,
        can_create integer DEFAULT false,
        can_edit integer DEFAULT false,
        can_delete integer DEFAULT false,
        created_at integer,
        updated_at integer,
        FOREIGN KEY (user_id) REFERENCES users(id) ON UPDATE no action ON DELETE cascade,
        FOREIGN KEY (tenant_id) REFERENCES users(id) ON UPDATE no action ON DELETE cascade
      );
    `);
    console.log('New table created');

    // Copy data from the old table to the new one
    await client.execute(`
      INSERT INTO __new_user_menu_permissions
        SELECT id, user_id, menu_id, tenant_id, can_view, can_create, can_edit, can_delete, created_at, updated_at
        FROM user_menu_permissions;
    `);
    console.log('Data copied to new table');

    // Drop the old table
    await client.execute(`DROP TABLE user_menu_permissions;`);
    console.log('Old table dropped');

    // Rename the new table to the original name
    await client.execute(`ALTER TABLE __new_user_menu_permissions RENAME TO user_menu_permissions;`);
    console.log('New table renamed');

    // Turn foreign keys back on
    await client.execute(`PRAGMA foreign_keys=ON;`);
    console.log('Foreign keys enabled');

    console.log('Schema update completed successfully!');
  } catch (error) {
    console.error('Error updating schema:', error);
  } finally {
    // Close the client connection
    await client.close();
  }
}

// Run the function
updateSchema()
  .then(() => {
    console.log('Schema update complete.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error during schema update:', error);
    process.exit(1);
  });
