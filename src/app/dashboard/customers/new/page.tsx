import { Metadata } from "next";
import Link from "next/link";
import { redirect } from "next/navigation";
import { auth } from "~/auth";
import ClientWrapper from "./_components/client-wrapper";
import { UserPlus, ArrowLeft } from "lucide-react";

export const metadata: Metadata = {
    title: "Add New Customer",
    description: "Add a new customer to your system",
};

export default async function NewCustomerPage() {
    const session = await auth();
    if (!session?.user) {
        redirect("/api/auth/signin");
    }

    // Get tenant ID based on user role
    const tenantId = session.user.role === 'tenant_sale' && session.user.tenantId
        ? session.user.tenantId
        : session.user.id;

    return (
        <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white bg-[url('/subtle-pattern.png')] bg-opacity-50">
            <div className="container max-w-6xl mx-auto">
                <div className="flex justify-between items-center mb-4">
                    <h1 className="text-xl font-bold mb-2 flex items-center">
                        <span className="bg-white/20 p-2 rounded-full mr-3 shadow-inner">
                            <UserPlus className="h-4 w-4" />
                        </span>
                        Add New Customer
                    </h1>
                    <Link
                        href="/dashboard/customers"
                        className="flex items-center gap-2 bg-gray-900 text-white px-4 py-3 rounded-lg transition-all duration-200 backdrop-blur-sm border border-white/20 shadow-sm self-start md:self-center relative z-10"
                    >
                        <ArrowLeft className="h-4 w-4" />
                        Back to Customers
                    </Link>
                </div>

                <ClientWrapper tenantId={tenantId} />
            </div>
        </div>
    );
}