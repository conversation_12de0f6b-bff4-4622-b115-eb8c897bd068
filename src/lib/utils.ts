import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { formatLocalizedDate, DEFAULT_TIMEZONE, getDhakaDate } from "./timezone";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Format a date object or timestamp to a readable date string
 * Uses the default timezone (Asia/Dhaka)
 */
export function formatDate(date: Date | number | string): string {
  if (!date) return "";
  return formatLocalizedDate(date, {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
}

/**
 * Format a number to a currency string
 */
export function formatCurrency(amount: number): string {
  if (amount == null) return "";
  return `৳ ${new Intl.NumberFormat('en-US', {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount)}`;
}

/**
 * Generate a unique memo number with prefix and current timestamp
 * Uses the Dhaka timezone for timestamp generation
 */
export function generateMemoNumber(prefix: string = "INV"): string {
  const timestamp = getDhakaDate().getTime();
  return `${prefix}-${timestamp}`;
}

/**
 * Calculate due amount based on total and paid amount
 * Returns 0 if the difference is very small (less than 0.01) to handle floating-point precision issues
 */
export function calculateDueAmount(total: number, paid: number): number {
  const due = Number((total - paid).toFixed(2));
  return due <= 0.01 ? 0 : due;
}
