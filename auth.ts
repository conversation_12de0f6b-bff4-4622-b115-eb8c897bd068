import NextAuth from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { db } from "./src/lib/db";
import { users, tenants, tenantUsers, userMenuPermissions, branchUsers } from "./src/db/schema";
import { eq, and } from "drizzle-orm";
import bcrypt from "bcryptjs";
import { type NextRequest } from "next/server";
import { ALL_MENUS, getAllMenusWithPermissions } from "./src/lib/menu-list";

export const { handlers, signIn, signOut, auth } = NextAuth({
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials, _request) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // Find user by email
        const user = await db
          .select()
          .from(users)
          .where(eq(users.email, credentials?.email as string))
          .get();

        if (!user) {
          return null;
        }

        // Check if user is active
        if (!user.isActive) {
          throw new Error("Your account has been deactivated. Please contact your administrator.");
        }

        // Verify password
        const passwordMatch = await bcrypt.compare(
          credentials?.password as string,
          user.password
        );

        if (!passwordMatch) {
          return null;
        }

        // Get tenantId, branchId, and menuPermissions based on user role
        let tenantId = null;
        let branchId = null;
        let menuPermissions = null;

        if (user.role === "tenant") {
          // Get tenant directly
          const tenant = await db
            .select()
            .from(tenants)
            .where(eq(tenants.userId, user.id))
            .get();

          if (tenant && tenant.isActive) {
            // Check if subscription is valid
            const now = new Date();
            if (
              !tenant.subscriptionEndDate ||
              tenant.subscriptionEndDate > now
            ) {
              // For tenant users, store their own user ID as the tenantId
              tenantId = user.id;
            }
          }
        } else if (user.role === "tenant_sale") {
          // First check if the sales user is active
          if (!user.isActive) {
            throw new Error("Your account has been deactivated. Please contact your administrator.");
          }

          // Find the associated tenant
          const tenantUser = await db
            .select()
            .from(tenantUsers)
            .where(eq(tenantUsers.userId, user.id))
            .get();

          if (tenantUser) {
            // Check if the tenant-user relationship is active
            if (!tenantUser.isActive) {
              throw new Error("Your access to this tenant has been deactivated. Please contact your administrator.");
            }

            // Get the tenant (which is actually a user with tenant role)
            const tenantOwner = await db
              .select()
              .from(users)
              .where(eq(users.id, tenantUser.tenantId))
              .get();

            if (tenantOwner && tenantOwner.isActive) {
              // Get the tenant record for subscription check
              const tenant = await db
                .select()
                .from(tenants)
                .where(eq(tenants.userId, tenantOwner.id))
                .get();

              if (tenant && tenant.isActive) {
                // Check if subscription is valid
                const now = new Date();
                if (tenant.subscriptionEndDate && tenant.subscriptionEndDate > now) {
                  // Check if user has any menu permissions
                  const permissions = await db
                    .select()
                    .from(userMenuPermissions)
                    .where(
                      and(
                        eq(userMenuPermissions.userId, user.id),
                        eq(userMenuPermissions.tenantId, tenantOwner.id)
                      )
                    )
                    .all();

                  // Only allow login if user has at least one menu permission
                  if (permissions && permissions.length > 0) {
                    // Store the tenant_id from tenant_users table for tenant_sale users
                    tenantId = tenantUser.tenantId;

                    // Get the branch assigned to this user
                    const userBranch = await db
                      .select()
                      .from(branchUsers)
                      .where(eq(branchUsers.userId, user.id))
                      .get();

                    if (userBranch) {
                      branchId = userBranch.branchId;
                    }

                    // Fetch user permissions from database
                    const userPermissions = await db
                      .select()
                      .from(userMenuPermissions)
                      .where(
                        and(
                          eq(userMenuPermissions.userId, user.id),
                          eq(userMenuPermissions.tenantId, tenantOwner.id)
                        )
                      )
                      .all();

                    // Use file-based menu definitions and apply user permissions
                    if (userPermissions && userPermissions.length > 0) {
                      // Create a map of menu permissions by menuId
                      const permissionsMap = new Map();
                      userPermissions.forEach(permission => {
                        permissionsMap.set(permission.menuId, {
                          canView: permission.canView,
                          canCreate: permission.canCreate,
                          canEdit: permission.canEdit,
                          canDelete: permission.canDelete
                        });
                      });

                      // Format menu permissions for storage in the session by combining file menus with DB permissions
                      menuPermissions = ALL_MENUS.map(menu => {
                        const userPermission = permissionsMap.get(menu.id) || {
                          canView: false,
                          canCreate: false,
                          canEdit: false,
                          canDelete: false
                        };

                        // Dashboard always has full permissions
                        if (menu.name === 'dashboard') {
                          userPermission.canView = true;
                          userPermission.canCreate = true;
                          userPermission.canEdit = true;
                          userPermission.canDelete = true;
                        }

                        return {
                          menuId: menu.id,
                          name: menu.name,
                          displayName: menu.displayName,
                          userId: user.id,
                          tenantId: tenantOwner.id,
                          path: menu.path,
                          canView: userPermission.canView,
                          canCreate: userPermission.canCreate,
                          canEdit: userPermission.canEdit,
                          canDelete: userPermission.canDelete,
                          order: menu.order
                        };
                      });

                      // Make sure dashboard is always included with full permissions
                      const hasDashboard = menuPermissions.some(menu => menu.name === 'dashboard');
                      if (!hasDashboard) {
                        const dashboardMenu = ALL_MENUS.find(menu => menu.name === 'dashboard');
                        if (dashboardMenu) {
                          menuPermissions.push({
                            menuId: dashboardMenu.id,
                            name: dashboardMenu.name,
                            displayName: dashboardMenu.displayName,
                            userId: user.id,
                            tenantId: tenantOwner.id,
                            path: dashboardMenu.path,
                            canView: true,
                            canCreate: true,
                            canEdit: true,
                            canDelete: true,
                            order: dashboardMenu.order
                          });
                        }
                      }

                      // Sort by menu order
                      menuPermissions.sort((a, b) => a.order - b.order);
                    }
                  }
                }
              }
            }
          }
        }

        // For tenant and tenant_sale roles, check if tenant is inactive or subscription expired
        if ((user.role === "tenant" || user.role === "tenant_sale") && !tenantId) {
          // If tenantId is null, it means either:
          // 1. Tenant is inactive
          // 2. Subscription has expired
          // 3. User has no permissions (for tenant_sale)

          // Check specific reason for tenant users
          if (user.role === "tenant") {
            const tenant = await db
              .select()
              .from(tenants)
              .where(eq(tenants.userId, user.id))
              .get();

            if (tenant && !tenant.isActive) {
              throw new Error("Your tenant account has been deactivated. Please contact admin for assistance.");
            }

            if (tenant && tenant.subscriptionEndDate) {
              const now = new Date();
              if (tenant.subscriptionEndDate < now) {
                throw new Error("Your subscription has expired. Please contact admin to renew your subscription.");
              }
            }
          }
          // Check specific reason for tenant_sale users
          else if (user.role === "tenant_sale") {
            // First check if the user itself is active
            if (!user.isActive) {
              throw new Error("Your account has been deactivated. Please contact your administrator.");
            }

            const tenantUser = await db
              .select()
              .from(tenantUsers)
              .where(eq(tenantUsers.userId, user.id))
              .get();

            if (!tenantUser) {
              throw new Error("Your account is not associated with any tenant. Please contact your administrator.");
            }

            // Check if the tenant-user relationship is active
            if (!tenantUser.isActive) {
              throw new Error("Your access to this tenant has been deactivated. Please contact your administrator.");
            }

            const tenantOwner = await db
              .select()
              .from(users)
              .where(eq(users.id, tenantUser.tenantId))
              .get();

            if (!tenantOwner) {
              throw new Error("The tenant associated with your account could not be found. Please contact your administrator.");
            }

            if (!tenantOwner.isActive) {
              throw new Error("The tenant account has been deactivated. Please contact your administrator.");
            }

            const tenant = await db
              .select()
              .from(tenants)
              .where(eq(tenants.userId, tenantOwner.id))
              .get();

            if (!tenant) {
              throw new Error("The tenant license could not be found. Please contact your administrator.");
            }

            if (!tenant.isActive) {
              throw new Error("The tenant account has been deactivated. Please contact admin for assistance.");
            }

            if (tenant.subscriptionEndDate) {
              const now = new Date();
              if (tenant.subscriptionEndDate < now) {
                throw new Error("The tenant's subscription has expired. Please contact admin to renew the subscription.");
              }
            }

            // Check if user has permissions
            const permissions = await db
              .select()
              .from(userMenuPermissions)
              .where(
                and(
                  eq(userMenuPermissions.userId, user.id),
                  eq(userMenuPermissions.tenantId, tenantOwner.id)
                )
              )
              .all();

            if (!permissions || permissions.length === 0) {
              throw new Error("You don't have any permissions in this tenant. Please contact your administrator.");
            }

            // If we got here, we should set the tenantId and load menu permissions
            // This is a fallback in case the main flow didn't set these values
            if (!tenantId) {
              tenantId = tenantOwner.id;

              // Get the branch assigned to this user if not already set
              if (!branchId) {
                const userBranch = await db
                  .select()
                  .from(branchUsers)
                  .where(eq(branchUsers.userId, user.id))
                  .get();

                if (userBranch) {
                  branchId = userBranch.branchId;
                }
              }

              // Load menu permissions if not already loaded
              if (!menuPermissions) {
                // Create a map of menu permissions by menuId
                const permissionsMap = new Map();
                permissions.forEach(permission => {
                  permissionsMap.set(permission.menuId, {
                    canView: permission.canView,
                    canCreate: permission.canCreate,
                    canEdit: permission.canEdit,
                    canDelete: permission.canDelete
                  });
                });

                // Format menu permissions for storage in the session
                menuPermissions = ALL_MENUS.map(menu => {
                  const userPermission = permissionsMap.get(menu.id) || {
                    canView: false,
                    canCreate: false,
                    canEdit: false,
                    canDelete: false
                  };

                  // Dashboard always has full permissions
                  if (menu.name === 'dashboard') {
                    userPermission.canView = true;
                    userPermission.canCreate = true;
                    userPermission.canEdit = true;
                    userPermission.canDelete = true;
                  }

                  return {
                    menuId: menu.id,
                    name: menu.name,
                    displayName: menu.displayName,
                    userId: user.id,
                    tenantId: tenantOwner.id,
                    path: menu.path,
                    canView: userPermission.canView,
                    canCreate: userPermission.canCreate,
                    canEdit: userPermission.canEdit,
                    canDelete: userPermission.canDelete,
                    order: menu.order
                  };
                });

                // Make sure dashboard is always included
                const hasDashboard = menuPermissions.some(menu => menu.name === 'dashboard');
                if (!hasDashboard) {
                  const dashboardMenu = ALL_MENUS.find(menu => menu.name === 'dashboard');
                  if (dashboardMenu) {
                    menuPermissions.push({
                      menuId: dashboardMenu.id,
                      name: dashboardMenu.name,
                      displayName: dashboardMenu.displayName,
                      userId: user.id,
                      tenantId: tenantOwner.id,
                      path: dashboardMenu.path,
                      canView: true,
                      canCreate: true,
                      canEdit: true,
                      canDelete: true,
                      order: dashboardMenu.order
                    });
                  }
                }

                // Sort by menu order
                menuPermissions.sort((a, b) => a.order - b.order);
              }
            }
          }

          // If we got here without finding a specific error, return a generic message
          throw new Error("Unable to log in. Please contact admin for assistance.");
        }

        // Return user object without password
        return {
          id: user.id,
          name: user.fullName, // Using fullName from schema as name for NextAuth
          email: user.email,
          role: user.role,
          tenantId: tenantId,
          branchId: branchId,
          menuPermissions: menuPermissions,
        };
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      // Add user data to token when signing in
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.tenantId = user.tenantId;
        token.branchId = user.branchId;
        token.menuPermissions = user.menuPermissions;
      }
      return token;
    },
    async session({ session, token }) {
      // Add user data to session
      if (session.user) {
        session.user.id = token.id as string;
        session.user.role = token.role as string;
        session.user.tenantId = token.tenantId as string | null;
        session.user.branchId = token.branchId as string | null;
        session.user.menuPermissions = token.menuPermissions as any[] | null;
      }
      return session;
    },
  },
  pages: {
    signIn: "/", // Use the home page with AuthForms component
    error: "/", // Error also goes to home page
  },
  events: {
    async signIn({ user }) {
      // Log successful sign-ins
      console.log("User signed in:", user.email);
    },
  },
  session: {
    strategy: "jwt",
    maxAge: 60 * 60 * 12, // 12 hours
  },
  secret: process.env.NEXTAUTH_SECRET,
});

// Session type is already defined in src/types/next-auth.d.ts
