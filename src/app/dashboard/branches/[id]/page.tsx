'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import { use } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { ReloadIcon } from '@radix-ui/react-icons';

interface Branch {
    id: string;
    tenantId: string;
    name: string;
    code: string;
    address: string | null;
    phone: string | null;
    email: string | null;
    isMain: boolean;
    isActive: boolean;
    notes: string | null;
    createdAt: string;
    updatedAt: string;
}

export default function BranchDetailPage() {
    const router = useRouter();
    const params = useParams();
    const searchParams = useSearchParams();
    const branchId = params?.id as string;
    const isViewMode = searchParams?.get('view') === 'true';

    const [branch, setBranch] = useState<Branch | null>(null);
    const [loading, setLoading] = useState(true);
    const [editMode, setEditMode] = useState(false);
    const [formData, setFormData] = useState<Partial<Branch>>({});
    const [saving, setSaving] = useState(false);
    const [deleting, setDeleting] = useState(false);
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

    useEffect(() => {
        fetchBranch();
    }, [branchId]);

    // If view mode is enabled, ensure edit mode is disabled
    useEffect(() => {
        if (isViewMode) {
            setEditMode(false);
        }
    }, [isViewMode]);

    const fetchBranch = async () => {
        try {
            setLoading(true);
            const response = await fetch(`/api/branches/${branchId}`);
            const data = await response.json();

            if (data.success) {
                setBranch(data.branch);
                setFormData(data.branch);
            } else {
                toast.error(data.error || 'Failed to load branch');
                router.push('/dashboard/branches');
            }
        } catch (error) {
            console.error('Error fetching branch:', error);
            toast.error('An error occurred while fetching branch');
            router.push('/dashboard/branches');
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData((prev) => ({ ...prev, [name]: value }));
    };

    const handleCheckboxChange = (name: string, checked: boolean) => {
        setFormData((prev) => ({ ...prev, [name]: checked }));
    };

    const handleSave = async () => {
        try {
            setSaving(true);

            // Include all fields from the form, passing through null values
            const preparedData: Record<string, any> = {
                // Include all fields as they are, allowing null values
                name: formData.name,
                code: formData.code,
                email: formData.email,
                notes: formData.notes,
                address: formData.address,
                phone: formData.phone,
                isMain: formData.isMain,
                isActive: formData.isActive
            };

            console.log('Submitting data:', preparedData);

            const response = await fetch(`/api/branches/${branchId}`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(preparedData),
            });

            const data = await response.json();

            if (data.success) {
                toast.success('Branch updated successfully');
                setBranch(data.branch);
                setEditMode(false);
            } else {
                console.error('Update error:', data);
                toast.error(data.error || 'Failed to update branch');
            }
        } catch (error) {
            console.error('Error updating branch:', error);
            toast.error('An error occurred while updating branch');
        } finally {
            setSaving(false);
        }
    };

    const handleDelete = async () => {
        try {
            console.log(`Attempting to delete branch with ID: ${branchId}`);
            setDeleting(true);

            const response = await fetch(`/api/branches/${branchId}`, {
                method: 'DELETE',
            });

            console.log('Delete response status:', response.status);
            const data = await response.json();
            console.log('Delete response data:', data);

            if (data.success) {
                toast.success('Branch deleted successfully');
                router.push('/dashboard/branches');
            } else {
                toast.error(data.error || 'Failed to delete branch');
            }
        } catch (error) {
            console.error('Error deleting branch:', error);
            toast.error('An error occurred while deleting branch');
        } finally {
            setDeleting(false);
        }
    };

    const handleCancel = () => {
        setFormData(branch || {});
        setEditMode(false);
    };

    if (loading) {
        return (
            <div className="container mx-auto py-6 space-y-6">
                <div className="flex justify-between items-center">
                    <Skeleton className="h-8 w-64" />
                    <Skeleton className="h-10 w-24" />
                </div>
                <Card>
                    <CardHeader>
                        <Skeleton className="h-6 w-32 mb-2" />
                        <Skeleton className="h-4 w-64" />
                    </CardHeader>
                    <CardContent className="space-y-4">
                        {Array(6).fill(0).map((_, i) => (
                            <div key={i} className="space-y-2">
                                <Skeleton className="h-4 w-24" />
                                <Skeleton className="h-10 w-full" />
                            </div>
                        ))}
                    </CardContent>
                </Card>
            </div>
        );
    }

    if (!branch) {
        return (
            <div className="container mx-auto py-6">
                <div className="text-center py-10 border rounded-lg">
                    <h3 className="text-lg font-medium">Branch not found</h3>
                    <p className="text-gray-500 mt-2">
                        The branch you are looking for does not exist or you don't have access to it.
                    </p>
                    <Button className="mt-4 cursor-pointer" onClick={() => router.push('/dashboard/branches')}>
                        Back to Branches
                    </Button>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto py-6 space-y-6">
            <div className="flex justify-between items-center">
                <h1 className="text-2xl font-bold">{isViewMode ? 'View Branch' : 'Branch Details'}</h1>
                <div className="flex gap-2">
                    <Button variant="outline" className='cursor-pointer' onClick={() => router.push('/dashboard/branches')}>
                        Back to Branches
                    </Button>
                    {isViewMode ? (
                        <Button onClick={() => router.push(`/dashboard/branches/${branchId}`)} className='cursor-pointer'>
                            Edit Branch
                        </Button>
                    ) : (
                        !editMode ? (
                            <Button onClick={() => setEditMode(true)} className='cursor-pointer'>Edit Branch</Button>
                        ) : (
                            <Button variant="outline" onClick={handleCancel} className='cursor-pointer'>Cancel</Button>
                        )
                    )}
                </div>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>{branch.name}</CardTitle>
                    <CardDescription>
                        Branch Code: {branch.code} | Created: {new Date(branch.createdAt).toLocaleDateString()}
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    {editMode ? (
                        // Edit Mode Form
                        <>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Branch Name</Label>
                                    <Input
                                        id="name"
                                        name="name"
                                        value={formData.name || ''}
                                        onChange={handleInputChange}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="code">Branch Code</Label>
                                    <Input
                                        id="code"
                                        name="code"
                                        value={formData.code || ''}
                                        onChange={handleInputChange}
                                    />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="phone">Phone</Label>
                                    <Input
                                        id="phone"
                                        name="phone"
                                        value={formData.phone || ''}
                                        onChange={handleInputChange}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="email">Email</Label>
                                    <Input
                                        id="email"
                                        name="email"
                                        type="email"
                                        value={formData.email || ''}
                                        onChange={handleInputChange}
                                    />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="address">Address</Label>
                                <Input
                                    id="address"
                                    name="address"
                                    value={formData.address || ''}
                                    onChange={handleInputChange}
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="notes">Notes</Label>
                                <Textarea
                                    id="notes"
                                    name="notes"
                                    rows={3}
                                    value={formData.notes || ''}
                                    onChange={handleInputChange}
                                />
                            </div>

                            <div className="flex flex-col space-y-4">
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="isMain"
                                        checked={formData.isMain || false}
                                        onCheckedChange={(checked) => handleCheckboxChange('isMain', checked as boolean)}
                                    />
                                    <Label htmlFor="isMain">Set as Main Branch</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="isActive"
                                        checked={formData.isActive || false}
                                        onCheckedChange={(checked) => handleCheckboxChange('isActive', checked as boolean)}
                                    />
                                    <Label htmlFor="isActive">Active</Label>
                                </div>
                            </div>
                        </>
                    ) : (
                        // View Mode
                        <>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h3 className="text-sm font-medium text-gray-500">Branch Name</h3>
                                    <p className="mt-1">{branch.name}</p>
                                </div>
                                <div>
                                    <h3 className="text-sm font-medium text-gray-500">Branch Code</h3>
                                    <p className="mt-1">{branch.code}</p>
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h3 className="text-sm font-medium text-gray-500">Phone</h3>
                                    <p className="mt-1">{branch.phone || '-'}</p>
                                </div>
                                <div>
                                    <h3 className="text-sm font-medium text-gray-500">Email</h3>
                                    <p className="mt-1">{branch.email || '-'}</p>
                                </div>
                            </div>

                            <div>
                                <h3 className="text-sm font-medium text-gray-500">Address</h3>
                                <p className="mt-1">{branch.address || '-'}</p>
                            </div>

                            <div>
                                <h3 className="text-sm font-medium text-gray-500">Notes</h3>
                                <p className="mt-1">{branch.notes || '-'}</p>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h3 className="text-sm font-medium text-gray-500">Status</h3>
                                    <p className="mt-1">
                                        <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${branch.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                            {branch.isActive ? 'Active' : 'Inactive'}
                                        </span>
                                    </p>
                                </div>
                                <div>
                                    <h3 className="text-sm font-medium text-gray-500">Main Branch</h3>
                                    <p className="mt-1">
                                        {branch.isMain ? (
                                            <span className="px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                Main Branch
                                            </span>
                                        ) : 'No'}
                                    </p>
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h3 className="text-sm font-medium text-gray-500">Created At</h3>
                                    <p className="mt-1">{new Date(branch.createdAt).toLocaleString()}</p>
                                </div>
                                <div>
                                    <h3 className="text-sm font-medium text-gray-500">Last Updated</h3>
                                    <p className="mt-1">{new Date(branch.updatedAt).toLocaleString()}</p>
                                </div>
                            </div>
                        </>
                    )}
                </CardContent>
                <CardFooter className="flex justify-between">
                    {isViewMode ? (
                        <div className="flex gap-2">
                            <Button onClick={() => router.push(`/dashboard/branches/${branchId}`)} className='cursor-pointer'>
                                Edit Branch
                            </Button>
                            <Button variant="outline" onClick={() => router.push('/dashboard/branches')} className='cursor-pointer'>
                                Back to List
                            </Button>
                        </div>
                    ) : editMode ? (
                        <div className="flex gap-2">
                            <Button onClick={handleSave} disabled={saving} className='cursor-pointer'>
                                {saving ? 'Saving...' : 'Save Changes'}
                            </Button>
                            <Button className='cursor-pointer' variant="outline" onClick={handleCancel}>
                                Cancel
                            </Button>
                        </div>
                    ) : (
                        <div className="flex gap-2">
                            <Button onClick={() => setEditMode(true)} className='cursor-pointer'>
                                Edit Branch
                            </Button>
                            {branch.isMain ? (
                                <div className="flex flex-col gap-2">
                                    <Button
                                        variant="destructive"
                                        disabled={true}
                                        title="Main branch cannot be deleted"
                                        className="cursor-not-allowed opacity-50"
                                    >
                                        Delete Branch
                                    </Button>
                                    <p className="text-xs text-red-500">Main branch cannot be deleted. Set another branch as main first.</p>
                                </div>
                            ) : (
                                <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                                    <AlertDialogTrigger asChild>
                                        <Button
                                            variant="destructive"
                                            disabled={deleting}
                                            className="cursor-pointer"
                                        >
                                            Delete Branch
                                        </Button>
                                    </AlertDialogTrigger>
                                    <AlertDialogContent>
                                        <AlertDialogHeader>
                                            <AlertDialogTitle>Delete Branch</AlertDialogTitle>
                                            <AlertDialogDescription>
                                                Are you sure you want to delete this branch? This action cannot be undone.
                                            </AlertDialogDescription>
                                        </AlertDialogHeader>
                                        <AlertDialogFooter>
                                            <AlertDialogCancel className="cursor-pointer">Cancel</AlertDialogCancel>
                                            <AlertDialogAction
                                                onClick={handleDelete}
                                                className="bg-red-600 hover:bg-red-700 cursor-pointer"
                                            >
                                                {deleting ? (
                                                    <ReloadIcon className="h-4 w-4 animate-spin" />
                                                ) : (
                                                    'Delete'
                                                )}
                                            </AlertDialogAction>
                                        </AlertDialogFooter>
                                    </AlertDialogContent>
                                </AlertDialog>
                            )}
                        </div>
                    )}
                </CardFooter>
            </Card>
        </div>
    );
}
