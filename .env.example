AUTH_SECRET="DpZL51i6JbEJ47gXDl6kO8OS7BJDi+999Fwt+l32n1w=" # Added by `npx auth`. Read more: https://cli.authjs.dev
NEXTAUTH_SECRET="DpZL51i6JbEJ47gXDl6kO8OS7BJDi+999Fwt+l32n1w="

# Set timezone to Asia/Dhaka
TZ="Asia/Dhaka"


# DATABASE_URL="postgresql://postgres.vjmakconjfriwuhvnaon:[YOUR-PASSWORD]@aws-0-ap-south-1.pooler.supabase.com:6543/postgres"

#----------------------------------For local----------------------------------------------------#

NEXT_PUBLIC_APP_URL_LOCAL=http://localhost:3000
TURSO_DATABASE_URL=libsql://hisab-manager-test-nazmul11.aws-ap-south-1.turso.io
TURSO_AUTH_TOKEN=**************************************************************************************************************************************************************************************************************************************************************************************************************

#----------------------------------For Live----------------------------------------------------#

# NEXT_PUBLIC_APP_URL=https://hisabmanager.com
# TURSO_DATABASE_URL=libsql://hisab-manager-live-nazmul11.aws-ap-south-1.turso.io
# TURSO_AUTH_TOKEN=**************************************************************************************************************************************************************************************************************************************************************************************************************

#----------------------------------End----------------------------------------------------#