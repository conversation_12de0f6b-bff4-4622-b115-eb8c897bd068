import { NextResponse } from 'next/server';
import { db } from '../../../../lib/db';
import { users } from '../../../../db/schema';
import { auth } from '../../../../../auth';
import bcrypt from 'bcryptjs';
import { eq } from 'drizzle-orm';

export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Check authentication and authorization
    const session = await auth();
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const userId = (await params).id;

    // Check if user exists
    const existingUser = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.id, userId)
    });

    if (!existingUser) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Parse request body
    const { username, email, fullName, password, role, isActive } = await request.json();

    // Validate required fields
    if (!username || !email || !fullName) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if email is already used by another user
    if (email !== existingUser.email) {
      const userWithEmail = await db.query.users.findFirst({
        where: (users, { eq }) => eq(users.email, email)
      });

      if (userWithEmail) {
        return NextResponse.json(
          { success: false, error: 'Email is already in use by another user' },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {
      username,
      email,
      fullName,
      role: role || existingUser.role,
      isActive: isActive !== undefined ? isActive : existingUser.isActive,
      updatedAt: new Date()
    };

    // Only update password if provided
    if (password) {
      updateData.password = await bcrypt.hash(password, 10);
    }

    // Update user
    await db.update(users)
      .set(updateData)
      .where(eq(users.id, userId));

    // Fetch updated user
    const updatedUser = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.id, userId)
    });

    if (!updatedUser) {
      return NextResponse.json(
        { success: false, error: 'Failed to retrieve updated user' },
        { status: 500 }
      );
    }

    // Return success without password
    const { password: _, ...userWithoutPassword } = updatedUser;
    
    return NextResponse.json({
      success: true,
      user: userWithoutPassword
    });
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update user' },
      { status: 500 }
    );
  }
}