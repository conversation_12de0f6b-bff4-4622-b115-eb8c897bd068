'use server';

import { db } from "@/lib/db";
import { tenants, users } from "@/db/schema";
import { eq } from "drizzle-orm";
import { revalidatePath } from "next/cache";
import { auth } from "../../../../auth";

interface BrandSettingsFormData {
  tenantId: string;
  companyName: string;
  phone: string;
  address: string;
}

export type BrandSettingsState = {
  success: boolean;
  message: string;
};

export async function updateBrandSettings(
  prevState: BrandSettingsState,
  formData: FormData
): Promise<BrandSettingsState> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      console.error("No user session found");
      return {
        success: false,
        message: "You must be logged in to update brand settings."
      };
    }

    console.log("Updating brand settings for user:", session.user.id);

    // Extract form data
    const data: BrandSettingsFormData = {
      tenantId: formData.get('tenantId') as string,
      companyName: formData.get('companyName') as string,
      phone: formData.get('phone') as string,
      address: formData.get('address') as string,
    };

    // Validate required fields
    if (!data.companyName) {
      return {
        success: false,
        message: "Company name is required."
      };
    }

    // Update tenant data (company name)
    await db.update(tenants)
      .set({
        companyName: data.companyName,
        updatedAt: new Date(),
      })
      .where(eq(tenants.userId, session.user.id));

    // Update user data (phone, address)
    await db.update(users)
      .set({
        phone: data.phone || null,
        address: data.address || null,
        updatedAt: new Date(),
      })
      .where(eq(users.id, session.user.id));

    // Revalidate the brand settings page and any pages that use this data
    revalidatePath('/dashboard/brand-settings');
    revalidatePath('/dashboard/sales');
    revalidatePath('/dashboard/sales/[id]/print', 'page');
    revalidatePath('/print-invoice/[id]', 'page');

    return {
      success: true,
      message: "Brand settings updated successfully."
    };
  } catch (error) {
    console.error("Error updating brand settings:", error);
    // Log more detailed error information
    if (error instanceof Error) {
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
    }
    return {
      success: false,
      message: "Failed to update brand settings. Please try again."
    };
  }
}
