'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ALL_MENUS } from '@/lib/menu-list';

type MenuPermission = {
  menuId: string;
  name: string;
  displayName: string;
  path: string;
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
  order?: number;
};

type Branch = {
  id: string;
  name: string;
  code: string;
  isMain: boolean;
  isActive: boolean;
};

type FormData = {
  username: string;
  email: string;
  fullName: string;
  password: string;
  confirmPassword: string;
  isActive: boolean;
  branchId: string;
  menuPermissions: MenuPermission[];
};

export default function TenantSaleUserForm() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [availableMenus, setAvailableMenus] = useState<any[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);

  const [formData, setFormData] = useState<FormData>({
    username: '',
    email: '',
    fullName: '',
    password: '',
    confirmPassword: '',
    isActive: true,
    branchId: '',
    menuPermissions: []
  });

  // Initialize menus from file
  const initializeMenus = () => {
    try {
      setIsLoading(true);
      fetchMenus();
      toast.success('Menu data initialized from file');
    } catch (error) {
      console.error('Error initializing menus:', error);
      toast.error('Failed to initialize menus');
    } finally {
      setIsLoading(false);
    }
  };

  // Use file-based menu definitions
  const fetchMenus = async () => {
    try {
      // Initialize menu permissions with all menus from file (except dashboard) and all permissions enabled
      const initialMenuPermissions = ALL_MENUS
        .filter(menu => menu.id !== 'dashboard') // Filter out dashboard menu
        .map(menu => ({
          menuId: menu.id,
          name: menu.name,
          displayName: menu.displayName,
          path: menu.path || '',
          canView: true,
          canCreate: true,
          canEdit: true,
          canDelete: true,
          order: menu.order || 999 // Use a high number if order is not provided
        }));

      // Sort by order
      const sortedMenuPermissions = initialMenuPermissions.sort((a, b) =>
        (a.order || 999) - (b.order || 999)
      );

      setAvailableMenus(ALL_MENUS.filter(menu => menu.id !== 'dashboard'));
      setFormData(prev => ({
        ...prev,
        menuPermissions: sortedMenuPermissions
      }));
    } catch (error) {
      console.error('Error initializing menus:', error);
      toast.error('Failed to initialize menus');
    }
  };

  // Fetch branches for the tenant
  const fetchBranches = async () => {
    try {
      const response = await fetch('/api/branches');
      const data = await response.json();

      if (data.success) {
        setBranches(data.branches);

        // Filter active branches
        const activeBranches = data.branches.filter((branch: Branch) => branch.isActive);

        // If there's only one active branch or there's an active main branch, select it by default
        if (activeBranches.length === 1) {
          setFormData(prev => ({
            ...prev,
            branchId: activeBranches[0].id
          }));
        } else if (activeBranches.length > 1) {
          const mainBranch = activeBranches.find((branch: Branch) => branch.isMain);
          if (mainBranch) {
            setFormData(prev => ({
              ...prev,
              branchId: mainBranch.id
            }));
          }
        }
      }
    } catch (error) {
      console.error('Error fetching branches:', error);
      toast.error('Failed to load branches');
    }
  };

  // Fetch menus and branches on component mount
  useEffect(() => {
    fetchMenus();
    fetchBranches();
  }, []);

  // Get branch display name from ID
  const getBranchDisplayName = (branchId: string) => {
    const branch = branches.find(b => b.id === branchId);
    if (!branch) return "";
    return `${branch.name} ${branch.isMain ? "(Main)" : ""} - ${branch.code}`;
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle menu permission changes
  const handlePermissionChange = (menuId: string, permissionType: 'canView' | 'canCreate' | 'canEdit' | 'canDelete', checked: boolean) => {
    setFormData(prev => {
      // First update the current permission
      const updatedPermissions = prev.menuPermissions.map(permission =>
        permission.menuId === menuId
          ? {
            ...permission,
            [permissionType]: checked,
            // If view permission is turned off, turn off all other permissions
            ...(permissionType === 'canView' && !checked
              ? { canCreate: false, canEdit: false, canDelete: false }
              : {})
          }
          : permission
      );

      // Special case: If products permission is being enabled, also enable categories view permission
      if (checked && permissionType === 'canView') {
        const changedPermission = prev.menuPermissions.find(p => p.menuId === menuId);

        // If this is the products menu and view is being enabled
        if (changedPermission?.path === '/dashboard/products') {
          // Find the categories menu and enable its view permission
          const finalPermissions = updatedPermissions.map(permission =>
            permission.path === '/dashboard/products/categories'
              ? { ...permission, canView: true }
              : permission
          );

          // Show a notification to the user
          toast.info('Categories view permission has been automatically enabled because Products permission is enabled');

          return {
            ...prev,
            menuPermissions: finalPermissions
          };
        }
      }

      return {
        ...prev,
        menuPermissions: updatedPermissions
      };
    });
  };

  // Submit form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Validate form
    if (formData.password !== formData.confirmPassword) {
      toast.error('Passwords do not match');
      setIsLoading(false);
      return;
    }

    try {
      // Filter out menus without view permission and sort by order
      const activeMenuPermissions = formData.menuPermissions
        .filter(permission => permission.canView)
        .sort((a, b) => (a.order || 999) - (b.order || 999));

      if (activeMenuPermissions.length === 0) {
        toast.error('Please grant view permission to at least one menu. At least one permission must be given.');
        setIsLoading(false);
        return;
      }

      // Check if products permission is enabled but categories is not
      const hasProductsPermission = activeMenuPermissions.some(
        permission => permission.path === '/dashboard/products'
      );

      const hasCategoriesPermission = activeMenuPermissions.some(
        permission => permission.path === '/dashboard/products/categories'
      );

      if (hasProductsPermission && !hasCategoriesPermission) {
        // Auto-enable categories permission
        const updatedPermissions = formData.menuPermissions.map(permission =>
          permission.path === '/dashboard/products/categories'
            ? { ...permission, canView: true }
            : permission
        );

        setFormData(prev => ({
          ...prev,
          menuPermissions: updatedPermissions
        }));

        // Update activeMenuPermissions to include the categories permission
        const categoriesPermission = formData.menuPermissions.find(
          permission => permission.path === '/dashboard/products/categories'
        );

        if (categoriesPermission) {
          // Create a new copy of the permission with view enabled
          const updatedCategoryPermission = {
            ...categoriesPermission,
            canView: true
          };

          // Add to active permissions
          activeMenuPermissions.push(updatedCategoryPermission);

          // Sort active permissions again after adding the new one
          activeMenuPermissions.sort((a, b) => (a.order || 999) - (b.order || 999));
        }

        toast.info('Categories view permission has been automatically enabled because Products permission is enabled');
      }

      // Validate branch selection
      if (!formData.branchId) {
        toast.error('Please select a branch for this user');
        setIsLoading(false);
        return;
      }

      const response = await fetch('/api/tenant/users/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: formData.username,
          email: formData.email,
          fullName: formData.fullName,
          password: formData.password,
          isActive: formData.isActive,
          branchId: formData.branchId,
          menuPermissions: activeMenuPermissions
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Tenant sale user created successfully');
        router.push('/dashboard/tenant/users');
      } else {
        toast.error(data.error || 'Failed to create user');
      }
    } catch (error) {
      console.error('Error creating tenant sale user:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Add Tenant Sale User</h2>
        {formData.menuPermissions.length === 0 && (
          <Button
            type="button"
            onClick={initializeMenus}
            disabled={isLoading}
            className="bg-blue-500 hover:bg-blue-600"
          >
            {isLoading ? 'Loading...' : 'Initialize Menu Data'}
          </Button>
        )}
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label className='mb-2' htmlFor="fullName">Full Name</Label>
            <Input
              id="fullName"
              name="fullName"
              value={formData.fullName}
              onChange={handleInputChange}
              required
            />
          </div>

          <div>
            <Label className='mb-2' htmlFor="username">Username</Label>
            <Input
              id="username"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              required
            />
          </div>

          <div>
            <Label className='mb-2' htmlFor="email">Email</Label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              required
            />
          </div>

          <div>
            <Label className='mb-2' htmlFor="password">Password</Label>
            <Input
              id="password"
              name="password"
              type="password"
              value={formData.password}
              onChange={handleInputChange}
              required
            />
          </div>

          <div>
            <Label className='mb-2' htmlFor="confirmPassword">Confirm Password</Label>
            <Input
              id="confirmPassword"
              name="confirmPassword"
              type="password"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              required
            />
          </div>

          <div>
            <Label className='mb-2' htmlFor="branchId">Branch</Label>
            <div className="relative">
              <select
                id="branchId"
                className="w-full h-10 px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                value={formData.branchId}
                onChange={(e) => setFormData(prev => ({ ...prev, branchId: e.target.value }))}
              >
                <option value="">Select branch</option>
                {branches
                  .filter(branch => branch.isActive)
                  .map((branch) => (
                    <option key={branch.id} value={branch.id}>
                      {branch.name} {branch.isMain ? "(Main)" : ""} - {branch.code}
                    </option>
                  ))}
              </select>
              {/* <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div> */}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="isActive"
              name="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) =>
                setFormData(prev => ({ ...prev, isActive: checked === true }))
              }
            />
            <Label htmlFor="isActive">Active</Label>
          </div>
        </div>

        <div>
          <h3 className="text-lg font-medium mb-4">Menu Permissions</h3>

          {formData.menuPermissions.length === 0 ? (
            <div className="border rounded-md p-8 text-center">
              <p className="text-gray-500 mb-4">No menu data available. Please initialize menu data using the button above.</p>
            </div>
          ) : (
            <div className="border rounded-md p-4">
              <div className="flex justify-between items-center mb-4">
                <div className="text-sm text-gray-500">All permissions are enabled by default for tenant_sale users.</div>
                <div className="flex items-center space-x-4">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Select all permissions for all menus
                      setFormData(prev => ({
                        ...prev,
                        menuPermissions: prev.menuPermissions.map(permission => ({
                          ...permission,
                          canView: true,
                          canCreate: true,
                          canEdit: true,
                          canDelete: true
                        }))
                      }));
                      toast.success('All permissions enabled');
                    }}
                  >
                    Select All
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Select only view permissions for all menus
                      setFormData(prev => ({
                        ...prev,
                        menuPermissions: prev.menuPermissions.map(permission => ({
                          ...permission,
                          canView: true,
                          canCreate: false,
                          canEdit: false,
                          canDelete: false
                        }))
                      }));
                      toast.success('View-only permissions enabled');
                    }}
                  >
                    View Only
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Get all permissions
                      const permissions = formData.menuPermissions;

                      // Sort permissions by order
                      const sortedPermissions = [...permissions].sort((a, b) => (a.order || 999) - (b.order || 999));

                      // Find the first menu
                      const firstMenu = sortedPermissions.length > 0 ? sortedPermissions[0] : null;

                      if (!firstMenu) {
                        toast.error('No menus available to deselect');
                        return;
                      }

                      // Keep only the first menu with view permission
                      const updatedPermissions = permissions.map(permission => {
                        // First menu in the sorted list gets view permission, others get none
                        const isFirstMenu = permission.menuId === firstMenu.menuId;
                        return {
                          ...permission,
                          canView: isFirstMenu,
                          canCreate: false,
                          canEdit: false,
                          canDelete: false
                        };
                      });

                      setFormData(prev => ({
                        ...prev,
                        menuPermissions: updatedPermissions
                      }));
                      toast.success('All permissions deselected except one required view permission');
                    }}
                  >
                    Deselect All
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-5 gap-4 font-medium mb-2 pb-2 border-b">
                <div>Menu</div>
                <div className="text-center">View</div>
                <div className="text-center">Create</div>
                <div className="text-center">Edit</div>
                <div className="text-center">Delete</div>
              </div>

              {/* Sort menu permissions by order before rendering */}
              {[...formData.menuPermissions]
                .sort((a, b) => (a.order || 999) - (b.order || 999))
                .map((permission) => (
                  <div key={permission.menuId} className="grid grid-cols-5 gap-4 py-2 border-b last:border-0">
                    <div>{permission.displayName}</div>

                    <div className="flex justify-center">
                      <Checkbox
                        checked={permission.canView}
                        onCheckedChange={(checked) =>
                          handlePermissionChange(permission.menuId, 'canView', checked === true)
                        }
                      />
                    </div>

                    <div className="flex justify-center">
                      <Checkbox
                        checked={permission.canCreate}
                        onCheckedChange={(checked) =>
                          handlePermissionChange(permission.menuId, 'canCreate', checked === true)
                        }
                        disabled={!permission.canView}
                      />
                    </div>

                    <div className="flex justify-center">
                      <Checkbox
                        checked={permission.canEdit}
                        onCheckedChange={(checked) =>
                          handlePermissionChange(permission.menuId, 'canEdit', checked === true)
                        }
                        disabled={!permission.canView}
                      />
                    </div>

                    <div className="flex justify-center">
                      <Checkbox
                        checked={permission.canDelete}
                        onCheckedChange={(checked) =>
                          handlePermissionChange(permission.menuId, 'canDelete', checked === true)
                        }
                        disabled={!permission.canView}
                      />
                    </div>
                  </div>
                ))}
            </div>
          )}
        </div>

        <div className="flex justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            className="mr-2 cursor-pointer"
            disabled={isLoading}
          >
            Cancel
          </Button>

          <Button
            type="submit"
            disabled={isLoading || formData.menuPermissions.length === 0}
            title={formData.menuPermissions.length === 0 ? 'Initialize menu data first' : ''}
            className='cursor-pointer'
          >
            {isLoading ? 'Creating...' : 'Create User'}
          </Button>
        </div>
      </form>
    </div>
  );
}
