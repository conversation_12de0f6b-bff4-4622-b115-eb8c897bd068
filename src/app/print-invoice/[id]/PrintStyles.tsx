"use client";

export default function PrintStyles() {
  // Define the CSS as a string constant to ensure consistency
  const printCss = `
      @media print {
        @page {
          size: A4;
          margin: 5mm;
        }

        body {
          padding: 0;
          margin: 0;
          background: white;
          font-size: 12pt;
        }

        /* Hide any potential navigation elements */
        header, nav, footer, aside, .sidebar, .topbar {
          display: none !important;
        }

        /* Hide print button and navigation controls */
        .print\\:hidden {
          display: none !important;
        }

        /* Ensure colors print properly */
        * {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
          color-adjust: exact;
        }

        /* Table styles for printing */
        table {
          border-collapse: collapse;
          width: 100%;
        }

        table, th, td {
          border: 1px solid #6b7280; /* gray-500 equivalent in hex */
        }

        th, td {
          padding: 2mm;
        }

        /* Ensure the invoice fits on one page */
        .print-container {
          page-break-inside: avoid;
        }

        /* Ensure the Memo Paid stamp is visible when printing */
        [class*="MemoPaidStamp"] {
          position: absolute;
          z-index: 50;
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
          color-adjust: exact;
        }

        /* Ensure all elements of the stamp print correctly */
        [class*="MemoPaidStamp"] * {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
          color-adjust: exact;
        }
      }
    `;

  return <style type="text/css" dangerouslySetInnerHTML={{ __html: printCss }} />;
}