import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { customers, userMenuPermissions } from "@/db/schema";
import { auth } from "~/auth";
import { v4 as uuidv4 } from "uuid";
import { eq, like, and, or } from "drizzle-orm";
import { withMenuPermission, checkMenuPermission } from "@/middleware/menuPermissions";

async function postHandler(request: NextRequest, context: any, permissionCheck: any) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Check if user has create permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      const menuPermission = permissionCheck.permissions.find(
        (p: any) => p.menu.path === '/dashboard/customers'
      );

      if (!menuPermission?.canCreate) {
        return NextResponse.json(
          { success: false, error: "You do not have permission to create customers" },
          { status: 403 }
        );
      }
    }

    const data = await request.json();

    // Validate required fields
    if (!data.tenantId || !data.name || !data.type) {
      return NextResponse.json(
        { message: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if customer code is provided and if it's unique
    if (data.code && data.code.trim() !== '') {
      // Check if a customer with the same code already exists for this tenant
      const existingCustomer = await db.query.customers.findFirst({
        where: (customers, { eq, and }) =>
          and(
            eq(customers.tenantId, data.tenantId),
            eq(customers.code, data.code)
          ),
      });

      if (existingCustomer) {
        return NextResponse.json(
          {
            message: "Customer code must be unique",
            code: "DUPLICATE_CODE"
          },
          { status: 400 }
        );
      }
    }

    // Create the customer
    const customerId = uuidv4();

    // Calculate the current balance based on initial due and advanced payment
    // If advanced payment is provided, it will be subtracted from the initial due
    // A negative balance means the customer has credit (advanced payment)
    const initialDue = data.initialDue || 0;
    const advancedPayment = data.advancedPayment || 0;
    const currentBalance = initialDue - advancedPayment;

    await db.insert(customers).values({
      id: customerId,
      tenantId: data.tenantId,
      name: data.name,
      code: data.code || null,
      phone: data.phone || null,
      email: data.email || null,
      address: data.address || null,
      type: data.type,
      creditLimit: data.creditLimit || 0,
      extraCommission: data.extraCommission || 0,
      currentBalance: currentBalance, // Can be negative if advanced payment > initial due
      notes: data.notes || null,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return NextResponse.json(
      { id: customerId, message: "Customer created successfully" },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating customer:", error);
    return NextResponse.json(
      { message: "Failed to create customer", error: (error as Error).message },
      { status: 500 }
    );
  }
}

async function getHandler(request: NextRequest, context: any, permissionCheck: any) {
  try {
    // Get authenticated session
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has view permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      const menuPermission = permissionCheck.permissions.find(
        (p: any) => p.menu.path === '/dashboard/customers'
      );

      if (!menuPermission?.canView) {
        return NextResponse.json(
          { success: false, error: "You do not have permission to view customers" },
          { status: 403 }
        );
      }
    }

    // Extract tenant ID from session
    const tenantId = session.user.tenantId || session.user.id;

    // Parse search query from URL
    const { searchParams } = new URL(request.url);
    const searchQuery = searchParams.get("search");

    // Build base query for the tenant
    const query = db
      .select()
      .from(customers)
      .where(eq(customers.tenantId, tenantId));

    // Add search filter if search query is provided
    if (searchQuery && searchQuery.trim() !== "") {
      const searchTerm = `%${searchQuery.trim()}%`;

      const searchResults = await db
        .select()
        .from(customers)
        .where(
          and(
            eq(customers.tenantId, tenantId),
            or(
              like(customers.name, searchTerm),
              like(customers.phone || "", searchTerm),
              like(customers.code || "", searchTerm)
            )
          )
        )
        .orderBy(customers.name);

      return NextResponse.json(searchResults);
    }

    // Execute query with ordering if no search
    const allCustomers = await query.orderBy(customers.name);

    // Return successful response
    return NextResponse.json(allCustomers);
  } catch (error) {
    // Handle errors properly
    console.error("Error fetching customers:", error);

    return NextResponse.json(
      { error: "Failed to fetch customers" },
      { status: 500 }
    );
  }
}

// Export the route handlers with permission middleware
export const GET = withMenuPermission(getHandler, '/dashboard/customers');
export const POST = withMenuPermission(postHandler, '/dashboard/customers');