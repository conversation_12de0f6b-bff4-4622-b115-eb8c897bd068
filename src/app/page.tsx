import { auth } from "~/auth";
import AuthForms from './components/AuthForms';
import { redirect } from "next/navigation";
import Image from "next/image";

export default async function Home() {
  const session = await auth();
  const user = session?.user;

  if (user) {
    return redirect('/dashboard');
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-8">
      <div className="w-full max-w-4xl mx-auto">
        <div className="flex justify-center mb-8">
          <Image
            src="/logo.webp"
            alt="Hisab Manager Logo"
            width={100}
            height={40}
            priority
          />
        </div>
        <AuthForms />
      </div>
    </div>
  );
}
