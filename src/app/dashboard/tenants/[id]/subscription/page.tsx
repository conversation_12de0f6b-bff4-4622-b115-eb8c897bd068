'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useSession } from 'next-auth/react';

interface SubscriptionData {
    plan: 'free-trial' | 'paid';
    startDate: string;
    endDate: string;
    isActive: boolean;
    daysRemaining: number;
    paymentStatus?: 'pending' | 'paid' | 'overdue';
    lastPaymentDate?: string;
    paymentDueDate?: string;
}

export default function TenantSubscriptionPage() {
    const router = useRouter();
    const params = useParams();
    const tenantId = params?.id as string;
    const { data: session } = useSession();

    const [subscription, setSubscription] = useState<SubscriptionData | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string>('');
    const [successMessage, setSuccessMessage] = useState<string>('');

    // Form states
    const [selectedPlan, setSelectedPlan] = useState<'free-trial' | 'paid'>('paid');
    const [newEndDate, setNewEndDate] = useState<string>('');
    const [paymentStatus, setPaymentStatus] = useState<'pending' | 'paid' | 'overdue'>('pending');

    // Action states
    const [updating, setUpdating] = useState<boolean>(false);

    useEffect(() => {
        fetchSubscriptionData();
    }, [tenantId]);

    // Set default end date to 30 days from today
    useEffect(() => {
        if (!newEndDate) {
            const date = new Date();
            date.setDate(date.getDate() + 30);
            setNewEndDate(formatDateForInput(date));
        }
    }, []);

    // Set new end date when subscription data loads
    useEffect(() => {
        if (subscription?.endDate) {
            const currentEndDate = new Date(subscription.endDate);
            const nextMonth = new Date(currentEndDate);
            nextMonth.setMonth(nextMonth.getMonth() + 1);
            setNewEndDate(formatDateForInput(nextMonth));
        }
    }, [subscription]);

    const fetchSubscriptionData = async () => {
        try {
            setLoading(true);
            setError('');

            const response = await fetch(`/api/tenants/${tenantId}/subscription`);

            if (!response.ok) {
                throw new Error('Failed to fetch subscription data');
            }

            const data = await response.json();

            if (data.success) {
                setSubscription(data.subscription);
                setSelectedPlan(data.subscription.plan);
                setPaymentStatus(data.subscription.paymentStatus || 'pending');
            } else {
                setError(data.error || 'Failed to load subscription data');
            }
        } catch (err) {
            setError('An error occurred while fetching subscription data');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!newEndDate) {
            setError('Please select a valid end date');
            return;
        }

        try {
            setUpdating(true);
            setError('');
            setSuccessMessage('');

            // Prepare payload with all subscription updates
            const payload = {
                subscriptionType: selectedPlan,
                subscriptionEndDate: new Date(newEndDate).toISOString(),
                paymentStatus
            };

            const response = await fetch(`/api/tenants/${tenantId}/subscription`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload),
            });

            const data = await response.json();

            if (data.success) {
                setSuccessMessage('Subscription updated successfully');
                await fetchSubscriptionData();
            } else {
                setError(data.error || 'Failed to update subscription');
            }
        } catch (err) {
            setError('An error occurred while updating the subscription');
            console.error(err);
        } finally {
            setUpdating(false);
        }
    };

    const formatDate = (dateString: string) => {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
    };

    const formatDateForInput = (date: Date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    const calculateDaysFromNow = (dateString: string) => {
        const targetDate = new Date(dateString);
        const now = new Date();
        const diffTime = targetDate.getTime() - now.getTime();
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="flex items-center mb-6">
                <button
                    onClick={() => router.back()}
                    className="mr-4 text-blue-500 hover:text-blue-700 cursor-pointer"
                >
                    &larr; Back
                </button>
                <h1 className="text-2xl font-bold">Subscription Management</h1>
            </div>

            {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {error}
                </div>
            )}

            {successMessage && (
                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {successMessage}
                </div>
            )}

            {subscription && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Current Subscription Info */}
                    <div className="bg-white p-6 rounded-lg shadow-md">
                        <h2 className="text-xl font-semibold mb-4">Current Subscription</h2>

                        <div className="space-y-3">
                            <div className="flex justify-between">
                                <span className="text-gray-600">Plan:</span>
                                <span className="font-medium">
                                    {subscription.plan === 'free-trial' ? 'Free Trial' : 'Paid Plan'}
                                </span>
                            </div>

                            <div className="flex justify-between">
                                <span className="text-gray-600">Status:</span>
                                <span className={`font-medium ${subscription.isActive ? 'text-green-600' : 'text-red-600'}`}>
                                    {subscription.isActive ? 'Active' : 'Inactive'}
                                </span>
                            </div>

                            <div className="flex justify-between">
                                <span className="text-gray-600">Start Date:</span>
                                <span className="font-medium">{formatDate(subscription.startDate)}</span>
                            </div>

                            <div className="flex justify-between">
                                <span className="text-gray-600">End Date:</span>
                                <span className="font-medium">{formatDate(subscription.endDate)}</span>
                            </div>

                            <div className="flex justify-between">
                                <span className="text-gray-600">Days Remaining:</span>
                                <span className={`font-medium ${subscription.daysRemaining < 7 ? 'text-red-600' : 'text-green-600'}`}>
                                    {subscription.daysRemaining} days
                                </span>
                            </div>

                            <div className="flex justify-between">
                                <span className="text-gray-600">Payment Status:</span>
                                <span className={`font-medium ${subscription.paymentStatus === 'paid' ? 'text-green-600' :
                                    subscription.paymentStatus === 'overdue' ? 'text-red-600' :
                                        'text-yellow-600'
                                    }`}>
                                    {subscription.paymentStatus || 'N/A'}
                                </span>
                            </div>

                            {subscription.lastPaymentDate && (
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Last Payment:</span>
                                    <span className="font-medium">{formatDate(subscription.lastPaymentDate)}</span>
                                </div>
                            )}

                            {subscription.paymentDueDate && (
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Payment Due:</span>
                                    <span className="font-medium">{formatDate(subscription.paymentDueDate)}</span>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Subscription Management - Combined Form */}
                    <div className="bg-white p-6 rounded-lg shadow-md">
                        <h2 className="text-xl font-semibold mb-4">Update Subscription</h2>

                        <form onSubmit={handleSubmit}>
                            {/* Change Plan */}
                            <div className="mb-6">
                                <h3 className="font-medium text-gray-700 mb-2">Subscription Plan</h3>
                                <div className="space-y-2">
                                    <label className="flex items-center p-3 border rounded cursor-pointer hover:bg-gray-50">
                                        <input
                                            type="radio"
                                            name="plan"
                                            value="free-trial"
                                            checked={selectedPlan === 'free-trial'}
                                            onChange={() => setSelectedPlan('free-trial')}
                                            className="mr-2"
                                        />
                                        <div>
                                            <div className="font-medium">Free Trial</div>
                                            <div className="text-sm text-gray-500">7 days of basic access</div>
                                            <div className="text-sm font-bold mt-1">Free</div>
                                        </div>
                                    </label>

                                    <label className="flex items-center p-3 border rounded cursor-pointer hover:bg-gray-50">
                                        <input
                                            type="radio"
                                            name="plan"
                                            value="paid"
                                            checked={selectedPlan === 'paid'}
                                            onChange={() => setSelectedPlan('paid')}
                                            className="mr-2"
                                        />
                                        <div>
                                            <div className="font-medium">Paid Plan</div>
                                            <div className="text-sm text-gray-500">Full featured access</div>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            {/* Extend Subscription */}
                            <div className="mb-6">
                                <h3 className="font-medium text-gray-700 mb-2">Subscription End Date</h3>
                                <input
                                    type="date"
                                    value={newEndDate}
                                    onChange={(e) => setNewEndDate(e.target.value)}
                                    min={formatDateForInput(new Date())}
                                    className="w-full p-2 border rounded"
                                    required
                                />
                                {newEndDate && (
                                    <p className="mt-2 text-sm text-gray-600">
                                        Subscription will end on {formatDate(newEndDate)}
                                        ({calculateDaysFromNow(newEndDate)} days from today)
                                    </p>
                                )}
                            </div>

                            {/* Update Payment Status */}
                            <div className="mb-6">
                                <h3 className="font-medium text-gray-700 mb-2">Payment Status</h3>
                                <select
                                    value={paymentStatus}
                                    onChange={(e) => setPaymentStatus(e.target.value as 'pending' | 'paid' | 'overdue')}
                                    className="w-full p-2 border rounded"
                                >
                                    <option value="pending">Pending</option>
                                    <option value="paid">Paid</option>
                                    <option value="overdue">Overdue</option>
                                </select>
                            </div>

                            <button
                                type="submit"
                                disabled={updating}
                                className="w-full bg-blue-600 text-white py-3 rounded hover:bg-blue-700 disabled:opacity-50 font-medium cursor-pointer"
                            >
                                {updating ? 'Updating...' : 'Update Subscription'}
                            </button>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
} 