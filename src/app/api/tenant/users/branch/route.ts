import { NextRequest, NextResponse } from "next/server";
import { auth } from "~/auth";
import { db } from "@/lib/db";
import { branchUsers, branches } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

// POST /api/tenant/users/branch - Update a tenant_sale user's branch assignment
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Only tenant_sale users can update their own branch
    if (session.user.role !== 'tenant_sale') {
      return NextResponse.json(
        { success: false, error: "Only tenant_sale users can update their branch" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { branchId } = body;

    if (!branchId) {
      return NextResponse.json(
        { success: false, error: "Branch ID is required" },
        { status: 400 }
      );
    }

    // Check if branch exists and belongs to user's tenant
    const branch = await db
      .select()
      .from(branches)
      .where(
        and(
          eq(branches.id, branchId),
          eq(branches.tenantId, session.user.tenantId || "")
        )
      )
      .limit(1);

    if (branch.length === 0) {
      return NextResponse.json(
        { success: false, error: "Branch not found or access denied" },
        { status: 404 }
      );
    }

    // Check if user is already associated with this branch
    const existingBranchUser = await db
      .select()
      .from(branchUsers)
      .where(
        and(
          eq(branchUsers.branchId, branchId),
          eq(branchUsers.userId, session.user.id)
        )
      )
      .limit(1);

    if (existingBranchUser.length > 0) {
      // User is already associated with this branch, just return success
      return NextResponse.json({
        success: true,
        message: "Branch already assigned to user",
        branch: branch[0],
        branchId: branch[0].id
      });
    }

    // Delete any existing branch assignments for this user
    await db
      .delete(branchUsers)
      .where(eq(branchUsers.userId, session.user.id));

    // Create a new branch user association
    await db.insert(branchUsers).values({
      id: uuidv4(),
      branchId,
      userId: session.user.id,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return NextResponse.json({
      success: true,
      message: "Branch assigned successfully",
      branch: branch[0],
      branchId: branch[0].id
    });
  } catch (error) {
    console.error("Error assigning branch to user:", error);
    return NextResponse.json(
      { success: false, error: "Failed to assign branch to user" },
      { status: 500 }
    );
  }
}
