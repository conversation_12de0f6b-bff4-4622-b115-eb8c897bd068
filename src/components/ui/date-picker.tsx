"use client"

import * as React from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover"

export interface DatePickerProps {
    value?: Date | null
    onChange?: (date: Date | null) => void
    onSelect?: (date: Date | null) => void
    placeholder?: string
    selected?: Date | null
    id?: string
    className?: string
    disabled?: boolean
}

export function DatePicker({
    value,
    onChange,
    onSelect,
    placeholder = "Pick a date",
    selected,
    id,
    className,
    disabled = false,
}: DatePickerProps) {
    const [date, setDate] = React.useState<Date | null>(selected || value || null)

    // Handle the change from parent component
    React.useEffect(() => {
        setDate(selected || value || null)
    }, [selected, value])

    // Handle the internal change
    const handleSelect = (newDate: Date | null) => {
        setDate(newDate)

        if (onSelect) {
            onSelect(newDate)
        }

        if (onChange) {
            onChange(newDate)
        }
    }

    return (
        <Popover>
            <PopoverTrigger asChild>
                <Button
                    id={id}
                    variant={"outline"}
                    className={cn(
                        "w-full justify-start text-left font-normal",
                        !date && "text-muted-foreground",
                        className
                    )}
                    disabled={disabled}
                >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {date ? format(date, "PPP") : <span>{placeholder}</span>}
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
                <Calendar
                    mode="single"
                    selected={date || undefined}
                    onSelect={handleSelect}
                    initialFocus
                    required
                />
            </PopoverContent>
        </Popover>
    )
} 