import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { users, tenants } from '@/db/schema';
import { auth } from '~/auth';
import { eq, sql, not, inArray } from 'drizzle-orm';

// GET /api/users/available - Get users who don't have a tenant yet (admin only)
export async function GET(request: Request) {
  try {
    // Check authentication and authorization
    const session = await auth();
    
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Get all tenant user IDs
    const tenantRows = await db.select({ userId: tenants.userId }).from(tenants);
    const tenantUserIds = tenantRows.map(row => row.userId);

    // Get users who don't have a tenant yet
    let availableUsers = [];
    
    if (tenantUserIds.length > 0) {
      availableUsers = await db.select({
        id: users.id,
        username: users.username,
        email: users.email,
        fullName: users.fullName
      })
      .from(users)
      .where(
        not(inArray(users.id, tenantUserIds))
      );
    } else {
      // If no tenants exist yet, return all users
      availableUsers = await db.select({
        id: users.id,
        username: users.username,
        email: users.email,
        fullName: users.fullName
      })
      .from(users);
    }

    return NextResponse.json({
      success: true,
      users: availableUsers
    });
  } catch (error) {
    console.error('Error fetching available users:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch available users' },
      { status: 500 }
    );
  }
} 