import { NextResponse } from "next/server";
import { auth } from "../../../../../auth";
import { db } from "@/lib/db";
import { users, tenants, tenantUsers } from "@/db/schema";
import { eq, and } from "drizzle-orm";

export async function GET() {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get user data from database
    const user = await db
      .select()
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (user.length === 0) {
      return NextResponse.json(
        { success: false, error: "User not found" },
        { status: 404 }
      );
    }

    // Get tenant info if available
    let tenantInfo = null;
    if (session.user.tenantId) {
      const tenantData = await db
        .select()
        .from(tenants)
        .where(eq(tenants.id, session.user.tenantId))
        .limit(1);

      if (tenantData.length > 0) {
        tenantInfo = tenantData[0];
      }
    }

    // If user doesn't have tenantId in session but is a tenant user, find their tenant associations
    if (!session.user.tenantId) {
      const tenantAssociations = await db
        .select({
          tenant: tenants,
          role: tenantUsers.role,
        })
        .from(tenantUsers)
        .leftJoin(tenants, eq(tenants.id, tenantUsers.tenantId))
        .where(eq(tenantUsers.userId, session.user.id));

      if (tenantAssociations.length > 0) {
        // User is associated with at least one tenant
        tenantInfo = tenantAssociations.map((association) => ({
          ...association.tenant,
          userRole: association.role,
        }));
      }
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user[0].id,
        username: user[0].username,
        email: user[0].email,
        fullName: user[0].fullName,
        role: user[0].role,
        tenantId: session.user.tenantId || null,
        tenant: tenantInfo,
      },
    });
  } catch (error) {
    console.error("Error fetching user data:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch user data" },
      { status: 500 }
    );
  }
}
