import { auth } from "~/auth";
import { db } from "@/lib/db";
import { users } from "@/db/schema";
import { redirect } from "next/navigation";
import UserManagement from "@/app/components/UserManagement";

export default async function UsersPage() {
  const session = await auth();
  
  // Redirect to login if not authenticated
  if (!session?.user) {
    redirect("/");
  }
  
  // Redirect if not admin
  if (session.user.role !== 'admin') {
    redirect("/dashboard");
  }
  
  // Fetch all users from the database
  const allUsers = await db.select().from(users).orderBy(users.createdAt);
  
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">User Management</h2>
      </div>
      
      <div className="flex flex-col md:flex-row gap-6">
        <div className="w-full">
          <UserManagement initialUsers={allUsers.map(user => ({
            id: user.id,
            username: user.username,
            email: user.email,
            fullName: user.fullName,
            role: user.role,
            isActive: user.isActive ?? false, // Default to false if null
            createdAt: user.createdAt ?? new Date(), // Default to current date if null
          }))} session={session}/>
        </div>
      </div>
    </div>
  );
}