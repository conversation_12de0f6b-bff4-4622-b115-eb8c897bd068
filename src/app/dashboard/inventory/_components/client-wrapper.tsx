'use client';

import { useState, useEffect, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { usePermissionCheck } from '@/lib/check-permissions';
import { useCachedFetch } from '@/hooks/useCachedFetch';

interface Product {
    id: string;
    name: string;
    code: string | null;
    costPrice: number;
    retailPrice: number;
    sellingPrice?: number;
    unit?: string;
    isActive?: boolean;
    categoryId?: string | null;
    category?: {
        name: string;
    };
}

interface Branch {
    id: string;
    name: string;
    code: string;
    isMain?: boolean;
    isActive?: boolean;
}

interface InventoryItem {
    id: string;
    tenantId: string;
    productId: string;
    branchId: string;
    quantity: number;
    warehouseStock: number;
    storeStock: number;
    lastStockUpdate: string | null;
    createdAt: string;
    updatedAt: string;
    product: Product;
    unit?: string;
    branch?: Branch;
}

export default function ClientWrapper() {
    // Check if user has permission to view inventory
    const { isLoading: permissionLoading } = usePermissionCheck('/dashboard/inventory', 'view');

    const router = useRouter();
    const searchParams = useSearchParams();
    const productIdFromUrl = searchParams?.get('productId');

    const [successMessage, setSuccessMessage] = useState<string>('');
    const [searchTerm, setSearchTerm] = useState<string>('');

    // Use the cached fetch hook for better performance
    const {
        data: inventoryData,
        isLoading: loading,
        error: fetchError,
        refetch: refetchInventory
    } = useCachedFetch<{ success: boolean; inventory: InventoryItem[]; error?: string }>('/api/inventory');

    // Extract and filter inventory data
    const inventory = useMemo(() => {
        if (!inventoryData?.success) return [];

        const allInventory = inventoryData.inventory || [];

        // Filter inventory based on productId from URL if provided
        return productIdFromUrl
            ? allInventory.filter((item: InventoryItem) => item.productId === productIdFromUrl)
            : allInventory;
    }, [inventoryData, productIdFromUrl]);

    // Filter inventory based on search term
    const filteredInventory = useMemo(() => {
        if (!searchTerm) return inventory;

        return inventory.filter(item =>
            item.product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (item.product.code && item.product.code.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (item?.product?.category?.name && item?.product?.category?.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (item.branch?.name && item.branch.name.toLowerCase().includes(searchTerm.toLowerCase()))
        );
    }, [searchTerm, inventory]);

    // Extract error message
    const error = fetchError ? fetchError.message : (inventoryData?.error || '');

    const handleViewDetails = (inventoryId: string) => {
        router.push(`/dashboard/inventory/${inventoryId}`);
    };

    const formatDate = (dateString: string | null) => {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
    };

    const calculateStockValue = (item: InventoryItem) => {
        return item.quantity * item.product.costPrice;
    };

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
    };

    if (permissionLoading || loading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                    <h1 className="text-2xl font-bold mr-4">Inventory Management</h1>
                    <button
                        onClick={() => refetchInventory()}
                        className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-1 px-3 rounded focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 cursor-pointer text-sm"
                    >
                        Refresh
                    </button>
                </div>
                <div className="flex space-x-2">
                    <button
                        onClick={() => router.push('/dashboard/inventory/transactions')}
                        className="bg-purple-500 hover:bg-purple-600 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 cursor-pointer"
                    >
                        Transaction History
                    </button>
                    <button
                        onClick={() => router.push('/dashboard/inventory/transfer')}
                        className="bg-indigo-500 hover:bg-indigo-600 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50 cursor-pointer"
                    >
                        Warehouse → Store Transfer
                    </button>
                    <button
                        onClick={() => router.push('/dashboard/inventory/store-to-warehouse')}
                        className="bg-teal-500 hover:bg-teal-600 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-opacity-50 cursor-pointer"
                    >
                        Store → Warehouse Transfer
                    </button>
                    <button
                        onClick={() => router.push('/dashboard/products')}
                        className="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 cursor-pointer"
                    >
                        Manage Products
                    </button>
                    <button
                        onClick={() => router.push('/dashboard/inventory/new')}
                        className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 cursor-pointer"
                    >
                        Add New Inventory
                    </button>
                </div>
            </div>

            {successMessage && (
                <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <span className="block sm:inline">{successMessage}</span>
                </div>
            )}

            {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">Error: </strong>
                    <span className="block sm:inline">{error}</span>
                </div>
            )}

            {/* Search Bar */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-6">
                <div className="flex items-center">
                    <input
                        type="text"
                        value={searchTerm}
                        onChange={handleSearchChange}
                        onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                                e.preventDefault();
                                // Filtering is already handled in useEffect
                            }
                        }}
                        placeholder="Search by product name, code, category, or branch"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                </div>
            </div>

            {/* Summary cards */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                    <h2 className="text-sm text-gray-500 dark:text-gray-400 uppercase mb-2">Total Items</h2>
                    <p className="text-3xl font-bold">{inventory.length}</p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                    <h2 className="text-sm text-gray-500 dark:text-gray-400 uppercase mb-2">Total Quantity</h2>
                    <p className="text-3xl font-bold">{inventory.reduce((sum, item) => sum + Number(item.quantity || 0), 0)}</p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                    <h2 className="text-sm text-gray-500 dark:text-gray-400 uppercase mb-2">Warehouse Stock</h2>
                    <p className="text-3xl font-bold">{inventory.reduce((sum, item) => sum + Number(item.warehouseStock || 0), 0)}</p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                    <h2 className="text-sm text-gray-500 dark:text-gray-400 uppercase mb-2">Store Stock</h2>
                    <p className="text-3xl font-bold">{inventory.reduce((sum, item) => sum + Number(item.storeStock || 0), 0)}</p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                    <h2 className="text-sm text-gray-500 dark:text-gray-400 uppercase mb-2">Inventory Value</h2>
                    <p className="text-3xl font-bold"><span className='text-4xl'>৳</span>{inventory.reduce((sum, item) => sum + Number(calculateStockValue(item) || 0), 0).toFixed(2)}</p>
                </div>
            </div>

            {filteredInventory.length === 0 ? (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 text-center">
                    <p className="text-gray-600 dark:text-gray-400">
                        {inventory.length === 0 ? "No inventory items found." : "No items match your search criteria."}
                    </p>
                </div>
            ) : (
                <div className="overflow-x-auto">
                    <table className="min-w-full bg-white dark:bg-gray-800 rounded-lg shadow">
                        <thead className="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Product</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Code</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Category</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Branch</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Total Qty</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Warehouse</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Store</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Unit</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">MRP</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Cost Value</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Last Updated</th>
                            </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                            {filteredInventory.map((item) => (
                                <tr key={item.id} className={`hover:bg-gray-50 dark:hover:bg-gray-700 ${(item.product.isActive === false) ? 'bg-gray-100 dark:bg-gray-900 opacity-70' : ''}`}>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm font-medium text-gray-900 dark:text-white">{item.product.name}</div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-500 dark:text-gray-400">{item.product.code || 'N/A'}</div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-500 dark:text-gray-400">{item?.product?.category?.name || 'Uncategorized'}</div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-500 dark:text-gray-400">
                                            {item?.branch ? (
                                                <>
                                                    {item.branch.code && <span className="text-gray-400 mr-1">[{item.branch.code}]</span>}
                                                    {item.branch.name}
                                                    {item.branch.isMain && (
                                                        <span className="ml-1 text-xs bg-blue-100 text-blue-800 px-1 py-0.5 rounded">
                                                            Main
                                                        </span>
                                                    )}
                                                </>
                                            ) : 'Unknown Branch'}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${item.quantity > 0 ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'}`}>
                                            {item.quantity}
                                        </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${item.warehouseStock > 0 ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'}`}>
                                            {item.warehouseStock}
                                        </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${item.storeStock > 0 ? 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'}`}>
                                            {item.storeStock}
                                        </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {item.product.unit || 'units'}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        <span className='text-xl'>৳</span> {item.product.sellingPrice?.toFixed(2) || item.product.retailPrice?.toFixed(2) || '0.00'}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        <span className='text-xl'>৳</span> {calculateStockValue(item).toFixed(2)}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {formatDate(item.lastStockUpdate)}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}
        </div>
    );
}
