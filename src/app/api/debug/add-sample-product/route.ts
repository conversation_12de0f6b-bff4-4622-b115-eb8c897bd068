import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { products, productCategories } from "@/db/schema";
import { auth } from "~/auth";
import { v4 as uuidv4 } from 'uuid';
import { eq } from "drizzle-orm";

// POST /api/debug/add-sample-product - Add a sample product for testing
export async function POST(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get tenant ID from session based on user role
    let tenantId: string;
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      // For sales users, use their associated tenant's ID
      tenantId = session.user.tenantId;
    } else {
      // For tenant users, use their own ID
      tenantId = session.user.id;
    }

    // Check if any products already exist
    const existingProducts = await db.query.products.findMany({
      where: eq(products.tenantId, tenantId),
      limit: 1
    });

    if (existingProducts.length > 0) {
      return NextResponse.json({
        success: true,
        message: "Products already exist",
        product: existingProducts[0]
      });
    }

    // Get or create a category
    let categoryId;
    const existingCategories = await db.query.productCategories.findMany({
      where: eq(productCategories.tenantId, tenantId),
      limit: 1
    });

    if (existingCategories.length > 0) {
      categoryId = existingCategories[0].id;
    } else {
      // Create a new category
      const newCategory = {
        id: uuidv4(),
        tenantId: tenantId,
        name: "Sample Category",
        description: "Sample category for testing",
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await db.insert(productCategories).values(newCategory);
      categoryId = newCategory.id;
    }

    // Create a sample product
    const newProduct = {
      id: uuidv4(),
      tenantId: tenantId,
      categoryId: categoryId,
      name: "Sample Product",
      description: "Sample product for testing",
      code: "SAMPLE001",
      unit: "pcs",
      costPrice: 100,
      sellingPrice: 150,
      discount: 0,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await db.insert(products).values(newProduct);

    return NextResponse.json({
      success: true,
      message: "Sample product created successfully",
      product: newProduct
    });
  } catch (error) {
    console.error("Error adding sample product:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to add sample product",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
