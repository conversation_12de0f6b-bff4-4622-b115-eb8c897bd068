import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { orders } from "@/db/schema";
import { auth } from "../../../../../auth";
import { eq, desc } from "drizzle-orm";
import { withMenuPermission } from "@/middleware/menuPermissions";

async function getHandler(request: NextRequest, _context: any, permissionCheck: any) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ success: false, message: "Unauthorized" }, { status: 401 });
    }

    // Check if user has view permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      const menuPermission = permissionCheck.permissions.find(
        (p: any) => p.menu.path === '/dashboard/sales'
      );

      if (!menuPermission?.canView) {
        return NextResponse.json(
          { success: false, error: "You do not have permission to view sales" },
          { status: 403 }
        );
      }
    }

    // Get tenant ID from session based on user role
    let tenantId: string;
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      // For sales users, use their associated tenant's ID
      tenantId = session.user.tenantId;
    } else {
      // For tenant users, use their own ID
      tenantId = session.user.id;
    }

    // Get all orders for this tenant
    const allOrders = await db.query.orders.findMany({
      where: eq(orders.tenantId, tenantId),
      orderBy: [desc(orders.date)],
      with: {
        customer: true,
        items: {
          with: {
            product: true,
          },
        },
      },
      limit: 100, // Limit to recent orders
    });

    return NextResponse.json({
      success: true,
      orders: allOrders,
    });
  } catch (error) {
    console.error("Error fetching orders:", error);
    return NextResponse.json(
      { 
        success: false, 
        error: "Failed to fetch orders",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// Export the route handler with permission middleware
export const GET = withMenuPermission(getHandler, '/dashboard/sales');
