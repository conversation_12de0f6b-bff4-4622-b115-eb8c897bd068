'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { pathRequiresPermission } from './menu-list';

/**
 * Custom hook to check if a user has permission to access a specific page
 * @param menuPath The path of the menu to check
 * @param requiredPermission The type of permission required (view, create, edit, delete)
 * @returns Object containing loading state and permission check result
 */
export function usePermissionCheck(
  menuPath: string,
  requiredPermission: 'view' | 'create' | 'edit' | 'delete' = 'view'
) {
  const router = useRouter();
  const { data: session, status } = useSession();

  useEffect(() => {
    // Skip permission check if session is loading
    if (status === 'loading') return;

    // Check if user is logged in
    if (status === 'unauthenticated') {
      router.push('/');
      return;
    }

    // Check if the path requires permission
    const requiresPermission = pathRequiresPermission(menuPath);

    // If path doesn't require permission (like dashboard) or is not a menu path
    if (!requiresPermission) {
      console.log(`Path ${menuPath} doesn't require permission check`);
      return;
    }

    // Admin and tenant users have full permissions
    if (session?.user?.role === 'admin' || session?.user?.role === 'tenant') {
      return;
    }

    // For tenant_sale users, check permissions
    if (session?.user?.role === 'tenant_sale') {
      checkPermission();
    }
  }, [session, status, menuPath, requiredPermission]);

  const checkPermission = () => {
    // Check permissions directly from session
    if (!session?.user?.menuPermissions) {
      console.error('No menu permissions found in session');
      router.push('/dashboard');
      return;
    }

    // Find the menu permission for this path
    const menuPermission = session.user.menuPermissions.find(
      (p: any) => p.path === menuPath
    );

    if (!menuPermission) {
      console.error(`No permission found for path ${menuPath}`);
      router.push('/dashboard');
      return;
    }

    // Check if user has the required permission
    const hasPermission =
      requiredPermission === 'view' ? menuPermission.canView :
      requiredPermission === 'create' ? menuPermission.canCreate :
      requiredPermission === 'edit' ? menuPermission.canEdit :
      requiredPermission === 'delete' ? menuPermission.canDelete : false;

    if (!hasPermission) {
      router.push('/dashboard');
    }
  };

  return {
    isLoading: status === 'loading',
    isAuthenticated: status === 'authenticated'
  };
}
