import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users, tenants } from "@/db/schema";
import { auth } from "~/auth";
import { eq } from "drizzle-orm";
import bcrypt from "bcryptjs";
import { revalidatePath } from "next/cache";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Fetch user data
    const userData = await db.query.users.findFirst({
      where: eq(users.id, session.user.id),
      columns: {
        id: true,
        username: true,
        email: true,
        fullName: true,
        phone: true,
        address: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!userData) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Fetch tenant data if user is tenant or admin
    let tenantData = null;
    if (session.user.role === 'tenant' || session.user.role === 'admin') {
      tenantData = await db.query.tenants.findFirst({
        where: eq(tenants.userId, session.user.id),
        columns: {
          id: true,
          companyName: true,
          subscriptionType: true,
          subscriptionStatus: true,
          trialEndsAt: true,
          paymentStatus: true,
          lastPaymentDate: true,
          paymentDueDate: true,
          createdAt: true,
          updatedAt: true,
        },
      });
    }

    return NextResponse.json({
      success: true,
      user: userData,
      tenant: tenantData,
    });
  } catch (error) {
    console.error("Error fetching profile:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const data = await request.json();

    // Validate required fields
    if (!data.fullName || !data.username) {
      return NextResponse.json(
        { message: "Full name and username are required" },
        { status: 400 }
      );
    }

    // Check if username is already taken by another user
    if (data.username !== session.user.username) {
      const existingUser = await db.query.users.findFirst({
        where: eq(users.username, data.username),
      });

      if (existingUser && existingUser.id !== session.user.id) {
        return NextResponse.json(
          { message: "Username is already taken" },
          { status: 400 }
        );
      }
    }

    // Check if email is already taken by another user
    if (data.email && data.email !== session.user.email) {
      const existingUser = await db.query.users.findFirst({
        where: eq(users.email, data.email),
      });

      if (existingUser && existingUser.id !== session.user.id) {
        return NextResponse.json(
          { message: "Email is already taken" },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {
      username: data.username,
      email: data.email || null,
      fullName: data.fullName,
      phone: data.phone || null,
      address: data.address || null,
      updatedAt: new Date(),
    };

    // Only update password if provided
    if (data.password) {
      if (data.password.length < 6) {
        return NextResponse.json(
          { message: "Password must be at least 6 characters long" },
          { status: 400 }
        );
      }

      if (data.password !== data.confirmPassword) {
        return NextResponse.json(
          { message: "Passwords do not match" },
          { status: 400 }
        );
      }

      updateData.password = await bcrypt.hash(data.password, 10);
    }

    // Update user data
    const updatedUser = await db.update(users)
      .set(updateData)
      .where(eq(users.id, session.user.id))
      .returning({
        id: users.id,
        username: users.username,
        email: users.email,
        fullName: users.fullName,
        phone: users.phone,
        address: users.address,
        role: users.role,
        isActive: users.isActive,
        updatedAt: users.updatedAt,
      });

    // Update tenant data if provided and user is tenant/admin
    if (data.companyName && (session.user.role === 'tenant' || session.user.role === 'admin')) {
      await db.update(tenants)
        .set({
          companyName: data.companyName,
          updatedAt: new Date(),
        })
        .where(eq(tenants.userId, session.user.id));
    }

    // Revalidate relevant pages
    revalidatePath("/dashboard/profile");
    revalidatePath("/dashboard/brand-settings");

    return NextResponse.json({
      success: true,
      message: "Profile updated successfully",
      user: updatedUser[0],
    });
  } catch (error) {
    console.error("Error updating profile:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
