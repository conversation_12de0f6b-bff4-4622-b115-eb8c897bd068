import { auth } from "../../../../../auth";
import { redirect } from "next/navigation";
import UserSidebar from "../../../components/UserSidebar";
import { useState } from "react";
import AddUserForm from "../../../components/AddUserForm";

export default async function AddUserPage() {
  const session = await auth();
  
  // Redirect to login if not authenticated
  if (!session?.user) {
    redirect("/");
  }
  
  // Redirect if not admin
  if (session.user.role !== 'admin') {
    redirect("/dashboard");
  }
  
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Add New User</h2>
      </div>
      
      <div className="flex flex-col md:flex-row gap-6">
        <div className="md:w-1/4">
          <UserSidebar />
        </div>
        <div className="md:w-3/4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-xl font-semibold mb-4">User Details</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Create a new user account by filling out the form below.
            </p>
            
            <AddUserForm />
          </div>
        </div>
      </div>
    </div>
  );
}