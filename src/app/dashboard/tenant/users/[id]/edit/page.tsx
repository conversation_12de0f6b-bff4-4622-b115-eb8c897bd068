import { auth } from "~/auth";
import { db } from "@/lib/db";
import { users, tenantUsers } from "@/db/schema";
import { redirect } from "next/navigation";
import { eq, and } from "drizzle-orm";
import EditTenantSaleUserForm from "@/app/components/EditTenantSaleUserForm";

export default async function EditTenantSaleUserPage({ params }: {params: Promise<{ id: string }> }) {
  const session = await auth();
  
  // Redirect to login if not authenticated
  if (!session?.user) {
    redirect("/");
  }
  
  // Redirect if not tenant
  if (session.user.role !== 'tenant') {
    redirect("/dashboard");
  }
  
  // Check if user exists and belongs to this tenant
  const user = await db.query.users.findFirst({
    where: eq(users.id, (await params).id)
  });
  
  if (!user) {
    redirect("/dashboard/tenant/users");
  }
  
  const tenantUser = await db.query.tenantUsers.findFirst({
    where: and(
      eq(tenantUsers.userId, (await params).id),
      eq(tenantUsers.tenantId, session.user.id)
    )
  });
  
  if (!tenantUser) {
    redirect("/dashboard/tenant/users");
  }
  
  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Edit Tenant Sale User</h1>
      <EditTenantSaleUserForm userId={(await params).id} />
    </div>
  );
}
