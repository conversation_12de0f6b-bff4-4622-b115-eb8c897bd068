'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { formatCurrency } from '@/lib/utils';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";


import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { toast } from 'sonner';

// Define interfaces
interface Product {
  id: string;
  name: string;
  code: string | null;
  unit: string;
  costPrice: number;
  sellingPrice: number;
  stock?: number;
}

interface Customer {
  id: string;
  name: string;
  code: string | null;
}

interface Branch {
  id: string;
  name: string;
  code: string;
  isMain: boolean;
  isActive: boolean;
}

interface Order {
  id: string;
  memoNo: string;
  date: string;
  customerId: string | null;
  customer?: Customer;
  items: OrderItem[];
  totalAmount: number;
  branchId: string;
}

interface OrderItem {
  id: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  product: Product;
}

interface RefundReason {
  id: string;
  name: string;
  description: string | null;
}

interface RefundItem {
  productId: string;
  quantity: number;
  unitPrice: number;
  total: number;
  isDamaged: boolean;
  product?: Product;
}

// Define form schema
const formSchema = z.object({
  orderId: z.string().min(1, { message: "Order selection is required" }),
  customerId: z.string().optional(),
  branchId: z.string().min(1, { message: "Branch is required" }),
  reasonId: z.string().min(1, { message: "Reason is required" }),
  refundDate: z.string().min(1, { message: "Date is required" }),
  notes: z.string().optional(),
  isDamaged: z.boolean().default(false),
});

type FormValues = z.infer<typeof formSchema>;

export default function NewRefundPage() {
  const router = useRouter();
  const [loading, setLoading] = useState<boolean>(false);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [reasons, setReasons] = useState<RefundReason[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [searchMemoNo, setSearchMemoNo] = useState<string>('');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [refundItems, setRefundItems] = useState<RefundItem[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<string>('');
  const [quantity, setQuantity] = useState<number>(1);
  const [productQuantities, setProductQuantities] = useState<Record<string, number>>({});
  const [totalAmount, setTotalAmount] = useState<number>(0);

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      orderId: '',
      customerId: '',
      branchId: '',
      reasonId: '',
      refundDate: new Date().toISOString().split('T')[0],
      notes: '',
      isDamaged: false,
    },
  });

  useEffect(() => {
    fetchInitialData();
  }, []);

  useEffect(() => {
    // Calculate total amount whenever refund items change
    const total = refundItems.reduce((sum, item) => sum + item.total, 0);
    setTotalAmount(total);
  }, [refundItems]);

  // We'll use orders directly instead of filtered orders

  const fetchInitialData = async () => {
    try {
      setLoading(true);

      try {
        // Fetch branches
        const branchesResponse = await fetch('/api/branches');
        if (!branchesResponse.ok) {
          console.error('Failed to fetch branches:', branchesResponse.statusText);
        } else {
          const branchesData = await branchesResponse.json();

          if (branchesData.success) {
            setBranches(branchesData.branches);

            // Set default branch if there's a main branch
            const mainBranch = branchesData.branches.find((branch: Branch) => branch.isMain);
            if (mainBranch) {
              form.setValue('branchId', mainBranch.id);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching branches:', error);
      }

      try {
        // Fetch customers
        const customersResponse = await fetch('/api/customers');
        if (!customersResponse.ok) {
          console.error('Failed to fetch customers:', customersResponse.statusText);
        } else {
          const customersData = await customersResponse.json();

          if (customersData.success) {
            // We'll use customer data directly from the order
            console.log('Customers loaded:', customersData.customers.length);
          }
        }
      } catch (error) {
        console.error('Error fetching customers:', error);
      }

      try {
        // Fetch products
        const productsResponse = await fetch('/api/products');
        if (!productsResponse.ok) {
          console.error('Failed to fetch products:', productsResponse.statusText);
        } else {
          const productsData = await productsResponse.json();

          if (productsData.success) {
            // We'll use product data directly from the order items
            console.log('Products loaded:', productsData.products.length);
          }
        }
      } catch (error) {
        console.error('Error fetching products:', error);
      }

      try {
        // Fetch refund reasons
        const reasonsResponse = await fetch('/api/refunds/reasons');
        if (!reasonsResponse.ok) {
          console.error('Failed to fetch refund reasons:', reasonsResponse.statusText);
        } else {
          const reasonsData = await reasonsResponse.json();

          if (reasonsData.success) {
            setReasons(reasonsData.reasons);
          }
        }
      } catch (error) {
        console.error('Error fetching refund reasons:', error);
      }

      try {
        // Fetch recent orders
        const ordersResponse = await fetch('/api/sales/list');
        if (!ordersResponse.ok) {
          console.error('Failed to fetch orders:', ordersResponse.statusText);
        } else {
          const ordersData = await ordersResponse.json();

          if (ordersData.success) {
            setOrders(ordersData.orders);
          }
        }
      } catch (error) {
        console.error('Error fetching orders:', error);
      }

    } catch (error) {
      console.error('Error fetching initial data:', error);
      toast.error('Failed to load initial data');
    } finally {
      setLoading(false);
    }
  };

  const handleSearchOrder = () => {
    if (!searchMemoNo.trim()) {
      toast.error('Please enter a memo number to search');
      return;
    }

    // Search for exact memo number match first
    const exactMatch = orders.find(order =>
      order.memoNo.toLowerCase() === searchMemoNo.trim().toLowerCase()
    );

    if (exactMatch) {
      // If exact match found, select it automatically
      handleOrderSelect(exactMatch.id);
      toast.success(`Found order with memo #${exactMatch.memoNo}`);
      return;
    }

    // If no exact match, search for partial matches
    const searchTerm = searchMemoNo.trim().toLowerCase();
    const filtered = orders.filter(order =>
      order.memoNo.toLowerCase().includes(searchTerm)
    );

    if (filtered.length === 0) {
      toast.error(`No orders found with memo number containing "${searchMemoNo}"`);
    } else if (filtered.length === 1) {
      // If only one order is found, select it automatically
      handleOrderSelect(filtered[0].id);
      toast.success(`Found order with memo #${filtered[0].memoNo}`);
    } else {
      // If multiple matches, select the first one
      handleOrderSelect(filtered[0].id);
      toast.success(`Found ${filtered.length} orders matching "${searchMemoNo}". Selected the first match.`);
    }
  };

  const handleOrderSelect = (orderId: string) => {
    if (!orderId) {
      setSelectedOrder(null);
      form.setValue('orderId', '');
      return;
    }

    const order = orders.find(o => o.id === orderId);
    if (order) {
      setSelectedOrder(order);

      // Set order ID in the form
      form.setValue('orderId', order.id);

      // Set branch ID from the order
      form.setValue('branchId', order.branchId);

      // Branch name will be displayed in the UI using the branches state

      // Set customer ID if available
      if (order.customerId) {
        form.setValue('customerId', order.customerId);
      }

      // Clear existing refund items and reset product quantities
      setRefundItems([]);
      setProductQuantities({});

      // Add order items to refund items
      if (order.items && order.items.length > 0) {
        const newRefundItems = order.items.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          total: item.quantity * item.unitPrice,
          isDamaged: false,
          product: item.product
        }));

        setRefundItems(newRefundItems);
      }
    } else {
      setSelectedOrder(null);
      form.setValue('orderId', '');
    }
  };

  const handleAddProduct = (productId?: string, qty?: number) => {
    if (!selectedOrder) {
      toast.error('Please select an order first');
      return;
    }

    // Use provided productId or selectedProduct from state
    const productToAdd = productId || selectedProduct;
    // Use provided quantity or quantity from state
    const quantityToAdd = qty || quantity;

    if (!productToAdd || quantityToAdd <= 0) {
      toast.error('Please select a product and enter a valid quantity');
      return;
    }

    // Find the product in the selected order's items
    const orderItem = selectedOrder.items.find(item => item.productId === productToAdd);
    if (!orderItem) {
      toast.error('Selected product not found in the order');
      return;
    }

    // Check if product already exists in refund items
    const existingItemIndex = refundItems.findIndex(item => item.productId === productToAdd);

    if (existingItemIndex >= 0) {
      // Update existing item
      const updatedItems = [...refundItems];
      const existingItem = updatedItems[existingItemIndex];

      // Calculate new quantity, ensuring it doesn't exceed the original order quantity
      const newQuantity = existingItem.quantity + quantityToAdd;
      if (newQuantity > orderItem.quantity) {
        toast.error(`Cannot refund more than the original quantity. Available: ${orderItem.quantity}`);
        return;
      }

      const newTotal = newQuantity * existingItem.unitPrice;

      updatedItems[existingItemIndex] = {
        ...existingItem,
        quantity: newQuantity,
        total: newTotal,
        isDamaged: form.getValues('isDamaged'), // Use global damaged setting
      };

      setRefundItems(updatedItems);
    } else {
      // Add new item
      const total = quantityToAdd * orderItem.unitPrice;

      setRefundItems([
        ...refundItems,
        {
          productId: orderItem.productId,
          quantity: quantityToAdd,
          unitPrice: orderItem.unitPrice,
          total,
          isDamaged: form.getValues('isDamaged'), // Use global damaged setting
          product: orderItem.product,
        },
      ]);
    }

    // Reset selection and quantity for this product
    setSelectedProduct('');
    setQuantity(1);

    // Reset the quantity in productQuantities back to 1
    const itemKey = `qty-${productToAdd}`;
    setProductQuantities(prev => ({
      ...prev,
      [itemKey]: 1
    }));

    toast.success(`Added ${quantityToAdd} ${orderItem.product.name} to refund`);
  };

  const handleRemoveItem = (index: number) => {
    const item = refundItems[index];
    const productId = item.productId;

    // Remove the item from refundItems
    const updatedItems = [...refundItems];
    updatedItems.splice(index, 1);
    setRefundItems(updatedItems);

    // Reset the quantity in productQuantities back to 1
    const itemKey = `qty-${productId}`;
    setProductQuantities(prev => ({
      ...prev,
      [itemKey]: 1
    }));

    toast.success(`Removed ${item.product?.name || 'item'} from refund`);
  };

  const handleToggleItemDamaged = (index: number) => {
    const updatedItems = [...refundItems];
    updatedItems[index].isDamaged = !updatedItems[index].isDamaged;
    setRefundItems(updatedItems);
  };

  const onSubmit = async (data: FormValues) => {
    try {
      if (!selectedOrder) {
        toast.error('Please select an order to process a refund');
        return;
      }

      if (refundItems.length === 0) {
        toast.error('Please add at least one product to refund');
        return;
      }

      setLoading(true);

      // Prepare refund data
      const refundData = {
        ...data,
        orderId: selectedOrder.id,
        totalAmount,
        items: refundItems.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          total: item.total,
          isDamaged: item.isDamaged,
        })),
      };

      // Submit refund
      const response = await fetch('/api/refunds', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(refundData),
      });

      const result = await response.json();

      if (result.success) {
        // Count how many items will be returned to inventory
        const returnedItemsCount = refundItems.filter(item => !item.isDamaged).length;

        if (returnedItemsCount > 0) {
          toast.success(
            `Refund processed successfully. ${returnedItemsCount} item(s) have been returned to store inventory.`
          );
        } else {
          toast.success('Refund processed successfully. No items were returned to inventory.');
        }

        router.push('/dashboard/refunds');
      } else {
        toast.error(result.error || 'Failed to process refund');
      }
    } catch (error) {
      console.error('Error processing refund:', error);
      toast.error('An error occurred while processing the refund');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Process New Refund</h1>
        <Link href="/dashboard/refunds">
          <Button className='cursor-pointer' variant="outline">Back to Refunds</Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Refund Form */}
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Refund Details</CardTitle>
              <CardDescription>
                Enter the details for this refund
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  {/* Hidden Order ID field */}
                  <FormField
                    control={form.control}
                    name="orderId"
                    render={({ field }) => (
                      <input type="hidden" {...field} />
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Branch Selection */}
                    <FormField
                      control={form.control}
                      name="branchId"
                      render={({ field }) => (
                        <FormItem>
                          <FormItem className="flex flex-col space-y-1.5">
                            <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                              Branch
                            </label>
                            {selectedOrder ? (
                              <div className="border rounded-md p-2 bg-gray-50">
                                {(() => {
                                  const branch = branches.find(b => b.id === selectedOrder.branchId);
                                  return branch ? `${branch.name} (${branch.code})` : 'Unknown Branch';
                                })()}
                              </div>
                            ) : (
                              <select
                                className="w-full border border-gray-300 rounded-md shadow-sm px-4 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                onChange={(e) => field.onChange(e.target.value)}
                                value={field.value}
                              >
                                <option value="">Select Branch</option>
                                {branches.map((branch) => (
                                  <option key={branch.id} value={branch.id}>
                                    {branch.name} ({branch.code})
                                  </option>
                                ))}
                              </select>
                            )}
                          </FormItem>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Date Selection */}
                    <FormField
                      control={form.control}
                      name="refundDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormItem className="flex flex-col space-y-1.5">
                            <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                              Refund Date
                            </label>
                            <FormControl>
                              <Input type="date" {...field} />
                            </FormControl>
                          </FormItem>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {selectedOrder && (
                    <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded mb-4">
                      <p className="font-medium">Order Selected: Memo #{selectedOrder.memoNo}</p>
                      <p className="text-sm">Branch and customer details are automatically filled from the order.</p>
                    </div>
                  )}

                  <div className="space-y-4">
                    {/* Order Search and Selection */}
                    <div className="flex flex-col space-y-1.5">
                      <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                        Search Order by Memo #
                      </label>
                      <div className="flex space-x-2">
                        <Input
                          type="text"
                          placeholder="Enter memo number"
                          value={searchMemoNo}
                          onChange={(e) => setSearchMemoNo(e.target.value)}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleSearchOrder}
                          disabled={!searchMemoNo.trim()}
                          className='cursor-pointer'
                        >
                          Search
                        </Button>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Enter the memo number to find the order
                      </p>
                    </div>

                    {selectedOrder ? (
                      <div className="border rounded-md p-3 bg-blue-50">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium">Order Found: Memo #{selectedOrder.memoNo}</h4>
                            <p className="text-sm text-gray-600">
                              Date: {new Date(selectedOrder.date).toLocaleDateString()}
                            </p>
                            <p className="text-sm text-gray-600">
                              Customer: {selectedOrder.customer?.name || 'No Customer'}
                            </p>
                            <p className="text-sm text-gray-600">
                              Total Amount: {formatCurrency(selectedOrder.totalAmount)}
                            </p>
                            <p className="text-sm text-gray-600">
                              Items: {selectedOrder.items.length}
                            </p>
                            <p className="text-sm text-gray-600">
                              Branch: {(() => {
                                const branch = branches.find(b => b.id === selectedOrder.branchId);
                                return branch ? `${branch.name} (${branch.code})` : 'Unknown';
                              })()}
                            </p>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedOrder(null);
                              setRefundItems([]);
                              form.setValue('orderId', '');
                              setSearchMemoNo('');
                            }}
                            className='cursor-pointer'
                          >
                            Clear
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="border rounded-md p-3 bg-gray-50">
                        <p className="text-center text-gray-500">
                          No order selected. Search by memo number to find an order.
                        </p>
                      </div>
                    )}

                    {/* Hidden Customer ID field */}
                    <FormField
                      control={form.control}
                      name="customerId"
                      render={({ field }) => (
                        <input type="hidden" {...field} />
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Reason Selection */}
                    <FormField
                      control={form.control}
                      name="reasonId"
                      render={({ field }) => (
                        <FormItem>
                          <FormItem className="flex flex-col space-y-1.5">
                            <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                              Reason
                            </label>
                            <select
                              className="w-full border border-gray-300 rounded-md shadow-sm px-4 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                              onChange={(e) => field.onChange(e.target.value)}
                              value={field.value}
                            >
                              <option value="">Select Reason</option>
                              {reasons.map((reason) => (
                                <option key={reason.id} value={reason.id}>
                                  {reason.name}
                                </option>
                              ))}
                            </select>
                          </FormItem>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Is Damaged Checkbox */}
                    <FormField
                      control={form.control}
                      name="isDamaged"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                              Mark All Items as Damaged
                            </label>
                            <p className="text-sm text-muted-foreground">
                              Damaged items will not be added back to inventory. Non-damaged items will be returned to store inventory.
                            </p>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Notes */}
                  <FormField
                    control={form.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex flex-col space-y-1.5">
                          <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Notes
                          </label>
                          <FormControl>
                            <Textarea
                              placeholder="Enter any additional notes about this refund"
                              className="resize-none"
                              {...field}
                            />
                          </FormControl>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {refundItems.length > 0 && (
                    <div className="bg-gray-50 p-4 rounded-md mb-4">
                      <h3 className="font-medium mb-2">Refund Summary</h3>
                      <div className="space-y-2 text-sm">
                        <p>Total Items: {refundItems.length}</p>
                        <p>
                          Items to return to inventory: {refundItems.filter(item => !item.isDamaged).length}
                          {refundItems.filter(item => !item.isDamaged).length > 0 && (
                            <span className="text-green-600 ml-2">
                              (Will be added back to store inventory)
                            </span>
                          )}
                        </p>
                        <p>
                          Damaged items: {refundItems.filter(item => item.isDamaged).length}
                          {refundItems.filter(item => item.isDamaged).length > 0 && (
                            <span className="text-red-600 ml-2">
                              (Will not be returned to inventory)
                            </span>
                          )}
                        </p>
                        <p className="font-medium">Total refund amount: {formatCurrency(totalAmount)}</p>
                      </div>
                    </div>
                  )}

                  <div className="flex justify-end">
                    <Button
                      type="submit"
                      disabled={loading || refundItems.length === 0}
                      className="bg-blue-500 hover:bg-blue-600 text-white cursor-pointer"
                    >
                      {loading ? 'Processing...' : 'Process Refund'}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>

        {/* Product Selection and Refund Items */}
        <div className="md:col-span-1">
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Order Products</CardTitle>
              <CardDescription>
                Select products to refund
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!selectedOrder ? (
                <div className="text-center py-4 text-gray-500">
                  Search for an order first to see available products
                </div>
              ) : selectedOrder.items.length === 0 ? (
                <div className="text-center py-4 text-gray-500">
                  No products found in this order
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="border rounded-md p-2 bg-gray-50">
                    <p className="text-sm font-medium mb-2">Available Products from Order</p>
                    <div className="space-y-3 max-h-60 overflow-y-auto">
                      {selectedOrder.items.map((item) => {
                        // Check if this item is already in refundItems
                        const existingItem = refundItems.find(ri => ri.productId === item.productId);
                        const remainingQty = item.quantity - (existingItem?.quantity || 0);

                        // Create a state key for this item's quantity
                        const itemKey = `qty-${item.productId}`;
                        if (productQuantities[itemKey] === undefined) {
                          // Initialize quantity state for this product if not already set
                          setProductQuantities(prev => ({
                            ...prev,
                            [itemKey]: 1
                          }));
                        }

                        return (
                          <div key={item.productId} className="flex flex-col border-b pb-2">
                            <div className="flex justify-between items-center">
                              <div>
                                <p className="font-medium">{item.product.name}</p>
                                <p className="text-xs text-gray-500">
                                  {item.product.code ? `Code: ${item.product.code}` : ''} |
                                  Price: {formatCurrency(item.unitPrice)} |
                                  Available: {remainingQty} of {item.quantity}
                                </p>
                              </div>
                            </div>

                            {remainingQty > 0 && (
                              <div className="flex items-center mt-2 space-x-2">
                                <div className="flex-1">
                                  <Input
                                    type="number"
                                    min="1"
                                    max={remainingQty}
                                    value={productQuantities[itemKey] || 1}
                                    onChange={(e) => {
                                      const val = parseInt(e.target.value) || 1;
                                      const newQty = Math.min(val, remainingQty);
                                      setProductQuantities(prev => ({
                                        ...prev,
                                        [itemKey]: newQty
                                      }));
                                    }}
                                    className="h-8 text-sm"
                                    placeholder="Qty"
                                  />
                                </div>
                                <Button
                                  size="sm"
                                  onClick={() => {
                                    if (remainingQty > 0) {
                                      const qtyToAdd = productQuantities[itemKey] || 1;
                                      handleAddProduct(item.productId, qtyToAdd);
                                    }
                                  }}
                                  disabled={remainingQty <= 0}
                                  className="whitespace-nowrap"
                                >
                                  Add to Refund
                                </Button>
                              </div>
                            )}

                            {remainingQty <= 0 && (
                              <div className="mt-2">
                                <Badge variant="outline" className="bg-gray-100">
                                  Already added to refund
                                </Badge>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 mt-4">
                    <Checkbox
                      id="all-damaged"
                      checked={form.getValues('isDamaged')}
                      onCheckedChange={(checked) => {
                        form.setValue('isDamaged', checked === true);

                        // Update all refund items to match the global damaged setting
                        if (refundItems.length > 0) {
                          const updatedItems = refundItems.map(item => ({
                            ...item,
                            isDamaged: checked === true
                          }));
                          setRefundItems(updatedItems);
                        }
                      }}
                      disabled={!selectedOrder}
                    />
                    <label
                      htmlFor="all-damaged"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Mark all items as damaged
                    </label>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Refund Items</CardTitle>
              <CardDescription>
                Items to be refunded
              </CardDescription>
            </CardHeader>
            <CardContent>
              {refundItems.length === 0 ? (
                <div className="text-center py-4 text-gray-500">
                  No items added yet
                </div>
              ) : (
                <div className="space-y-4">
                  {refundItems.map((item, index) => (
                    <div key={index} className="border rounded-md p-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium">
                            {item.product?.name}
                            {item.isDamaged && (
                              <Badge className="ml-2 bg-red-100 text-red-800 border-red-200">
                                Damaged
                              </Badge>
                            )}
                          </h4>
                          <p className="text-sm text-gray-500">
                            {item.quantity} x {formatCurrency(item.unitPrice)}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant={item.isDamaged ? "destructive" : "outline"}
                            size="sm"
                            className='cursor-pointer'
                            onClick={() => handleToggleItemDamaged(index)}
                            title={item.isDamaged ?
                              "This item is marked as damaged and will not be returned to inventory" :
                              "This item is not damaged and will be returned to store inventory"}
                          >
                            {item.isDamaged ? 'Damaged' : 'Good'}
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleRemoveItem(index)}
                            className='cursor-pointer'
                          >
                            Remove
                          </Button>
                        </div>
                      </div>
                      <div className="mt-2 flex justify-between">
                        <span className="text-sm">Total:</span>
                        <span className="font-medium">{formatCurrency(item.total)}</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="font-medium">Total Amount:</div>
              <div className="font-bold text-lg">{formatCurrency(totalAmount)}</div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}
