import { format, formatInTimeZone } from 'date-fns-tz';

// Default timezone for the application
export const DEFAULT_TIMEZONE = 'Asia/Dhaka';

/**
 * Format a date in the default timezone (Asia/Dhaka)
 * @param date Date to format
 * @param formatStr Format string
 * @returns Formatted date string in the default timezone
 */
export function formatInDhaka(date: Date | number | string, formatStr: string = 'yyyy-MM-dd HH:mm:ss'): string {
  if (!date) return '';
  const d = typeof date === 'object' ? date : new Date(date);
  return formatInTimeZone(d, DEFAULT_TIMEZONE, formatStr);
}

/**
 * Get current date in Dhaka timezone
 * @returns Current date in Dhaka timezone
 */
export function getDhakaDate(): Date {
  // For simplicity, we'll just return the current date
  // The actual timezone conversion will be handled by the formatInTimeZone function
  return new Date();
}

/**
 * Format a date for display in a localized format using Dhaka timezone
 * @param date Date to format
 * @param options Intl.DateTimeFormatOptions
 * @returns Formatted date string
 */
export function formatLocalizedDate(
  date: Date | number | string,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }
): string {
  if (!date) return '';
  const d = typeof date === 'object' ? date : new Date(date);

  // Use Intl.DateTimeFormat with the Asia/Dhaka timezone
  return new Intl.DateTimeFormat('en-US', {
    ...options,
    timeZone: DEFAULT_TIMEZONE
  }).format(d);
}
