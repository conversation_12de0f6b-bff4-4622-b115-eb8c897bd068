'use client';

import { useState, useEffect } from 'react';

interface BranchDisplayProps {
  branchId: string;
}

export default function BranchDisplay({ branchId }: BranchDisplayProps) {
  const [branchName, setBranchName] = useState<string | null>(null);
  const [branchCode, setBranchCode] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBranchDetails = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/branches/${branchId}`);

        if (!response.ok) {
          throw new Error(`Failed to fetch branch: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.success) {
          // Handle both cases: when branch is directly in the response or in an array
          if (data.branch) {
            // If branch is directly in the response
            setBranchName(data.branch.name);
            setBranchCode(data.branch.code || null);
          } else if (Array.isArray(data.branches) && data.branches.length > 0) {
            // If branch is in an array (first item)
            setBranchName(data.branches[0].name);
            setBranchCode(data.branches[0].code || null);
          } else {
            setError('Branch data not found in response');
          }
        } else {
          setError(data.error || 'Failed to load branch details');
        }
      } catch (error) {
        console.error('Error fetching branch details:', error);
        setError('An error occurred while fetching branch details');
      } finally {
        setLoading(false);
      }
    };

    if (branchId) {
      fetchBranchDetails();
    }
  }, [branchId]);

  if (loading) {
    return (
      <div className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-md flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
        Loading branch...
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-sm bg-red-100 text-red-800 px-2 py-1 rounded-md flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
        Branch error
      </div>
    );
  }

  return (
    <div className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-md flex items-center">
      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
      </svg>
      {branchCode ? `[${branchCode}] ` : ''}{branchName || 'Current Branch'}
    </div>
  );
}
