import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { vendors, users } from "@/db/schema";
import { auth } from "~/auth";
import { eq, desc, sql, or, and } from "drizzle-orm";

// GET /api/products/vendors - List all vendors available for product creation
// This includes:
// 1. Vendors created by the current tenant
// 2. Vendors created by admin (that should be available to all tenants)

export async function GET(request: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const tenantId = session.user.id;
    const { searchParams } = new URL(request.url);
    const search = searchParams.get("search");
    const code = searchParams.get("code");

    // Build the query to get both tenant's vendors and admin-created vendors
    let whereCondition;

    if (code) {
      // If code is provided, search specifically by vendor code
      whereCondition = and(
        or(
          // Vendors belonging to this tenant
          eq(vendors.tenantId, tenantId),
          // Vendors created by admin (with userId set)
          and(
            sql`${vendors.userId} IS NOT NULL`,
            eq(vendors.isActive, true)
          )
        ),
        eq(vendors.vendorCode, code)
      );
    } else if (search) {
      // If search is provided, filter by name or code and include both tenant's vendors and admin vendors
      whereCondition = and(
        or(
          // Vendors belonging to this tenant
          eq(vendors.tenantId, tenantId),
          // Vendors created by admin (with userId set)
          and(
            sql`${vendors.userId} IS NOT NULL`,
            eq(vendors.isActive, true)
          )
        ),
        or(
          sql`${vendors.name} LIKE ${'%' + search + '%'}`,
          sql`${vendors.vendorCode} LIKE ${'%' + search + '%'}`
        )
      );
    } else {
      // No search - get all vendors for this tenant and all admin-created vendors
      whereCondition = or(
        // Vendors belonging to this tenant
        eq(vendors.tenantId, tenantId),
        // Vendors created by admin (with userId set)
        and(
          sql`${vendors.userId} IS NOT NULL`,
          eq(vendors.isActive, true)
        )
      );
    }

    const vendorsList = await db.query.vendors.findMany({
      where: whereCondition,
      orderBy: [desc(vendors.createdAt)],
      with: {
        user: {
          columns: {
            id: true,
            username: true,
            fullName: true,
            email: true,
            role: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      vendors: vendorsList,
    });
  } catch (error) {
    console.error("Error fetching vendors for products:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch vendors" },
      { status: 500 }
    );
  }
}
