import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { payments, customers, branches, orders } from "@/db/schema";
import { auth } from "~/auth";
import { v4 as uuidv4 } from "uuid";
import { eq, or, and, asc } from "drizzle-orm";
import { revalidatePath } from "next/cache";

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const data = await request.json();

    // Validate required fields
    if (
      !data.tenantId ||
      !data.customerId ||
      !data.amount ||
      data.amount <= 0
    ) {
      return NextResponse.json(
        { message: "Missing required fields" },
        { status: 400 }
      );
    }

    await db.transaction(async (tx) => {
      // Get the customer to check current balance
      const customer = await tx.query.customers.findFirst({
        where: (customers, { eq }) => eq(customers.id, data.customerId),
      });

      if (!customer) {
        throw new Error("Customer not found");
      }

      // Check if this payment is associated with a specific order
      const orderId = data.orderId || null;
      let order = null;

      if (orderId) {
        // Get the order details if orderId is provided
        order = await tx.query.orders.findFirst({
          where: (orders, { eq }) => eq(orders.id, orderId),
        });

        if (!order) {
          throw new Error("Order not found");
        }

        // For specific order payments, check against the order's due amount
        const orderDueAmount = order.dueAmount || 0;
        if (data.amount > orderDueAmount) {
          throw new Error(`Payment amount exceeds order due amount: ${orderDueAmount}`);
        }
        console.log(`Order payment validation passed: amount=${data.amount}, orderDue=${orderDueAmount}`);
      } else {
        // For general due payments, check against customer's total balance
        const currentBalance = customer.currentBalance || 0;
        if (data.amount > currentBalance) {
          throw new Error("Payment amount exceeds current due amount");
        }
      }

      // Get a valid branch ID if not provided
      let branchId = data.branchId;
      if (!branchId) {
        // Try to find a branch for this tenant
        const tenantBranches = await db
          .select()
          .from(branches)
          .where(eq(branches.tenantId, data.tenantId));

        if (tenantBranches.length > 0) {
          // Use the first branch or a main branch if available
          const mainBranch = tenantBranches.find(b => b.isMain);
          branchId = mainBranch ? mainBranch.id : tenantBranches[0].id;
        } else {
          // Create a default branch if none exists
          const newBranchId = uuidv4();
          await db.insert(branches).values({
            id: newBranchId,
            tenantId: data.tenantId,
            name: "Main Store",
            code: "MAIN",
            isMain: true,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date(),
          });
          branchId = newBranchId;
        }
      }

      // Create the payment record
      await tx.insert(payments).values({
        id: uuidv4(),
        tenantId: data.tenantId,
        branchId, // Use the valid branch ID
        customerId: data.customerId,
        orderId: orderId, // Link to order if provided
        amount: data.amount,
        paymentMethod: data.paymentMethod || "cash",
        paymentReference: data.paymentReference || null,
        paymentType: order ? "sale" : "due_payment",
        notes: data.notes || (order ? `Payment for order #${order.memoNo}` : "Due payment"),
        performedBy: session.user.id,
        date: new Date(data.date || new Date()),
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Update the customer's balance
      const currentBalance = customer.currentBalance || 0;
      const newBalance = currentBalance - data.amount;
      await tx
        .update(customers)
        .set({
          currentBalance: newBalance,
          updatedAt: new Date(),
        })
        .where(eq(customers.id, data.customerId));

      // If this payment is for a specific order, update the order's payment status
      if (order) {
        // Calculate new paid amount and due amount
        const newPaidAmount = (order.paidAmount || 0) + data.amount;
        const newDueAmount = Math.max(0, (order.totalAmount || 0) - newPaidAmount);

        // Determine the payment status based on the new amounts
        let paymentStatus: "paid" | "partial" | "unpaid" = "unpaid";
        // Use a small tolerance (0.01) to account for potential floating-point precision issues
        if (newDueAmount <= 0.01 || newPaidAmount >= order.totalAmount) {
          paymentStatus = "paid";
        } else if (newPaidAmount > 0) {
          paymentStatus = "partial";
        }

        console.log(`Updating order payment status: newPaidAmount=${newPaidAmount}, newDueAmount=${newDueAmount}, totalAmount=${order.totalAmount}, paymentStatus=${paymentStatus}`);

        // Update the order
        await tx
          .update(orders)
          .set({
            paidAmount: newPaidAmount,
            dueAmount: newDueAmount,
            paymentStatus: paymentStatus,
            updatedAt: new Date(),
          })
          .where(eq(orders.id, orderId));
      } else {
        // For due payments (no specific order), update payment status of unpaid/partial orders
        // Get all unpaid and partial orders for this customer
        const customerOrders = await tx.query.orders.findMany({
          where: (orders, { eq, and, or }) => and(
            eq(orders.customerId, data.customerId),
            eq(orders.tenantId, data.tenantId),
            or(
              eq(orders.paymentStatus, "unpaid"),
              eq(orders.paymentStatus, "partial")
            )
          ),
          orderBy: (orders, { asc }) => [asc(orders.date)] // Process oldest orders first
        });

        console.log(`Found ${customerOrders.length} unpaid/partial orders for customer ${data.customerId}`);

        // Apply the payment amount to orders starting from the oldest
        let remainingPayment = data.amount;

        for (const customerOrder of customerOrders) {
          if (remainingPayment <= 0) break;

          const orderDue = customerOrder.dueAmount || 0;
          if (orderDue <= 0) continue; // Skip orders with no due amount

          // Calculate how much to apply to this order
          const paymentForThisOrder = Math.min(remainingPayment, orderDue);
          const newOrderPaidAmount = (customerOrder.paidAmount || 0) + paymentForThisOrder;
          const newOrderDueAmount = Math.max(0, (customerOrder.totalAmount || 0) - newOrderPaidAmount);

          // Determine new payment status
          let newPaymentStatus: "paid" | "partial" | "unpaid" = "unpaid";
          if (newOrderDueAmount <= 0.01 || newOrderPaidAmount >= customerOrder.totalAmount) {
            newPaymentStatus = "paid";
          } else if (newOrderPaidAmount > 0) {
            newPaymentStatus = "partial";
          }

          // Update the order
          await tx
            .update(orders)
            .set({
              paidAmount: newOrderPaidAmount,
              dueAmount: newOrderDueAmount,
              paymentStatus: newPaymentStatus,
              updatedAt: new Date(),
            })
            .where(eq(orders.id, customerOrder.id));

          console.log(`Updated order ${customerOrder.memoNo}: paidAmount=${newOrderPaidAmount}, dueAmount=${newOrderDueAmount}, paymentStatus=${newPaymentStatus}`);

          // Reduce remaining payment amount
          remainingPayment -= paymentForThisOrder;
        }

        if (remainingPayment > 0) {
          console.log(`Remaining payment amount after applying to orders: ${remainingPayment}`);
        }
      }
    });

    // Revalidate all relevant paths to reflect the changes with stronger cache busting
    // Always revalidate the sales pages with a tag to ensure complete refresh
    revalidatePath("/dashboard/sales", "page");

    // Revalidate the sales list API
    revalidatePath("/api/sales/paginated", "page");

    // Revalidate the specific order page if an order ID was provided
    if (data.orderId) {
      revalidatePath(`/dashboard/sales/${data.orderId}`, "page");
      // Also revalidate any API that might return this order
      revalidatePath(`/api/sales/${data.orderId}`, "page");
    }

    // Revalidate the payments page
    revalidatePath("/dashboard/payments", "page");
    revalidatePath("/api/payments", "page");

    // Revalidate the customers page since customer balance has changed
    revalidatePath("/dashboard/customers", "page");
    revalidatePath("/api/customers", "page");

    const response = NextResponse.json(
      { message: "Payment recorded successfully" },
      { status: 201 }
    );

    // Add cache control headers to prevent caching
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    return response;
  } catch (error) {
    console.error("Error recording payment:", error);
    return NextResponse.json(
      { message: "Failed to record payment", error: (error as Error).message },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const customerId = searchParams.get("customerId");
    const tenantId = session.user.id;

    // Query conditions
    const conditions: any[] = [];
    conditions.push(eq(payments.tenantId, tenantId));

    if (customerId) {
      conditions.push(eq(payments.customerId, customerId));
    }

    // Get payments
    const paymentRecords = await db.query.payments.findMany({
      where: (payments, { and }) => and(...conditions),
      with: {
        customer: true,
      },
      orderBy: (payments, { desc }) => [desc(payments.date)],
    });

    const response = NextResponse.json(paymentRecords);

    // Add cache control headers to prevent caching
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    return response;
  } catch (error) {
    console.error("Error getting payments:", error);
    return NextResponse.json(
      { message: "Failed to get payments", error: (error as Error).message },
      { status: 500 }
    );
  }
}
