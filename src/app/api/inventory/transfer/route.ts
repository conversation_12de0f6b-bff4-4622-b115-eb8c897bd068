import { NextRequest, NextResponse } from "next/server";
import { auth } from "~/auth";
import { db } from "@/lib/db";
import { branches, inventoryTransactions, products } from "@/db/schema";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import { eq, and, sql } from "drizzle-orm";

// Schema for validating branch inventory transfers
const branchTransferSchema = z
  .object({
    productId: z.string().min(1, "Product ID is required"),
    quantity: z.number().int().min(1, "Quantity must be at least 1"),
    branchId: z.string().min(1, "Branch is required"),
    notes: z.string().optional(),
  });

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get tenant ID from session based on user role
    let tenantId: string;
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      // For sales users, use their associated tenant's ID
      tenantId = session.user.tenantId;
    } else if (session.user.tenantId) {
      // For tenant users with tenantId
      tenantId = session.user.tenantId;
    } else {
      // Fallback to user ID
      tenantId = session.user.id;
    }
    console.log('POST /api/inventory/transfer - Using tenant ID:', tenantId, 'for user role:', session.user.role);

    // Parse and validate request body
    const body = await request.json();
    const validationResult = branchTransferSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: "Validation failed",
          details: validationResult.error.format(),
        },
        { status: 400 }
      );
    }

    const transferData = validationResult.data;

    // Verify branch exists and belongs to the tenant
    const branch = await db
      .select()
      .from(branches)
      .where(
        and(
          eq(branches.id, transferData.branchId),
          eq(branches.tenantId, tenantId)
        )
      )
      .limit(1);

    if (branch.length === 0) {
      return NextResponse.json(
        { success: false, error: "Branch not found or access denied" },
        { status: 404 }
      );
    }

    // Check if product exists
    const product = await db
      .select()
      .from(products)
      .where(
        and(
          eq(products.id, transferData.productId),
          eq(products.tenantId, tenantId)
        )
      )
      .limit(1);

    if (product.length === 0) {
      return NextResponse.json(
        { success: false, error: "Product not found" },
        { status: 404 }
      );
    }

    // Check if branch has enough inventory
    const branchInventory = await db
      .select({
        quantity: sql<number>`SUM(CASE WHEN type = 'in' THEN quantity ELSE -quantity END)`,
      })
      .from(inventoryTransactions)
      .where(
        and(
          eq(inventoryTransactions.tenantId, tenantId),
          eq(inventoryTransactions.branchId, transferData.branchId),
          eq(inventoryTransactions.productId, transferData.productId)
        )
      );

    const availableQuantity = branchInventory[0]?.quantity || 0;

    if (availableQuantity < transferData.quantity) {
      return NextResponse.json(
        {
          success: false,
          error: "Insufficient inventory in branch",
          available: availableQuantity,
        },
        { status: 400 }
      );
    }

    // Create inventory transaction
    const transaction = await db
      .insert(inventoryTransactions)
      .values({
        id: uuidv4(),
        tenantId,
        branchId: transferData.branchId,
        productId: transferData.productId,
        quantity: transferData.quantity,
        warehouseQuantity: 0, // Default to 0 for transfers
        storeQuantity: transferData.quantity, // Assume all from store
        unitPrice: product[0].costPrice,
        type: "transfer",
        date: new Date(),
        notes: transferData.notes || 'Inventory transfer',
        performedBy: session.user.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return NextResponse.json({
      success: true,
      message: "Inventory transferred successfully",
      transaction: transaction[0],
    });
  } catch (error) {
    console.error("Error transferring inventory:", error);
    return NextResponse.json(
      { success: false, error: "Failed to transfer inventory" },
      { status: 500 }
    );
  }
}
