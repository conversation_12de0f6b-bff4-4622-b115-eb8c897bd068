CREATE TABLE `payment_methods` (
	`id` text PRIMARY KEY NOT NULL,
	`tenant_id` text NOT NULL,
	`name` text NOT NULL,
	`code` text NOT NULL,
	`description` text,
	`is_active` integer DEFAULT true,
	`is_default` integer DEFAULT false,
	`created_at` integer,
	`updated_at` integer,
	FOREIGN KEY (`tenant_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
ALTER TABLE `dealer_payments` ADD `payment_method_id` text REFERENCES payment_methods(id);--> statement-breakpoint
ALTER TABLE `dealer_purchases` ADD `payment_method_id` text REFERENCES payment_methods(id);--> statement-breakpoint
ALTER TABLE `expenses` ADD `payment_method_id` text REFERENCES payment_methods(id);--> statement-breakpoint
ALTER TABLE `orders` ADD `payment_method_id` text REFERENCES payment_methods(id);--> statement-breakpoint
ALTER TABLE `payments` ADD `payment_method_id` text REFERENCES payment_methods(id);