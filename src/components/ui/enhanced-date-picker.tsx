"use client"

import * as React from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover"

export interface EnhancedDatePickerProps {
    value?: Date | null
    onChange?: (date: Date | null) => void
    onSelect?: (date: Date | null) => void
    placeholder?: string
    selected?: Date | null
    id?: string
    className?: string
    disabled?: boolean
}

export function EnhancedDatePicker({
    value,
    onChange,
    onSelect,
    placeholder = "Pick a date",
    selected,
    id,
    className,
    disabled = false,
}: EnhancedDatePickerProps) {
    const [date, setDate] = React.useState<Date | null>(selected || value || null)

    // Handle the change from parent component
    React.useEffect(() => {
        setDate(selected || value || null)
    }, [selected, value])

    // Handle the internal change
    const handleSelect = (newDate: Date | undefined) => {
        // Convert undefined to null for internal state consistency
        const dateValue = newDate === undefined ? null : newDate;
        setDate(dateValue)

        if (onSelect) {
            onSelect(dateValue)
        }

        if (onChange) {
            onChange(dateValue)
        }
    }

    return (
        <Popover>
            <PopoverTrigger asChild>
                <Button
                    id={id}
                    variant={"outline"}
                    className={cn(
                        "w-full h-9 px-3 py-2 justify-start text-left font-normal bg-white border-input",
                        "hover:bg-gray-50 focus:ring-2 focus:ring-blue-200 transition-all duration-200",
                        !date && "text-muted-foreground",
                        className
                    )}
                    disabled={disabled}
                >
                    <CalendarIcon className="mr-2 h-4 w-4 text-gray-500" />
                    {date ? (
                        <span className="text-gray-700">{format(date, "MMMM d, yyyy")}</span>
                    ) : (
                        <span className="text-gray-400">{placeholder}</span>
                    )}
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0 bg-white shadow-xl rounded-lg border border-gray-200" align="start">
                <Calendar
                    mode="single"
                    selected={date || undefined}
                    onSelect={handleSelect}
                    initialFocus
                    required={false}
                    className="rounded-md shadow-sm"
                />
            </PopoverContent>
        </Popover>
    )
}
