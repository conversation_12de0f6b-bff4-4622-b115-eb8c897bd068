/**
 * Dictionary mapping numbers to Bengali words
 */
export const banglaNumbers: { [key: number]: string } = {
  0: 'শূন্য',
  1: 'এক',
  2: 'দুই',
  3: 'তিন',
  4: 'চার',
  5: 'পাঁচ',
  6: 'ছয়',
  7: 'সাত',
  8: 'আট',
  9: 'নয়',
  10: 'দশ',
  20: 'বিশ',
  30: 'ত্রিশ',
  40: 'চল্লিশ',
  50: 'পঞ্চাশ',
  60: 'ষাট',
  70: 'সত্তর',
  80: 'আশি',
  90: 'নব্বই',
  100: 'এক শত'
};

/**
 * Safely converts a number to Bengali words
 * @param num - The number to convert
 * @returns Bengali words representation of the number
 */
export function numberToBanglaWords(num: number): string {
  try {
    // Handle invalid inputs
    if (num === undefined || num === null || isNaN(num)) {
      return '';
    }

    // Round to 2 decimal places to avoid floating point issues
    num = Math.round(num * 100) / 100;

    // Handle zero
    if (num === 0) return banglaNumbers[0];

    let result: string = '';

    // Handle negative numbers
    if (num < 0) {
      result = 'ঋণাত্মক ';
      num = Math.abs(num);
    }

    // Extract whole and decimal parts
    const wholePart = Math.floor(num);
    const decimalPart = Math.round((num - wholePart) * 100);

    // Process whole part
    if (wholePart > 0) {
      result += processWholePart(wholePart);
    }

    // Process decimal part if exists
    if (decimalPart > 0) {
      result += ' দশমিক ' + processDecimalPart(decimalPart);
    }

    return result.trim();
  } catch (error) {
    console.error('Error in numberToBanglaWords:', error);
    return '';
  }
}

/**
 * Process the whole part of a number
 * @param num - The whole number to process
 * @returns Bengali words for the whole number
 */
function processWholePart(num: number): string {
  let result: string = '';

  if (num >= 10000000) { // কোটি (10 million)
    const koti: number = Math.floor(num / 10000000);
    result += processWholePart(koti) + ' কোটি ';
    num = num % 10000000;
  }

  if (num >= 100000) { // লক্ষ (100 thousand)
    const lokkho: number = Math.floor(num / 100000);
    result += processWholePart(lokkho) + ' লক্ষ ';
    num = num % 100000;
  }

  if (num >= 1000) { // হাজার (thousand)
    const hajar: number = Math.floor(num / 1000);
    result += processWholePart(hajar) + ' হাজার ';
    num = num % 1000;
  }

  if (num >= 100) { // শত (hundred)
    const shoto: number = Math.floor(num / 100);
    result += processWholePart(shoto) + ' শত ';
    num = num % 100;
  }

  if (num > 0) {
    if (num in banglaNumbers) {
      result += banglaNumbers[num];
    } else {
      const tens: number = Math.floor(num / 10) * 10;
      const ones: number = num % 10;

      // Handle compound numbers (11-99)
      if (num > 10 && num < 100) {
        // Numbers 11-19
        if (tens === 10) {
          if (ones === 1) result += 'এগারো';
          else if (ones === 2) result += 'বারো';
          else if (ones === 3) result += 'তেরো';
          else if (ones === 4) result += 'চৌদ্দ';
          else if (ones === 5) result += 'পনেরো';
          else if (ones === 6) result += 'ষোল';
          else if (ones === 7) result += 'সতেরো';
          else if (ones === 8) result += 'আঠারো';
          else if (ones === 9) result += 'উনিশ';
        }
        // Numbers 21-29
        else if (tens === 20) {
          if (ones === 1) result += 'একুশ';
          else if (ones === 2) result += 'বাইশ';
          else if (ones === 3) result += 'তেইশ';
          else if (ones === 4) result += 'চব্বিশ';
          else if (ones === 5) result += 'পঁচিশ';
          else if (ones === 6) result += 'ছাব্বিশ';
          else if (ones === 7) result += 'সাতাশ';
          else if (ones === 8) result += 'আটাশ';
          else if (ones === 9) result += 'ঊনত্রিশ';
        }
        // Numbers 31-39
        else if (tens === 30) {
          if (ones === 1) result += 'একত্রিশ';
          else if (ones === 2) result += 'বত্রিশ';
          else if (ones === 3) result += 'তেত্রিশ';
          else if (ones === 4) result += 'চৌত্রিশ';
          else if (ones === 5) result += 'পঁয়ত্রিশ';
          else if (ones === 6) result += 'ছত্রিশ';
          else if (ones === 7) result += 'সাঁইত্রিশ';
          else if (ones === 8) result += 'আটত্রিশ';
          else if (ones === 9) result += 'ঊনচল্লিশ';
        }
        // Numbers 41-49
        else if (tens === 40) {
          if (ones === 1) result += 'একচল্লিশ';
          else if (ones === 2) result += 'বিয়াল্লিশ';
          else if (ones === 3) result += 'তেতাল্লিশ';
          else if (ones === 4) result += 'চুয়াল্লিশ';
          else if (ones === 5) result += 'পঁয়তাল্লিশ';
          else if (ones === 6) result += 'ছেচল্লিশ';
          else if (ones === 7) result += 'সাতচল্লিশ';
          else if (ones === 8) result += 'আটচল্লিশ';
          else if (ones === 9) result += 'ঊনপঞ্চাশ';
        }
        // Numbers 51-59
        else if (tens === 50) {
          if (ones === 1) result += 'একান্ন';
          else if (ones === 2) result += 'বায়ান্ন';
          else if (ones === 3) result += 'তিপ্পান্ন';
          else if (ones === 4) result += 'চুয়ান্ন';
          else if (ones === 5) result += 'পঞ্চান্ন';
          else if (ones === 6) result += 'ছাপ্পান্ন';
          else if (ones === 7) result += 'সাতান্ন';
          else if (ones === 8) result += 'আটান্ন';
          else if (ones === 9) result += 'ঊনষাট';
        }
        // Numbers 61-69
        else if (tens === 60) {
          if (ones === 1) result += 'একষট্টি';
          else if (ones === 2) result += 'বাষট্টি';
          else if (ones === 3) result += 'তেষট্টি';
          else if (ones === 4) result += 'চৌষট্টি';
          else if (ones === 5) result += 'পঁয়ষট্টি';
          else if (ones === 6) result += 'ছেষট্টি';
          else if (ones === 7) result += 'সাতষট্টি';
          else if (ones === 8) result += 'আটষট্টি';
          else if (ones === 9) result += 'ঊনসত্তর';
        }
        // Numbers 71-79
        else if (tens === 70) {
          if (ones === 1) result += 'একাত্তর';
          else if (ones === 2) result += 'বাহাত্তর';
          else if (ones === 3) result += 'তিয়াত্তর';
          else if (ones === 4) result += 'চুয়াত্তর';
          else if (ones === 5) result += 'পঁচাত্তর';
          else if (ones === 6) result += 'ছিয়াত্তর';
          else if (ones === 7) result += 'সাতাত্তর';
          else if (ones === 8) result += 'আটাত্তর';
          else if (ones === 9) result += 'ঊনআশি';
        }
        // Numbers 81-89
        else if (tens === 80) {
          if (ones === 1) result += 'একাশি';
          else if (ones === 2) result += 'বিরাশি';
          else if (ones === 3) result += 'তিরাশি';
          else if (ones === 4) result += 'চুরাশি';
          else if (ones === 5) result += 'পঁচাশি';
          else if (ones === 6) result += 'ছিয়াশি';
          else if (ones === 7) result += 'সাতাশি';
          else if (ones === 8) result += 'আটাশি';
          else if (ones === 9) result += 'ঊননব্বই';
        }
        // Numbers 91-99
        else if (tens === 90) {
          if (ones === 1) result += 'একানব্বই';
          else if (ones === 2) result += 'বিরানব্বই';
          else if (ones === 3) result += 'তিরানব্বই';
          else if (ones === 4) result += 'চুরানব্বই';
          else if (ones === 5) result += 'পঁচানব্বই';
          else if (ones === 6) result += 'ছিয়ানব্বই';
          else if (ones === 7) result += 'সাতানব্বই';
          else if (ones === 8) result += 'আটানব্বই';
          else if (ones === 9) result += 'নিরানব্বই';
        }
        // If no special case matched, fall back to the default format
        else {
          result += banglaNumbers[tens] + ' ' + banglaNumbers[ones];
        }
      } else {
        // For other numbers, use the default format
        result += banglaNumbers[tens] + ' ' + banglaNumbers[ones];
      }
    }
  }

  return result.trim();
}

/**
 * Process the decimal part of a number
 * @param num - The decimal part to process (as a whole number)
 * @returns Bengali words for the decimal
 */
function processDecimalPart(num: number): string {
  if (num === 0) return '';

  // For decimal parts like .01, .02, etc.
  if (num < 10) {
    return banglaNumbers[0] + ' ' + banglaNumbers[num];
  }

  // For other decimal parts
  return processWholePart(num);
}

/**
 * Converts a number to Bengali words and adds "টাকা মাত্র" suffix
 * @param amount - The amount to convert
 * @returns Bengali words with currency suffix
 */
export function amountToBanglaWords(amount: number | string): string {
  try {
    const numValue: number = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (isNaN(numValue)) return '';

    return `${numberToBanglaWords(numValue)} টাকা মাত্র`;
  } catch (error) {
    console.error('Error in amountToBanglaWords:', error);
    return '';
  }
}
