import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import {
  orders,
  orderItems as orderItemsTable,
  inventoryTransactions,
  customers,
  payments
} from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { auth } from "~/auth";
import { revalidatePath } from "next/cache";
import { v4 as uuidv4 } from "uuid";
import { withMenuPermission } from "@/middleware/menuPermissions";

async function putHandler(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
  permissionCheck: any
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Check if user has edit permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      const menuPermission = permissionCheck.permissions.find(
        (p: any) => p.menu.path === '/dashboard/sales'
      );

      if (!menuPermission?.canEdit) {
        return NextResponse.json(
          { success: false, error: "You do not have permission to edit sales" },
          { status: 403 }
        );
      }
    }

    const tenantId = session.user.tenantId || session.user.id;
    const { id } = await params;
    const orderId = id;

    // Get the order data from the request
    const orderData = await request.json();

    // Log the entire request body for debugging
    console.log("Edit order request body:", JSON.stringify(orderData, null, 2));

    // Verify the order exists and belongs to this tenant
    const existingOrder = await db.query.orders.findFirst({
      where: and(eq(orders.id, orderId), eq(orders.tenantId, tenantId)),
      with: {
        items: true,
      },
    });

    if (!existingOrder) {
      return NextResponse.json({ message: "Order not found" }, { status: 404 });
    }

    // Log the existing order for debugging
    console.log("Existing order:", JSON.stringify({
      id: existingOrder.id,
      status: existingOrder.status,
      itemsCount: existingOrder.items.length,
      items: existingOrder.items.map(item => ({
        id: item.id,
        productId: item.productId,
        quantity: item.quantity
      }))
    }, null, 2));

    // 1. Update the order details
    try {
      // Log the order status for debugging
      console.log(`Updating order ${orderId} with status: ${orderData.status}`);

      await db
        .update(orders)
        .set({
          memoNo: orderData.memoNo,
          date: new Date(orderData.date),
          customerId: orderData.customerId || null,
          totalAmount: orderData.totalAmount || 0,
          paidAmount: orderData.paidAmount || 0,
          dueAmount: orderData.dueAmount || 0,
          paymentMethod: orderData.paymentMethod || "cash",
          previousDue: orderData.previousDue || 0,
          extCommission: orderData.extCommission || 0,
          extCommissionType: orderData.extCommissionType || "percentage",
          notes:
            orderData.extCommission > 0
              ? `Commission: ${orderData.extCommission}${orderData.extCommissionType === "percentage" ? "%" : " fixed"} ${orderData.remarks || ""}`
              : orderData.remarks || null,
          status: orderData.status || existingOrder.status, // Preserve the order status
          paymentStatus: orderData.paymentStatus || (orderData.paidAmount >= orderData.totalAmount || orderData.dueAmount <= 0.01 ? "paid" : (orderData.paidAmount > 0 ? "partial" : "unpaid")),
          updatedAt: new Date(),
        })
        .where(eq(orders.id, orderId));
    } catch (error: unknown) {
      console.error("Error updating order:", error);
      throw new Error("Failed to update order details: " + (error as Error).message);
    }

    // 2. Delete existing order items (we'll recreate them from scratch)
    await db
      .delete(orderItemsTable)
      .where(eq(orderItemsTable.orderId, orderId));

    // 3. Insert updated order items
    if (orderData.items && orderData.items.length > 0) {
      try {
        console.log(`Inserting ${orderData.items.length} items for order ${orderId} with status ${orderData.status}`);

        // For draft orders, we need to handle items with quantity 0
        const orderItemsToInsert = orderData.items.map((item: any) => {
          // For draft orders, ensure we have valid values even if they're 0
          const quantity = orderData.status === "draft" ? (item.quantity || 0) : item.quantity;
          const unitPrice = orderData.status === "draft" ? (item.unitPrice || 0) : item.unitPrice;
          const discountPercentage = item.discountPercentage || 0;

          // Calculate discount amount safely
          const discountAmount =
            (unitPrice * quantity * discountPercentage) / 100;

          // Calculate total safely
          const total = orderData.status === "draft" ? (item.total || 0) : item.total;

          return {
            id: uuidv4(), // Generate new ID for each item
            orderId,
            productId: item.productId,
            quantity,
            unitPrice,
            discountPercentage,
            discountAmount,
            total,
            createdAt: new Date(),
            updatedAt: new Date(),
          };
        });

        console.log("Inserting order items:", JSON.stringify(orderItemsToInsert, null, 2));

        try {
          await db.insert(orderItemsTable).values(orderItemsToInsert);
          console.log(`Successfully inserted ${orderItemsToInsert.length} items for order ${orderId}`);
        } catch (insertError) {
          console.error("Error during item insertion:", insertError);
          throw insertError;
        }
      } catch (error) {
        console.error("Error inserting order items:", error);
        throw new Error("Failed to update order items: " + (error as Error).message);
      }
    } else {
      console.log(`No items to insert for order ${orderId} with status ${orderData.status}`);
    }

    // 4. Handle inventory transactions
    // First, reverse the previous inventory transactions for this order, but only for non-draft orders
    if (orderData.status !== "draft" && existingOrder.status !== "draft") {
      try {
        console.log(`Finding inventory transactions for order ID: ${orderId}, memo: ${existingOrder.memoNo}`);

        // Find the transactions first, then delete them
        const previousTransactions = await db.query.inventoryTransactions.findMany({
          where: and(
            eq(inventoryTransactions.tenantId, tenantId),
            eq(inventoryTransactions.type, "out")
          ),
        });

        console.log(`Found ${previousTransactions.length} total 'out' transactions`);

        // Find transactions for this order by checking notes
        // Try different patterns that might be in the notes
        const transactionsToDelete = previousTransactions.filter(
          (transaction) => {
            const noteMatches =
              (transaction.notes && transaction.notes.includes(`Memo #${existingOrder.memoNo}`)) ||
              (transaction.notes && transaction.notes.includes(`Sale - Memo #${existingOrder.memoNo}`)) ||
              (transaction.notes && transaction.notes.includes(`Sale: ${existingOrder.memoNo}`));

            return noteMatches;
          }
        );

        console.log(`Found ${transactionsToDelete.length} transactions to delete for this order`);

        // Delete them one by one
        for (const transaction of transactionsToDelete) {
          console.log(`Deleting transaction ID: ${transaction.id}, notes: ${transaction.notes}`);
          await db
            .delete(inventoryTransactions)
            .where(eq(inventoryTransactions.id, transaction.id));
        }
      } catch (error) {
        console.error("Error deleting previous inventory transactions:", error);
        throw new Error(
          "Failed to delete previous inventory transactions: " + (error as Error).message
        );
      }
    } else {
      console.log(`Skipping inventory transaction deletion for draft order: ${orderId}`);
    }

    // Create new inventory transactions for updated items, but only for non-draft orders
    if (orderData.items && orderData.items.length > 0 && orderData.status !== "draft") {
      try {
        // Get the branch ID from the order data or existing order
        const branchId = orderData.branchId || existingOrder.branchId;

        if (!branchId) {
          throw new Error("Branch ID is required for inventory transactions");
        }

        console.log("Using branch ID for inventory transactions:", branchId);

        // Create transactions one by one
        for (const item of orderData.items) {
          await db.insert(inventoryTransactions).values({
            id: uuidv4(),
            tenantId,
            branchId: branchId,
            productId: item.productId,
            quantity: item.quantity,
            storeQuantity: item.quantity, // Set store quantity to match quantity
            warehouseQuantity: 0, // Set warehouse quantity to 0
            unitPrice: item.unitPrice,
            type: "out",
            date: new Date(),
            notes: `Sale - Memo #${orderData.memoNo}`,
            performedBy: session.user.id || null,
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }
      } catch (error) {
        console.error("Error inserting inventory transactions:", error);
        throw new Error(
          "Failed to update inventory transactions: " + (error as Error).message
        );
      }
    } else if (orderData.status === "draft") {
      console.log("Skipping inventory transactions for draft order");
    }

    // 5. Update customer balance if necessary
    if (orderData.customerId) {
      // Update customer balance based on the changes in this order
      const customer = await db.query.customers.findFirst({
        where: and(
          eq(customers.id, orderData.customerId),
          eq(customers.tenantId, tenantId)
        ),
      });

      if (customer) {
        // Handle payment for the current order
        // Use effectivePaidAmount if provided, otherwise use paidAmount
        const effectiveAmount = orderData.effectivePaidAmount !== undefined ? orderData.effectivePaidAmount : orderData.paidAmount;

        if (effectiveAmount > 0) {
          // Check if there's an existing payment record for this order
          const existingPayment = await db.query.payments.findFirst({
            where: (payments, { eq, and }) => and(
              eq(payments.orderId, orderId),
              eq(payments.paymentType, "sale")
            )
          });

          if (existingPayment) {
            // Update the existing payment record
            await db.update(payments)
              .set({
                amount: effectiveAmount,
                paymentMethod: orderData.paymentMethod || existingPayment.paymentMethod,
                paymentReference: orderData.paymentNo || existingPayment.paymentReference,
                date: new Date(orderData.date),
                updatedAt: new Date()
              })
              .where(eq(payments.id, existingPayment.id));

            console.log(`Updated existing payment record: id=${existingPayment.id}, newAmount=${effectiveAmount}`);
          } else {
            // Create a new payment record
            await db.insert(payments).values({
              id: uuidv4(),
              tenantId: tenantId,
              branchId: orderData.branchId,
              customerId: orderData.customerId,
              orderId: orderId,
              amount: effectiveAmount,
              paymentMethod: orderData.paymentMethod || "cash",
              paymentReference: orderData.paymentNo || null,
              paymentType: "sale",
              notes: `Payment for order #${orderData.memoNo} (edited)`,
              performedBy: session.user.id,
              date: new Date(orderData.date),
              createdAt: new Date(),
              updatedAt: new Date(),
            });

            console.log(`Created new payment record for edited order: amount=${effectiveAmount}`);
          }
        }

        // Handle previous due payment if it's being made
        if (orderData.previousDuePaid && orderData.previousDuePaymentAmount > 0) {
          console.log(`Processing previous due payment in edit: amount=${orderData.previousDuePaymentAmount}`);

          // Generate a unique ID for the payment
          const paymentId = uuidv4();

          // Create a separate payment record for the previous due payment
          await db.insert(payments).values({
            id: paymentId,
            tenantId: tenantId,
            branchId: orderData.branchId,
            customerId: orderData.customerId,
            orderId: orderId, // Link to current order for reference
            amount: orderData.previousDuePaymentAmount,
            paymentMethod: orderData.paymentMethod || "cash",
            paymentReference: orderData.paymentNo || null,
            paymentType: "due_payment",
            notes: `Previous due payment with order #${orderData.memoNo} (edited)`,
            performedBy: session.user.id,
            date: new Date(orderData.date),
            createdAt: new Date(),
            updatedAt: new Date(),
          });

          console.log(`Previous due payment record created in edit: amount=${orderData.previousDuePaymentAmount}, paymentId=${paymentId}`);

          // Update customer balance for the previous due payment
          await db
            .update(customers)
            .set({
              currentBalance: (customer.currentBalance || 0) - orderData.previousDuePaymentAmount,
              updatedAt: new Date(),
            })
            .where(eq(customers.id, orderData.customerId));

          console.log(`Customer balance updated for previous due payment in edit: newBalance=${(customer.currentBalance || 0) - orderData.previousDuePaymentAmount}`);
        }

        // Calculate the difference in due amount for the order itself
        const previousDueAmount = existingOrder.dueAmount || 0;
        const newDueAmount = orderData.dueAmount || 0;
        const dueAmountDifference = newDueAmount - previousDueAmount;

        // Only update if there's a change in due
        if (dueAmountDifference !== 0) {
          // Get the latest customer balance after any previous due payment
          const updatedCustomer = await db.query.customers.findFirst({
            where: (customers, { eq }) => eq(customers.id, orderData.customerId),
          });

          if (updatedCustomer) {
            // Update customer balance
            await db
              .update(customers)
              .set({
                currentBalance: (updatedCustomer.currentBalance || 0) + dueAmountDifference,
                updatedAt: new Date(),
              })
              .where(eq(customers.id, orderData.customerId));

            console.log(`Customer balance updated for order due change: dueAmountDifference=${dueAmountDifference}, newBalance=${(updatedCustomer.currentBalance || 0) + dueAmountDifference}`);
          }
        }
      }
    }

    // Revalidate the sales and order pages to reflect the changes
    revalidatePath("/dashboard/sales");
    revalidatePath(`/dashboard/sales/${orderId}`);

    return NextResponse.json(
      {
        message: "Order updated successfully",
        id: orderId,
      },
      { status: 200 }
    );
  } catch (error: any) {
    console.error("Error updating order:", error);
    return NextResponse.json(
      { message: error.message || "Failed to update order" },
      { status: 500 }
    );
  }
}

// Export the route handlers with permission middleware
export const PUT = withMenuPermission(putHandler, '/dashboard/sales');
