const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Check if @next/bundle-analyzer is installed
try {
  require.resolve('@next/bundle-analyzer');
  console.log('✅ @next/bundle-analyzer is already installed');
} catch (e) {
  console.log('⚠️ Installing @next/bundle-analyzer...');
  execSync('npm install --save-dev @next/bundle-analyzer', { stdio: 'inherit' });
}

// Create a temporary next.config.js with bundle analyzer
const configPath = path.join(process.cwd(), 'next.config.ts');
const backupPath = path.join(process.cwd(), 'next.config.backup.ts');

// Backup the original config
fs.copyFileSync(configPath, backupPath);
console.log('✅ Backed up next.config.ts to next.config.backup.ts');

// Read the original config
const originalConfig = fs.readFileSync(configPath, 'utf8');

// Add bundle analyzer
const analyzerConfig = `
import { withBundleAnalyzer } from '@next/bundle-analyzer';
import type { NextConfig } from "next";

// Set the timezone to Asia/Dhaka
process.env.TZ = 'Asia/Dhaka';

const nextConfig: NextConfig = {
  /* config options here */
  env: {
    TZ: 'Asia/Dhaka',
  },
  // Enable image optimization
  images: {
    formats: ['image/avif', 'image/webp'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
  // Optimize performance
  poweredByHeader: false,
  reactStrictMode: true,
  // Optimize page loading
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn'],
    } : false,
  },
};

export default withBundleAnalyzer({
  enabled: true,
})(nextConfig);
`;

// Write the analyzer config
fs.writeFileSync(configPath, analyzerConfig);
console.log('✅ Added bundle analyzer to next.config.ts');

// Build the project
console.log('🔄 Building project with bundle analyzer...');
try {
  execSync('ANALYZE=true npm run build', { stdio: 'inherit' });
  console.log('✅ Build completed successfully');
} catch (e) {
  console.error('❌ Build failed:', e);
}

// Restore the original config
fs.copyFileSync(backupPath, configPath);
fs.unlinkSync(backupPath);
console.log('✅ Restored original next.config.ts');

console.log('\n🎉 Bundle analysis complete! Check the .next/analyze folder for results.');
console.log('📊 Open the HTML files in your browser to view the bundle analysis.');
