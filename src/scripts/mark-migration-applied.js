// This script manually marks the migration as applied in the database
// Run with: node src/scripts/mark-migration-applied.js

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@libsql/client');

async function markMigrationAsApplied() {
  // Create database client
  const client = createClient({
    url: process.env.TURSO_DATABASE_URL,
    authToken: process.env.TURSO_AUTH_TOKEN,
  });

  try {
    console.log('Checking if __drizzle_migrations table exists...');
    
    // Check if the __drizzle_migrations table exists
    const tableExists = await client.execute(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='__drizzle_migrations';
    `);
    
    if (tableExists.rows.length === 0) {
      console.log('Creating __drizzle_migrations table...');
      
      // Create the migrations table if it doesn't exist
      await client.execute(`
        CREATE TABLE __drizzle_migrations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          hash TEXT NOT NULL,
          created_at NUMERIC
        );
      `);
    }
    
    // Check if migration is already recorded
    const existingMigration = await client.execute({
      sql: `SELECT hash FROM __drizzle_migrations WHERE hash = ?;`,
      args: ['113a349d-86c7-43fd-b88a-cdfc3eaa953d']
    });
    
    if (existingMigration.rows.length === 0) {
      console.log('Marking migration as applied...');
      
      // Insert migration record
      await client.execute({
        sql: `INSERT INTO __drizzle_migrations (hash, created_at) VALUES (?, ?);`,
        args: ['113a349d-86c7-43fd-b88a-cdfc3eaa953d', Date.now()]
      });
      
      console.log('Migration marked as applied successfully!');
    } else {
      console.log('Migration is already marked as applied.');
    }
  } catch (error) {
    console.error('Error marking migration as applied:', error);
  } finally {
    // Close the client connection
    await client.close();
  }
}

// Run the function
markMigrationAsApplied();
