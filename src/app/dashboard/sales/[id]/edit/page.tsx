import { Metadata } from "next";
import Link from "next/link";
import { redirect, notFound } from "next/navigation";
import { auth } from "~/auth";
import { SaleForm } from "@/app/dashboard/sales/new/_components/sale-form";
import { db } from "@/lib/db";
import { products, customers, orders, orderItems as orderItemsTable, inventoryTransactions, branches } from "@/db/schema";
import { eq, and, sum } from "drizzle-orm";

export const metadata: Metadata = {
    title: "Edit Sale",
    description: "Edit an existing sale",
};

export default async function EditSalePage({ params }: { params: Promise<{ id: string }> }) {
    const session = await auth();
    if (!session?.user) {
        redirect("/api/auth/signin");
    }

    const tenantId = session.user.id;
    const { id } = await params;
    const orderId = id;

    // Get the existing order
    const existingOrder = await db.query.orders.findFirst({
        where: and(
            eq(orders.id, orderId),
            eq(orders.tenantId, tenantId)
        ),
        with: {
            customer: true,
            items: {
                with: {
                    product: true,
                },
            },
        },
    });

    // If the order doesn't exist or doesn't belong to this tenant, return 404
    if (!existingOrder) {
        notFound();
    }

    // Get all products for this tenant
    const allProducts = await db.query.products.findMany({
        where: (products, { eq, and }) =>
            and(
                eq(products.tenantId, tenantId)
            ),
        with: {
            category: true,
            vendor: true,
        },
    });

    // Get all branches for this tenant
    const allBranches = await db.query.branches.findMany({
        where: eq(branches.tenantId, tenantId),
        orderBy: (branches, { asc }) => [asc(branches.name)]
    });

    // Get inventory data for products by branch
    const inventoryData = new Map();

    // Get "in" transaction quantities by product and branch
    const inTransactions = await db
        .select({
            productId: inventoryTransactions.productId,
            branchId: inventoryTransactions.branchId,
            totalIn: sum(inventoryTransactions.storeQuantity), // Only use store quantity for sales
        })
        .from(inventoryTransactions)
        .where(
            and(
                eq(inventoryTransactions.type, "in"),
                eq(inventoryTransactions.tenantId, tenantId)
            )
        )
        .groupBy(inventoryTransactions.productId, inventoryTransactions.branchId);

    // Get "out" transaction quantities by product and branch
    const outTransactions = await db
        .select({
            productId: inventoryTransactions.productId,
            branchId: inventoryTransactions.branchId,
            totalOut: sum(inventoryTransactions.storeQuantity), // Only use store quantity for sales
        })
        .from(inventoryTransactions)
        .where(
            and(
                eq(inventoryTransactions.type, "out"),
                eq(inventoryTransactions.tenantId, tenantId)
            )
        )
        .groupBy(inventoryTransactions.productId, inventoryTransactions.branchId);

    // Create maps for looking up stock by product and branch
    const inMap = new Map();
    inTransactions.forEach(t => {
        const key = `${t.productId}-${t.branchId}`;
        inMap.set(key, Number(t.totalIn || 0));
    });

    const outMap = new Map();
    outTransactions.forEach(t => {
        const key = `${t.productId}-${t.branchId}`;
        outMap.set(key, Number(t.totalOut || 0));
    });

    // Calculate stock for each product by branch
    allProducts.forEach(product => {
        allBranches.forEach(branch => {
            const key = `${product.id}-${branch.id}`;
            const inStock = inMap.get(key) || 0;
            const outStock = outMap.get(key) || 0;
            const currentStock = inStock - outStock;
            inventoryData.set(key, currentStock);
        });
    });

    // Add stock information to products
    const productsWithStock = allProducts.map(product => ({
        ...product,
        stock: inventoryData.get(product.id) || 0
    }));

    // Get all customers for this tenant
    const allCustomers = await db.query.customers.findMany({
        where: (customers, { eq }) => eq(customers.tenantId, tenantId),
    });

    // Check if user has a branch assigned in their session
    const sessionBranchId = session.user.branchId || null;

    return (
        <div className="container py-10">
            <div className="flex items-center justify-between mb-6">
                <div>
                    <h1 className="text-3xl font-bold">Edit Sale</h1>
                    <p className="text-muted-foreground">Update an existing sales transaction</p>
                </div>
                <Link href="/dashboard/sales" className="text-sm bg-sky-100 p-4 text-blue-600 hover:underline">
                    Back to Sales
                </Link>
            </div>
            <SaleForm
                products={productsWithStock}
                customers={allCustomers}
                branches={allBranches}
                initialBranchId={existingOrder.branchId || ""}
                initialMemoNo={existingOrder.memoNo || ""}
                tenantId={tenantId}
                inventoryData={inventoryData}
                sessionBranchId={sessionBranchId}
                existingOrder={existingOrder}
                isEditMode={true}
            />
        </div>
    );
}