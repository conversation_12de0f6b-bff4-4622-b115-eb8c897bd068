'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Pencil, Trash2, Plus, Search, FileText } from 'lucide-react';

interface ExpenseCategory {
  id: string;
  name: string;
}

interface Branch {
  id: string;
  name: string;
  isActive: boolean;
}

interface User {
  id: string;
  fullName: string;
}

interface Expense {
  id: string;
  categoryId: string;
  branchId: string;
  amount: number;
  date: string;
  description: string | null;
  paymentMethod: 'cash' | 'card' | 'bank_transfer';
  reference: string | null;
  performedBy: string;
  createdAt: string;
  category: ExpenseCategory;
  branch: Branch;
  performer: User;
}

export default function ExpensesPage() {
  const router = useRouter();
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [filteredExpenses, setFilteredExpenses] = useState<Expense[]>([]);
  const [categories, setCategories] = useState<ExpenseCategory[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [branchFilter, setBranchFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [selectedCategoryName, setSelectedCategoryName] = useState<string>('All Categories');
  const [selectedBranchName, setSelectedBranchName] = useState<string>('All Branches');
  const [selectedDateFilterName, setSelectedDateFilterName] = useState<string>('All Time');
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentExpense, setCurrentExpense] = useState<Expense | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchExpenses();
    fetchCategories();
    fetchBranches();
  }, []);

  useEffect(() => {
    filterExpenses();
  }, [expenses, searchTerm, categoryFilter, branchFilter, dateFilter]);

  const fetchExpenses = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/expenses');

      if (!response.ok) {
        throw new Error(`Failed to fetch expenses: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setExpenses(data.expenses || []);
      } else {
        setError(data.error || 'Failed to load expenses');
      }
    } catch (error) {
      console.error('Error fetching expenses:', error);
      setError('An error occurred while fetching expenses');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/expense-categories');

      if (response.ok) {
        const data = await response.json();

        if (data.success) {
          setCategories(data.categories || []);
        }
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchBranches = async () => {
    try {
      const response = await fetch('/api/branches');

      if (response.ok) {
        const data = await response.json();

        if (data.success) {
          // Filter to only include active branches
          const activeBranches = data.branches.filter((branch: Branch) => branch.isActive);
          setBranches(activeBranches || []);
        }
      }
    } catch (error) {
      console.error('Error fetching branches:', error);
    }
  };

  const filterExpenses = () => {
    let filtered = [...expenses];

    // Filter by search term (description or reference)
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(expense =>
        (expense.description && expense.description.toLowerCase().includes(term)) ||
        (expense.reference && expense.reference.toLowerCase().includes(term)) ||
        expense.category.name.toLowerCase().includes(term)
      );
    }

    // Filter by category
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(expense => expense.categoryId === categoryFilter);
    }

    // Filter by branch
    if (branchFilter !== 'all') {
      filtered = filtered.filter(expense => expense.branchId === branchFilter);
    }

    // Filter by date
    if (dateFilter !== 'all') {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      if (dateFilter === 'today') {
        filtered = filtered.filter(expense => {
          const expenseDate = new Date(expense.date);
          return expenseDate >= today && expenseDate < new Date(today.getTime() + 86400000);
        });
      } else if (dateFilter === 'week') {
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());

        filtered = filtered.filter(expense => {
          const expenseDate = new Date(expense.date);
          return expenseDate >= weekStart;
        });
      } else if (dateFilter === 'month') {
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);

        filtered = filtered.filter(expense => {
          const expenseDate = new Date(expense.date);
          return expenseDate >= monthStart;
        });
      }
    }

    setFilteredExpenses(filtered);
  };

  const handleDeleteClick = (expense: Expense) => {
    setCurrentExpense(expense);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteExpense = async () => {
    if (!currentExpense) return;

    try {
      setIsSubmitting(true);
      const response = await fetch(`/api/expenses?id=${currentExpense.id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Expense deleted successfully');
        setIsDeleteDialogOpen(false);
        fetchExpenses();
      } else {
        toast.error(data.error || 'Failed to delete expense');
      }
    } catch (error) {
      console.error('Error deleting expense:', error);
      toast.error('An error occurred while deleting the expense');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number) => {
    return '৳ ' + new Intl.NumberFormat('en-US', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  // Payment method badge is now directly implemented in the table cell

  const calculateTotal = () => {
    return filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0);
  };

  return (
    <div className="container mx-auto px-2 py-1">
      <div className="flex justify-between items-center mb-2">
        <h1 className="text-lg font-bold">Expenses</h1>
        <div className="flex space-x-1">
          <Button
            onClick={() => router.push('/dashboard/expenses/categories')}
            variant="outline"
            size="default"
            className="cursor-pointer font-medium"
          >
            <FileText className="mr-1 h-4 w-4" />Categories
          </Button>
          <Button
            onClick={() => router.push('/dashboard/expenses/new')}
            className="bg-blue-500 hover:bg-blue-600 text-white cursor-pointer font-medium"
            size="default"
          >
            <Plus className="mr-1 h-4 w-4" />Add New Expense
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border-l-2 border-red-500 text-red-700 px-2 py-1 text-xs mb-2" role="alert">
          <span className="inline">{error}</span>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-wrap gap-2 mb-2 bg-white p-2 border border-gray-100 rounded">
        <div className="relative flex-1 min-w-[180px]">
          <Search className="absolute left-2 top-2 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search..."
            className="pl-8 h-8 text-xs"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="w-[150px]">
          <Select
            value={categoryFilter}
            onValueChange={(value) => {
              setCategoryFilter(value);
              if (value === 'all') {
                setSelectedCategoryName('All Categories');
              } else {
                const category = categories.find(cat => cat.id === value);
                if (category) {
                  setSelectedCategoryName(category.name);
                }
              }
            }}
          >
            <SelectTrigger className="h-8 text-xs cursor-pointer border-gray-200">
              <SelectValue placeholder="Category">
                {selectedCategoryName}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all" className="text-xs cursor-pointer">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.id} className="text-xs cursor-pointer">
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="w-[150px]">
          <Select
            value={branchFilter}
            onValueChange={(value) => {
              setBranchFilter(value);
              if (value === 'all') {
                setSelectedBranchName('All Branches');
              } else {
                const branch = branches.find(b => b.id === value);
                if (branch) {
                  setSelectedBranchName(branch.name);
                }
              }
            }}
          >
            <SelectTrigger className="h-8 text-xs cursor-pointer border-gray-200">
              <SelectValue placeholder="Branch">
                {selectedBranchName}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all" className="text-xs cursor-pointer">All Branches</SelectItem>
              {branches.map((branch) => (
                <SelectItem key={branch.id} value={branch.id} className="text-xs cursor-pointer">
                  {branch.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="w-[150px]">
          <Select
            value={dateFilter}
            onValueChange={(value) => {
              setDateFilter(value);
              switch (value) {
                case 'all':
                  setSelectedDateFilterName('All Time');
                  break;
                case 'today':
                  setSelectedDateFilterName('Today');
                  break;
                case 'week':
                  setSelectedDateFilterName('This Week');
                  break;
                case 'month':
                  setSelectedDateFilterName('This Month');
                  break;
                default:
                  setSelectedDateFilterName('All Time');
              }
            }}
          >
            <SelectTrigger className="h-8 text-xs cursor-pointer border-gray-200">
              <SelectValue placeholder="Date">
                {selectedDateFilterName}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all" className="text-xs cursor-pointer">All Time</SelectItem>
              <SelectItem value="today" className="text-xs cursor-pointer">Today</SelectItem>
              <SelectItem value="week" className="text-xs cursor-pointer">This Week</SelectItem>
              <SelectItem value="month" className="text-xs cursor-pointer">This Month</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Summary Card */}
      <div className="flex items-center mb-2 bg-white p-2 border border-gray-100 rounded">
        <div className="text-xs text-gray-500 mr-2">Total Expenses (Filtered):</div>
        <div className="text-base font-bold">{formatCurrency(calculateTotal())}</div>
      </div>

      {/* Expenses Table */}
      <div className="border border-gray-100 rounded bg-white">
        <div className="flex justify-between items-center px-3 py-2 border-b border-gray-100">
          <div className="text-sm font-medium">Expenses</div>
          <div className="text-xs text-gray-500">
            {filteredExpenses.length} {filteredExpenses.length === 1 ? 'expense' : 'expenses'} found
          </div>
        </div>
        <div className="p-2">
          {loading ? (
            <div className="text-center py-2 text-sm flex items-center justify-center">
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-500 mr-2"></div>
              Loading...
            </div>
          ) : filteredExpenses.length === 0 ? (
            <div className="text-center py-2 text-sm text-gray-500">
              {expenses.length === 0
                ? "No expenses found. Add your first expense."
                : "No expenses match your filters."}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full text-xs border-collapse">
                <thead>
                  <tr className="border-b border-gray-100">
                    <th className="py-1 px-2 font-medium text-left">Date</th>
                    <th className="py-1 px-2 font-medium text-left">Category</th>
                    <th className="py-1 px-2 font-medium text-left">Branch</th>
                    <th className="py-1 px-2 font-medium text-left">Description</th>
                    <th className="py-1 px-2 font-medium text-left">Payment</th>
                    <th className="py-1 px-2 font-medium text-left">Ref</th>
                    <th className="py-1 px-2 font-medium text-right">Amount</th>
                    <th className="py-1 px-2 font-medium text-right">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredExpenses.map((expense) => (
                    <tr key={expense.id} className="border-b border-gray-50">
                      <td className="py-1 px-2">{formatDate(expense.date)}</td>
                      <td className="py-1 px-2 max-w-[120px] truncate" title={expense.category.name}>
                        {expense.category.name}
                      </td>
                      <td className="py-1 px-2 max-w-[120px] truncate" title={expense.branch.name}>
                        {expense.branch.name}
                      </td>
                      <td className="py-1 px-2 max-w-[150px] truncate" title={expense.description || '-'}>
                        {expense.description || '-'}
                      </td>
                      <td className="py-1 px-2">
                        <span className={`text-xs py-0.5 px-1.5 rounded-sm ${expense.paymentMethod === 'cash' ? 'bg-green-100 text-green-800' :
                          expense.paymentMethod === 'card' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
                          }`}>
                          {expense.paymentMethod === 'cash' ? 'Cash' :
                            expense.paymentMethod === 'card' ? 'Card' : 'Bank'}
                        </span>
                      </td>
                      <td className="py-1 px-2 max-w-[100px] truncate" title={expense.reference || '-'}>
                        {expense.reference || '-'}
                      </td>
                      <td className="py-1 px-2 text-right font-medium">{formatCurrency(expense.amount)}</td>
                      <td className="py-1 px-2 text-right">
                        <div className="flex justify-end space-x-1">
                          <button
                            onClick={() => router.push(`/dashboard/expenses/${expense.id}/edit`)}
                            className="h-6 w-6 flex items-center justify-center rounded border border-gray-200 text-blue-500 hover:text-blue-700 cursor-pointer"
                          >
                            <Pencil className="h-3 w-3" />
                          </button>
                          <button
                            className="h-6 w-6 flex items-center justify-center rounded border border-gray-200 text-red-500 hover:text-red-700 cursor-pointer"
                            onClick={() => handleDeleteClick(expense)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-sm p-3">
          <DialogHeader className="pb-2">
            <DialogTitle className="text-sm">Delete Expense</DialogTitle>
            <DialogDescription className="text-xs mt-1">
              Are you sure you want to delete this expense of {currentExpense ? formatCurrency(currentExpense.amount) : ''} for {currentExpense?.category?.name}?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="pt-2 space-x-2">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isSubmitting}
              className="h-8 text-xs cursor-pointer"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteExpense}
              disabled={isSubmitting}
              className="h-8 text-xs cursor-pointer"
            >
              {isSubmitting ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
