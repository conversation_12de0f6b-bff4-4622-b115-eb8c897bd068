import { NextRequest, NextResponse } from "next/server";
import { auth } from "~/auth";
import { ALL_MENUS } from "@/lib/menu-list";

// POST /api/debug/add-order-reports-menu - Check if order reports menu exists in file
export async function POST(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Check if the order reports menu exists in the file
    const orderReportsMenu = ALL_MENUS.find(menu => menu.path === '/dashboard/reports/orders');

    if (orderReportsMenu) {
      return NextResponse.json({
        success: true,
        message: "Order reports menu exists in the file-based menu definitions",
        menu: orderReportsMenu
      });
    } else {
      return NextResponse.json({
        success: false,
        message: "Order reports menu does not exist in the file-based menu definitions. Please add it to the ALL_MENUS array in menu-list.ts"
      });
    }
  } catch (error) {
    console.error("Error adding order reports menu:", error);
    return NextResponse.json(
      { success: false, error: "Failed to add order reports menu" },
      { status: 500 }
    );
  }
}
