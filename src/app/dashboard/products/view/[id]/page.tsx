'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { use } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  Card<PERSON><PERSON>er, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  AlertCircle, 
  ArrowLeft, 
  Edit, 
  Package, 
  Tag, 
  DollarSign, 
  Percent, 
  Info, 
  Calendar, 
  CheckCircle, 
  XCircle 
} from "lucide-react";
import { formatCurrency, formatDate } from "@/lib/utils";

interface Category {
  id: string;
  name: string;
}

interface Vendor {
  id: string;
  name: string;
}

interface Product {
  id: string;
  name: string;
  code: string | null;
  description: string | null;
  costPrice: number;
  sellingPrice: number;
  discount: number;
  unit: string;
  stock?: number;
  categoryId: string | null;
  category: Category | null;
  vendorId: string | null;
  vendor?: Vendor | null;
  royaltyType: 'none' | 'fixed' | 'percentage';
  royaltyValue: number;
  isActive: boolean;
  sku: string | null;
  weight: number | null;
  lastStockUpdate?: Date | null;
  createdAt: string;
  updatedAt: string;
}

export default function ViewProductPage({ params }: { params: any }) {
  const router = useRouter();
  const resolvedParams = use(params) as { id: string };
  const productId = resolvedParams.id;
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchProduct();
  }, [productId]);

  const fetchProduct = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/products?id=${productId}`);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Product not found');
        }
        throw new Error('Failed to fetch product');
      }

      const data = await response.json();

      if (data.success && data.product) {
        setProduct(data.product);
      } else {
        setError(data.error || 'Failed to load product');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching product');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const getRoyaltyTypeText = (type: 'none' | 'fixed' | 'percentage') => {
    switch (type) {
      case 'fixed':
        return 'Fixed Amount';
      case 'percentage':
        return 'Percentage';
      default:
        return 'None';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 flex justify-center items-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <AlertCircle className="h-8 w-8 text-blue-500 animate-pulse mb-4" />
          <p className="text-lg">Loading product information...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <Button
          variant="outline"
          onClick={() => router.push('/dashboard/products')}
          className="cursor-pointer"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Products
        </Button>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>Product not found</AlertDescription>
        </Alert>
        <Button
          variant="outline"
          onClick={() => router.push('/dashboard/products')}
          className="cursor-pointer"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Products
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Product Details</h1>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => router.push('/dashboard/products')}
            className="cursor-pointer"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Products
          </Button>
          <Link href={`/dashboard/products/${product.id}`}>
            <Button className="cursor-pointer">
              <Edit className="mr-2 h-4 w-4" />
              Edit Product
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Product Information */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Product Information</CardTitle>
            <CardDescription>Detailed information about this product</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between border-b pb-4">
              <div className="flex items-center">
                <Package className="h-5 w-5 text-gray-500 mr-2" />
                <span className="font-medium">Product Name:</span>
              </div>
              <span className="text-lg">{product.name}</span>
            </div>

            {product.code && (
              <div className="flex items-center justify-between border-b pb-4">
                <div className="flex items-center">
                  <Tag className="h-5 w-5 text-gray-500 mr-2" />
                  <span className="font-medium">Product Code:</span>
                </div>
                <span>{product.code}</span>
              </div>
            )}

            {product.sku && (
              <div className="flex items-center justify-between border-b pb-4">
                <div className="flex items-center">
                  <Tag className="h-5 w-5 text-gray-500 mr-2" />
                  <span className="font-medium">SKU:</span>
                </div>
                <span>{product.sku}</span>
              </div>
            )}

            <div className="flex items-center justify-between border-b pb-4">
              <div className="flex items-center">
                <DollarSign className="h-5 w-5 text-gray-500 mr-2" />
                <span className="font-medium">Cost Price:</span>
              </div>
              <span>{formatCurrency(product.costPrice)}</span>
            </div>

            <div className="flex items-center justify-between border-b pb-4">
              <div className="flex items-center">
                <DollarSign className="h-5 w-5 text-gray-500 mr-2" />
                <span className="font-medium">Selling Price:</span>
              </div>
              <span>{formatCurrency(product.sellingPrice)}</span>
            </div>

            {product.discount > 0 && (
              <div className="flex items-center justify-between border-b pb-4">
                <div className="flex items-center">
                  <Percent className="h-5 w-5 text-gray-500 mr-2" />
                  <span className="font-medium">Discount:</span>
                </div>
                <span>{product.discount}%</span>
              </div>
            )}

            <div className="flex items-center justify-between border-b pb-4">
              <div className="flex items-center">
                <Info className="h-5 w-5 text-gray-500 mr-2" />
                <span className="font-medium">Unit:</span>
              </div>
              <span>{product.unit}</span>
            </div>

            {product.weight && (
              <div className="flex items-center justify-between border-b pb-4">
                <div className="flex items-center">
                  <Package className="h-5 w-5 text-gray-500 mr-2" />
                  <span className="font-medium">Weight:</span>
                </div>
                <span>{product.weight}</span>
              </div>
            )}

            <div className="flex items-center justify-between border-b pb-4">
              <div className="flex items-center">
                <Info className="h-5 w-5 text-gray-500 mr-2" />
                <span className="font-medium">Category:</span>
              </div>
              <span>{product.category?.name || 'Uncategorized'}</span>
            </div>

            {product.vendor && (
              <div className="flex items-center justify-between border-b pb-4">
                <div className="flex items-center">
                  <Info className="h-5 w-5 text-gray-500 mr-2" />
                  <span className="font-medium">Vendor:</span>
                </div>
                <span>{product.vendor.name}</span>
              </div>
            )}

            <div className="flex items-center justify-between border-b pb-4">
              <div className="flex items-center">
                <Info className="h-5 w-5 text-gray-500 mr-2" />
                <span className="font-medium">Royalty Type:</span>
              </div>
              <span>{getRoyaltyTypeText(product.royaltyType)}</span>
            </div>

            {product.royaltyType !== 'none' && (
              <div className="flex items-center justify-between border-b pb-4">
                <div className="flex items-center">
                  <DollarSign className="h-5 w-5 text-gray-500 mr-2" />
                  <span className="font-medium">Royalty Value:</span>
                </div>
                <span>
                  {product.royaltyType === 'percentage' 
                    ? `${product.royaltyValue}%` 
                    : formatCurrency(product.royaltyValue)}
                </span>
              </div>
            )}

            <div className="flex items-center justify-between border-b pb-4">
              <div className="flex items-center">
                <Calendar className="h-5 w-5 text-gray-500 mr-2" />
                <span className="font-medium">Created At:</span>
              </div>
              <span>{formatDate(new Date(product.createdAt))}</span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Calendar className="h-5 w-5 text-gray-500 mr-2" />
                <span className="font-medium">Last Updated:</span>
              </div>
              <span>{formatDate(new Date(product.updatedAt))}</span>
            </div>
          </CardContent>
        </Card>

        {/* Status and Inventory Information */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                {product.isActive ? (
                  <>
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-green-600 font-medium">Active</span>
                  </>
                ) : (
                  <>
                    <XCircle className="h-5 w-5 text-red-500 mr-2" />
                    <span className="text-red-600 font-medium">Inactive</span>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Inventory</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="font-medium">Current Stock:</span>
                <span className="text-lg font-bold">
                  {product.stock !== undefined ? `${product.stock} ${product.unit}` : 'N/A'}
                </span>
              </div>
              {product.lastStockUpdate && (
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>Last Updated:</span>
                  <span>{formatDate(new Date(product.lastStockUpdate))}</span>
                </div>
              )}
              <div className="pt-4">
                <Link href={`/dashboard/inventory?productId=${product.id}`}>
                  <Button variant="outline" className="w-full">
                    View Inventory History
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          {product.description && (
            <Card>
              <CardHeader>
                <CardTitle>Description</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="whitespace-pre-wrap">{product.description}</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
