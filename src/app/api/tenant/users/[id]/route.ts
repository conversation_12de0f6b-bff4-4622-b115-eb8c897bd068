import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users, tenantUsers } from "@/db/schema";
import { auth } from "~/auth";
import { eq, and } from "drizzle-orm";
import bcrypt from "bcryptjs";
import { type NextRequest } from "next/server";

// GET /api/tenant/users/[id] - Get a specific tenant_sale user
export async function GET(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  try {
    // Check authentication and authorization
    const session = await auth();

    // Only tenant users can access this endpoint
    if (!session?.user || session.user.role !== 'tenant') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Only tenant admins can access tenant users.' },
        { status: 403 }
      );
    }

    const userId = (await context.params).id;

    // Get the user
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId)
    });

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if this user belongs to the current tenant
    const tenantUser = await db.query.tenantUsers.findFirst({
      where: and(
        eq(tenantUsers.userId, userId),
        eq(tenantUsers.tenantId, session.user.id)
      )
    });

    if (!tenantUser) {
      return NextResponse.json(
        { success: false, error: 'User does not belong to this tenant' },
        { status: 403 }
      );
    }

    // Return user without password
    const { password, ...userWithoutPassword } = user;

    return NextResponse.json({
      success: true,
      user: userWithoutPassword
    });
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch user' },
      { status: 500 }
    );
  }
}

// PUT /api/tenant/users/[id] - Update a tenant_sale user
export async function PUT(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  try {
    // Check authentication and authorization
    const session = await auth();

    // Only tenant users can access this endpoint
    if (!session?.user || session.user.role !== 'tenant') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Only tenant admins can update tenant users.' },
        { status: 403 }
      );
    }

    const userId = (await context.params).id;

    // Check if user exists
    const existingUser = await db.query.users.findFirst({
      where: eq(users.id, userId)
    });

    if (!existingUser) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if this user belongs to the current tenant
    const tenantUser = await db.query.tenantUsers.findFirst({
      where: and(
        eq(tenantUsers.userId, userId),
        eq(tenantUsers.tenantId, session.user.id)
      )
    });

    if (!tenantUser) {
      return NextResponse.json(
        { success: false, error: 'User does not belong to this tenant' },
        { status: 403 }
      );
    }

    // Parse request body
    const {
      username,
      email,
      fullName,
      password,
      isActive
    } = await request.json();

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date()
    };

    if (username !== undefined) updateData.username = username;
    if (email !== undefined) updateData.email = email;
    if (fullName !== undefined) updateData.fullName = fullName;
    if (isActive !== undefined) updateData.isActive = isActive;

    // Only update password if provided
    if (password) {
      updateData.password = await bcrypt.hash(password, 10);
    }

    // Update user
    await db.update(users)
      .set(updateData)
      .where(eq(users.id, userId));

    // Update tenant_user relationship if isActive changed
    if (isActive !== undefined) {
      await db.update(tenantUsers)
        .set({
          isActive: isActive,
          updatedAt: new Date()
        })
        .where(and(
          eq(tenantUsers.userId, userId),
          eq(tenantUsers.tenantId, session.user.id)
        ));
    }

    // Get updated user
    const updatedUser = await db.query.users.findFirst({
      where: eq(users.id, userId)
    });

    if (!updatedUser) {
      return NextResponse.json(
        { success: false, error: 'Failed to retrieve updated user' },
        { status: 500 }
      );
    }

    // Return user without password
    const { password: _, ...userWithoutPassword } = updatedUser;

    return NextResponse.json({
      success: true,
      user: userWithoutPassword,
      message: 'User updated successfully'
    });
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update user' },
      { status: 500 }
    );
  }
}
