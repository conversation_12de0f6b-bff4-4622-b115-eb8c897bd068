import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { products } from "@/db/schema";
import { auth } from "~/auth";
import { eq } from "drizzle-orm";

// GET /api/debug/products - Debug endpoint to check products
export async function GET(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get tenant ID from session based on user role
    let tenantId: string;
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      // For sales users, use their associated tenant's ID
      tenantId = session.user.tenantId;
    } else {
      // For tenant users, use their own ID
      tenantId = session.user.id;
    }

    // Get all products for this tenant
    const allProducts = await db.query.products.findMany({
      where: eq(products.tenantId, tenantId),
      orderBy: (products, { asc }) => [asc(products.name)]
    });

    return NextResponse.json({
      success: true,
      user: {
        id: session.user.id,
        role: session.user.role,
        tenantId: tenantId
      },
      productCount: allProducts.length,
      products: allProducts.slice(0, 5) // Only return first 5 products to avoid large response
    });
  } catch (error) {
    console.error("Error fetching products for debug:", error);
    return NextResponse.json(
      { 
        success: false, 
        error: "Failed to fetch products for debug",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
