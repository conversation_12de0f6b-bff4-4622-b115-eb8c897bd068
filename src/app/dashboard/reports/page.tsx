import { auth } from "~/auth";
import { redirect } from "next/navigation";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { BarChart3, FileText, ShoppingCart } from "lucide-react";

export default async function ReportsPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/api/auth/signin");
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Reports</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ShoppingCart className="mr-2 h-5 w-5" />
              Order Reports
            </CardTitle>
            <CardDescription>
              View and analyze sales orders with filtering options
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-500 mb-4">
              Generate reports on sales orders with filters for date range, branch, and products.
              View totals, payment status, and detailed order information.
            </p>
            <Link href="/dashboard/reports/orders">
              <Button className="w-full">View Order Reports</Button>
            </Link>
          </CardContent>
        </Card>

        <Card className="opacity-50">
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="mr-2 h-5 w-5" />
              Sales Analytics
            </CardTitle>
            <CardDescription>
              Visualize sales trends and performance metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-500 mb-4">
              Analyze sales performance with charts and graphs. View trends over time,
              top-selling products, and revenue breakdowns.
            </p>
            <Button className="w-full" disabled>Coming Soon</Button>
          </CardContent>
        </Card>

        <Card className="opacity-50">
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="mr-2 h-5 w-5" />
              Inventory Reports
            </CardTitle>
            <CardDescription>
              Track inventory levels and movement
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-500 mb-4">
              Generate reports on inventory status, stock levels, and product movement.
              Identify low stock items and analyze inventory turnover.
            </p>
            <Button className="w-full" disabled>Coming Soon</Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
