"use client";

import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";

export function ToastHandler() {
    const { toast } = useToast();
    const searchParams = useSearchParams();

    useEffect(() => {
        const deleted = searchParams?.get("deleted") === "true";
        const error = searchParams?.get("error");

        if (deleted) {
            toast({
                title: "Success",
                description: "Sale deleted successfully",
                variant: "default",
            });
        }

        if (error) {
            toast({
                title: "Error",
                description: error,
                variant: "destructive",
            });
        }
    }, [searchParams, toast]);

    return null;
} 