"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Trash2, AlertTriangle } from "lucide-react";
import React, { useState } from "react";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";

export function SimpleDeleteButton({ id }: { id: string }) {
    const [isOpen, setIsOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleSubmit = () => {
        setIsSubmitting(true);
        console.log('Submitting delete form for sale ID:', id);

        // Create and submit the form programmatically
        const form = document.createElement('form');
        form.action = `/api/sales/${id}/delete`;
        form.method = 'POST';
        form.style.display = 'none';
        document.body.appendChild(form);
        form.submit();
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button
                    variant="outline"
                    className="text-red-500 hover:text-red-700 hover:bg-red-50 cursor-pointer"
                >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Sale (Direct)
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <AlertTriangle className="h-5 w-5 text-red-500" />
                        Confirm Deletion
                    </DialogTitle>
                    <DialogDescription>
                        Are you sure you want to delete this invoice? This action cannot be undone and will remove all associated data.
                    </DialogDescription>
                </DialogHeader>
                <DialogFooter className="sm:justify-between">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsOpen(false)}
                        className="mt-2 sm:mt-0"
                        disabled={isSubmitting}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="button"
                        variant="destructive"
                        onClick={handleSubmit}
                        disabled={isSubmitting}
                        className="cursor-pointer"
                    >
                        {isSubmitting ? "Deleting..." : "Delete Invoice"}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
