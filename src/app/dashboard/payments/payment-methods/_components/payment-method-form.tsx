'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';

type PaymentMethodFormProps = {
  method?: {
    id: string;
    name: string;
    code: string;
    description: string | null;
    isActive: boolean | null;
    isDefault: boolean | null;
  };
};

export default function PaymentMethodForm({ method }: PaymentMethodFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: method?.name || '',
    code: method?.code || '',
    description: method?.description || '',
    isActive: method?.isActive !== null && method?.isActive !== undefined ? method.isActive : true,
    isDefault: method?.isDefault !== null && method?.isDefault !== undefined ? method.isDefault : false,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim() || !formData.code.trim()) {
      toast.error('Name and code are required');
      return;
    }

    try {
      setIsLoading(true);

      const url = method
        ? `/api/payment-methods/${method.id}`
        : '/api/payment-methods';

      const method_type = method ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method: method_type,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to save payment method');
      }

      toast.success(`Payment method ${method ? 'updated' : 'created'} successfully`);
      router.push('/dashboard/payments/payment-methods');
    } catch (error) {
      console.error('Error saving payment method:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to save payment method');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/dashboard/payments/payment-methods');
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="name">Name *</Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            placeholder="e.g., Cash, Credit Card, Bank Transfer"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="code">Code *</Label>
          <Input
            id="code"
            name="code"
            value={formData.code}
            onChange={handleInputChange}
            placeholder="e.g., CASH, CARD, BANK"
            required
            disabled={!!method} // Don't allow editing code for existing methods
          />
          {method && (
            <p className="text-xs text-muted-foreground">
              Code cannot be changed after creation
            </p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleInputChange}
          placeholder="Optional description for this payment method"
          rows={3}
        />
      </div>

      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="isActive"
            checked={formData.isActive}
            onCheckedChange={(checked) => handleCheckboxChange('isActive', checked as boolean)}
          />
          <Label htmlFor="isActive">Active</Label>
          <p className="text-sm text-muted-foreground">
            Only active payment methods will be available for selection
          </p>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="isDefault"
            checked={formData.isDefault}
            onCheckedChange={(checked) => handleCheckboxChange('isDefault', checked as boolean)}
          />
          <Label htmlFor="isDefault">Set as default</Label>
          <p className="text-sm text-muted-foreground">
            This payment method will be pre-selected in forms
          </p>
        </div>
      </div>

      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={handleCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : method ? 'Update Payment Method' : 'Create Payment Method'}
        </Button>
      </div>
    </form>
  );
}
