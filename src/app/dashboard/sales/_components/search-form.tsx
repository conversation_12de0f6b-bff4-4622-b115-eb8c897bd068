"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useEffect } from "react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

export function SearchForm({ defaultValue }: { defaultValue?: string }) {
    const router = useRouter();
    const searchParams = useSearchParams();
    const [searchValue, setSearchValue] = React.useState(defaultValue || "");
    const [statusFilter, setStatusFilter] = React.useState(searchParams?.get("status") || "");

    // Get the current branch filter if it exists
    const branchFilter = searchParams?.get("branch") || "";

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        // Construct the new URL with search and preserving branch filter
        const params = new URLSearchParams();

        if (searchValue) {
            params.set("search", searchValue);
        }

        if (branchFilter) {
            params.set("branch", branchFilter);
        }

        if (statusFilter) {
            params.set("status", statusFilter);
        }

        const queryString = params.toString();
        router.push(`/dashboard/sales${queryString ? `?${queryString}` : ''}`);
    };

    // Handle status filter change
    const handleStatusChange = (value: string) => {
        setStatusFilter(value);

        // Construct the new URL with updated status filter
        const params = new URLSearchParams(searchParams ? searchParams.toString() : '');

        if (value) {
            params.set("status", value);
        } else {
            params.delete("status");
        }

        const queryString = params.toString();
        router.push(`/dashboard/sales${queryString ? `?${queryString}` : ''}`);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchValue(e.target.value);
    };

    const handleClear = () => {
        setSearchValue("");

        // Clear search but preserve branch and status filters
        const params = new URLSearchParams();

        if (branchFilter) {
            params.set("branch", branchFilter);
        }

        if (statusFilter) {
            params.set("status", statusFilter);
        }

        const queryString = params.toString();
        router.push(`/dashboard/sales${queryString ? `?${queryString}` : ''}`);
    };

    return (
        <div className="flex gap-2">
            <form onSubmit={handleSubmit} className="flex flex-1 gap-2">
                <div className="relative flex-1">
                    <Input
                        name="search"
                        placeholder="Search by memo number or customer name..."
                        value={searchValue}
                        onChange={handleChange}
                        className="pr-16 h-9"
                    />
                    {searchValue && (
                        <Button
                            type="button"
                            variant="ghost"
                            onClick={handleClear}
                            className="absolute right-0 top-0 h-9 px-2"
                        >
                            ✕
                        </Button>
                    )}
                </div>
                <Select value={statusFilter} onValueChange={handleStatusChange}>
                    <SelectTrigger className="w-[140px] h-9">
                        <SelectValue placeholder="All Orders" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="">All Orders</SelectItem>
                        <SelectItem value="draft">Draft Orders</SelectItem>
                        <SelectItem value="completed">Completed Orders</SelectItem>
                    </SelectContent>
                </Select>
                <Button type="submit" className="cursor-pointer h-9 px-3">Search</Button>
            </form>
        </div>
    );
}