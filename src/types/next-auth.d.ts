import 'next-auth';

// Define menu permission type
type MenuPermission = {
  menuId: string;
  name: string;
  displayName: string;
  userId: string;
  tenantId: string | null;  
  path: string;
  canView: boolean | null;
  canCreate: boolean | null;
  canEdit: boolean | null;
  canDelete: boolean | null;
  order: number;
};

declare module 'next-auth' {
  interface User {
    id: string;
    role: string;
    tenantId?: string | null;
    branchId?: string | null;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    menuPermissions?: MenuPermission[] | null;
  }

  interface Session {
    user: {
      id: string;
      role: string;
      tenantId?: string | null;
      branchId?: string | null;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      menuPermissions?: MenuPermission[] | null;
    };
  }
}