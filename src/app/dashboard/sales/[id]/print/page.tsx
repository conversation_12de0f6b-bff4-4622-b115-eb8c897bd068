import { Metadata } from "next";
import { redirect } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { auth } from "~/auth";
import { db } from "@/lib/db";
import { orders, branches, tenants } from "@/db/schema";
import { formatDate, formatCurrency } from "@/lib/utils";
import PrintStyles from "./PrintStyles";
import PrintButton from "./PrintButton";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import AutoPrint from "./AutoPrint";
import BanglaNumberConverter from "@/components/BanglaNumberConverter";
import MemoPaidStamp from "@/components/MemoPaidStamp";
import DraftWatermark from "@/components/DraftWatermark";

export const metadata: Metadata = {
    title: "Print Invoice",
    description: "Print a sales invoice",
};

// Mark this as standalone route segment for rendering
export const dynamic = "force-dynamic";

interface PrintPageProps {
    params: Promise<{ id: string }>;
}

export default async function PrintPage({ params }: PrintPageProps) {
    const session = await auth();
    if (!session?.user) {
        redirect("/api/auth/signin");
    }

    const { id } = await params;
    if (!id) {
        redirect("/dashboard/sales");
    }

    // Get the order with branch, customer, and items information
    const order = await db.query.orders.findFirst({
        where: (orders, { eq, and }) =>
            and(
                eq(orders.id, id),
                eq(orders.tenantId, session.user.id)
            ),
        with: {
            branch: true,
            customer: true,
            items: {
                with: {
                    product: {
                        with: {
                            category: true,
                            vendor: true
                        }
                    }
                }
            },
            performer: {
                columns: {
                    fullName: true
                }
            }
        }
    });

    if (!order) {
        redirect("/dashboard/sales");
    }

    // Get tenant information for company details
    const tenantInfo = await db.query.tenants.findFirst({
        where: (tenants, { eq }) => eq(tenants.userId, session.user.id)
    });

    // If branch doesn't have address, use tenant address as fallback
    const branchAddress = order.branch?.address || "N/A";
    const branchPhone = order.branch?.phone || "N/A";
    const companyName = tenantInfo?.companyName || "Your Company Name";
    const logoUrl = tenantInfo?.logoUrl || "/logo.webp"; // Default to logo.webp if no custom logo

    return (
        <div className="print-container bg-white min-h-screen">
            <div className="max-w-4xl mx-auto p-4">
                {/* Navigation buttons */}
                <div className="mb-8 print:hidden flex justify-between">
                    <Link href={`/dashboard/sales/${id}`}>
                        <Button variant="outline" className="flex items-center">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            View Details
                        </Button>
                    </Link>
                    <PrintButton />
                </div>

                {/* Invoice content */}
                <div className="border p-4 rounded shadow print:shadow-none relative">
                    {/* Memo Paid Stamp - Only show when payment status is "paid" */}
                    {order.paymentStatus === "paid" && (
                        <MemoPaidStamp
                            className="absolute top-1/4 right-10"
                        />
                    )}

                    {/* Draft Watermark - Only show for draft orders */}
                    {order.status === "draft" && (
                        <DraftWatermark />
                    )}

                    {/* Header - Bengali Style */}
                    <div className="mb-4 text-center">
                        <div className="flex items-center justify-between mb-2">
                            {/* Logo on left */}
                            <div className="w-24 h-24 relative">
                                <Image
                                    src={logoUrl}
                                    alt={companyName}
                                    width={80}
                                    height={80}
                                    style={{ objectFit: 'contain' }}
                                />
                            </div>

                            {/* Center header with Bengali text */}
                            <div className="text-center flex-1">
                                <h2 className="text-xl font-bold mb-1">কোটার কপি/মেমো</h2>
                                <h1 className="text-2xl font-bold mb-1">{companyName}</h1>
                                <p className="text-sm mt-1">{branchAddress}</p>
                                <p className="text-sm">📞 {branchPhone}</p>
                            </div>

                            {/* Invoice details on right */}
                            <div className="text-right">
                                <p className="mb-1"><strong>ক্রমিক নং:</strong> {order.memoNo}</p>
                                <p><strong>তারিখ:</strong> {formatDate(order.date)}</p>
                            </div>
                        </div>
                    </div>

                    {/* Customer Information */}
                    <div className="mb-4 border-t border-b py-2">
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <p><strong>কাস্টমার নাম:</strong> {order.customer?.name || "ওয়াক-ইন কাস্টমার"}</p>
                                <p><strong>ঠিকানা:</strong> {order.customer?.address || "N/A"}</p>
                            </div>
                            <div>
                                <p><strong>ফোন নম্বর:</strong> {order.customer?.phone || "N/A"}</p>
                                <p><strong>তারিখ:</strong> {formatDate(order.date)}</p>
                                <p><strong>সময়:</strong> {new Date(order.date).toLocaleTimeString()}</p>
                            </div>
                        </div>
                    </div>

                    {/* Items Table - Bengali Style */}
                    <div className="mb-4">
                        <table className="w-full border-collapse border">
                            <thead>
                                <tr className="bg-gray-100">
                                    <th className="py-2 px-2 text-center border">নং</th>
                                    <th className="py-2 px-2 text-left border">বই এর বিবরণ</th>
                                    <th className="py-2 px-2 text-center border">সংখ্যা</th>
                                    <th className="py-2 px-2 text-right border">বিক্রয় মূল্য</th>
                                    <th className="py-2 px-2 text-right border">মোট টাকা</th>
                                </tr>
                            </thead>
                            <tbody>
                                {order.items.map((item, index) => (
                                    <tr key={item.id}>
                                        <td className="py-1 px-2 text-center border">{index + 1}</td>
                                        <td className="py-1 px-2 text-left border">
                                            {item.product.name}
                                            {item.product.vendor?.name && (
                                                <div className="text-xs text-gray-600">
                                                    {item.product.vendor.name}
                                                </div>
                                            )}
                                        </td>
                                        <td className="py-1 px-2 text-center border">{item.quantity}</td>
                                        <td className="py-1 px-2 text-right border">{formatCurrency(item.unitPrice)}</td>
                                        <td className="py-1 px-2 text-right border">{formatCurrency(item.total)}</td>
                                    </tr>
                                ))}

                                {/* Empty rows to match the photo layout */}
                                {Array.from({ length: Math.max(0, 10 - order.items.length) }).map((_, index) => (
                                    <tr key={`empty-${index}`}>
                                        <td className="py-1 px-2 border">&nbsp;</td>
                                        <td className="py-1 px-2 border">&nbsp;</td>
                                        <td className="py-1 px-2 border">&nbsp;</td>
                                        <td className="py-1 px-2 border">&nbsp;</td>
                                        <td className="py-1 px-2 border">&nbsp;</td>
                                    </tr>
                                ))}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colSpan={2} className="py-1 px-2 text-right border font-bold">সর্বমোট</td>
                                    <td className="py-1 px-2 text-center border font-bold">{order.items.reduce((sum, item) => sum + item.quantity, 0)}</td>
                                    <td className="py-1 px-2 text-right border"></td>
                                    <td className="py-1 px-2 text-right border font-bold">{formatCurrency(order.subTotal || 0)}</td>
                                </tr>
                                {order.discountAmount && order.discountAmount > 0 && (
                                    <tr>
                                        <td colSpan={4} className="py-1 px-2 text-right border">ডিসকাউন্ট {order.discountPercentage || 0}%</td>
                                        <td className="py-1 px-2 text-right border">{formatCurrency(order.discountAmount)}</td>
                                    </tr>
                                )}
                                {(order.extCommission || 0) > 0 && (
                                    <tr>
                                        <td colSpan={4} className="py-1 px-2 text-right border">
                                            কমিশন {order.extCommissionType === "percentage" ? `${order.extCommission || 0}%` : "ফিক্সড"}
                                        </td>
                                        <td className="py-1 px-2 text-right border">
                                            {formatCurrency(order.extCommissionType === "percentage"
                                                ? ((order.subTotal || 0) * (order.extCommission || 0) / 100)
                                                : (order.extCommission || 0))}
                                        </td>
                                    </tr>
                                )}
                                <tr>
                                    <td colSpan={4} className="py-1 px-2 text-right border font-bold">মোট মূল্য</td>
                                    <td className="py-1 px-2 text-right border font-bold">{formatCurrency(order.totalAmount || 0)}</td>
                                </tr>
                                <tr>
                                    <td colSpan={4} className="py-1 px-2 text-right border">পূর্ব বকেয়া/জমা</td>
                                    <td className="py-1 px-2 text-right border">{formatCurrency(order?.previousDue || 0)}</td>
                                </tr>
                                <tr>
                                    <td colSpan={4} className="py-1 px-2 text-right border font-bold">সর্বমোট</td>
                                    <td className="py-1 px-2 text-right border font-bold">{formatCurrency((order.totalAmount || 0) + (order.previousDue || 0))}</td>
                                </tr>
                                <tr>
                                    <td colSpan={4} className="py-1 px-2 text-right border">পরিশোধিত অর্থ</td>
                                    <td className="py-1 px-2 text-right border">{formatCurrency(order.paidAmount || 0)}</td>
                                </tr>
                                <tr>
                                    <td colSpan={4} className="py-1 px-2 text-right border font-bold">বর্তমান ব্যালেন্স</td>
                                    <td className="py-1 px-2 text-right border font-bold">{formatCurrency(order.dueAmount || 0)}</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    {/* Amount in words */}
                    <div className="mb-4 text-sm">
                        <p><strong>কথায়:</strong> <BanglaNumberConverter numb={(order.totalAmount || 0) + (order.previousDue || 0)} showCurrency={true} /></p>
                    </div>

                    {/* Signature Section */}
                    <div className="mt-8 flex justify-between">
                        <div className="text-center">
                            <div className="border-t border-black pt-1 w-32">
                                <p className="text-sm">ক্রেতার স্বাক্ষর</p>
                            </div>
                        </div>
                        <div className="text-center">
                            <div className="border-t border-black pt-1 w-32">
                                <p className="text-sm">বিক্রেতার স্বাক্ষর</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <PrintStyles />
            <AutoPrint />
        </div>
    );
}