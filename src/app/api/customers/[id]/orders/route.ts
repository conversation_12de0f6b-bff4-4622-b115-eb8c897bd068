import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { orders } from "@/db/schema";
import { auth } from "~/auth";
import { eq, and, or } from "drizzle-orm";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const { id: customerId } = await params;
    const tenantId = session.user.tenantId || session.user.id;

    // Get all unpaid and partial orders for this customer
    const customerOrders = await db.query.orders.findMany({
      where: (orders, { eq, and, or, gt }) => and(
        eq(orders.customerId, customerId),
        eq(orders.tenantId, tenantId),
        or(
          eq(orders.paymentStatus, "unpaid"),
          eq(orders.paymentStatus, "partial")
        ),
        gt(orders.dueAmount, 0) // Only orders with due amount > 0
      ),
      orderBy: (orders, { asc }) => [asc(orders.date)], // Oldest orders first
      with: {
        customer: {
          columns: {
            id: true,
            name: true,
            phone: true
          }
        }
      }
    });

    // Filter out orders with zero or negative due amounts (just to be safe)
    const unpaidOrders = customerOrders.filter(order => (order.dueAmount || 0) > 0);

    return NextResponse.json({
      success: true,
      orders: unpaidOrders
    });
  } catch (error) {
    console.error("Error fetching customer orders:", error);
    return NextResponse.json(
      { 
        success: false, 
        error: "Failed to fetch customer orders",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
