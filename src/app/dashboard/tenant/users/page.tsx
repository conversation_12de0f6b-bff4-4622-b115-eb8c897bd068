"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PlusIcon, MagnifyingGlassIcon, ReloadIcon } from "@radix-ui/react-icons";

interface User {
  id: string;
  fullName: string | null;
  email: string | null;
  username: string | null;
  isActive: boolean;
  createdAt: string | null;
  role: string;
}

export default function TenantUsersPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const searchQuery = searchParams?.get("search") || "";

  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState(searchQuery);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [hasMenus, setHasMenus] = useState(true);

  // Fetch users and menu data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  // Update filtered users when search term changes
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredUsers(users);
      return;
    }

    const lowerQuery = searchTerm.toLowerCase();
    const filtered = users.filter(user =>
      (user.fullName?.toLowerCase().includes(lowerQuery) || false) ||
      (user.email?.toLowerCase().includes(lowerQuery) || false) ||
      (user.username?.toLowerCase().includes(lowerQuery) || false)
    );

    setFilteredUsers(filtered);
  }, [searchTerm, users]);

  // Update URL when search term changes (with debounce)
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchTerm) {
        router.push(`/dashboard/tenant/users?search=${encodeURIComponent(searchTerm)}`);
      } else if (searchParams?.get("search")) {
        router.push("/dashboard/tenant/users");
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timer);
  }, [searchTerm, router, searchParams]);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError("");

      // Fetch menu data
      const menuResponse = await fetch("/api/menus");
      const menuData = await menuResponse.json();
      setHasMenus(menuData.success && menuData.menus.length > 0);

      // Fetch users
      const userResponse = await fetch("/api/tenant/users");
      const userData = await userResponse.json();

      if (userData.success) {
        const tenantSaleUsers = userData.users
          .filter((tu: any) => tu.user.role === 'tenant_sale')
          .map((tu: any) => ({
            id: tu.user.id,
            fullName: tu.user.fullName,
            email: tu.user.email,
            username: tu.user.username,
            isActive: tu.user.isActive,
            createdAt: tu.user.createdAt,
            role: tu.user.role
          }));

        setUsers(tenantSaleUsers);

        // Apply initial filtering if search query exists
        if (searchQuery) {
          const lowerQuery = searchQuery.toLowerCase();
          const filtered = tenantSaleUsers.filter((user: User) =>
            (user.fullName?.toLowerCase().includes(lowerQuery) || false) ||
            (user.email?.toLowerCase().includes(lowerQuery) || false) ||
            (user.username?.toLowerCase().includes(lowerQuery) || false)
          );
          setFilteredUsers(filtered);
        } else {
          setFilteredUsers(tenantSaleUsers);
        }
      } else {
        setError(userData.error || "Failed to load users");
      }
    } catch (err) {
      setError("An error occurred while fetching data");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleClearSearch = () => {
    setSearchTerm("");
    router.push("/dashboard/tenant/users");
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Tenant Sale Users</h1>
        <Link href="/dashboard/tenant/users/add">
          <Button className="cursor-pointer">
            <PlusIcon className="h-4 w-4 mr-1" />
            {hasMenus ? 'Add User' : 'Initialize & Add User'}
          </Button>
        </Link>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {/* Search Form */}
      <div className="mb-6">
        <div className="flex gap-2">
          <div className="relative flex-1">
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
            <Input
              type="search"
              placeholder="Search by name, email or username..."
              className="pl-8"
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </div>
          {searchTerm && (
            <Button
              variant="outline"
              className="cursor-pointer"
              onClick={handleClearSearch}
            >
              <ReloadIcon className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {!hasMenus && (
        <div className="bg-amber-50 border border-amber-200 text-amber-800 px-4 py-3 rounded mb-6">
          <p className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            Menu data needs to be initialized before adding tenant sale users. Click the button above to proceed.
          </p>
        </div>
      )}

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Email
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Created At
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {filteredUsers.length === 0 ? (
              <tr>
                <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                  {searchTerm
                    ? `No users match your search "${searchTerm}". Try a different search term.`
                    : "No tenant sale users found. Create one to get started."}
                </td>
              </tr>
            ) : (
              filteredUsers.map((user) => (
                <tr key={user.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {user.fullName || 'N/A'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {user.email || 'N/A'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.isActive
                      ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                      : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                      }`}>
                      {user.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link
                      href={`/dashboard/tenant/users/${user.id}/permissions`}
                      className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-4"
                    >
                      Permissions
                    </Link>
                    <Link
                      href={`/dashboard/tenant/users/${user.id}/edit`}
                      className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                    >
                      Edit
                    </Link>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
