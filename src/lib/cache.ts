// Simple in-memory cache implementation for server components

type CacheEntry<T> = {
  data: T;
  expiry: number;
};

// Cache storage
const cache = new Map<string, CacheEntry<any>>();

/**
 * Get data from cache or fetch it using the provided function
 * @param key Cache key
 * @param fetchFn Function to fetch data if not in cache
 * @param ttl Time to live in milliseconds (default: 5 minutes)
 * @returns Cached or freshly fetched data
 */
export async function getCachedData<T>(
  key: string,
  fetchFn: () => Promise<T>,
  ttl: number = 5 * 60 * 1000 // 5 minutes default
): Promise<T> {
  const now = Date.now();
  const cached = cache.get(key);

  // Return cached data if it exists and hasn't expired
  if (cached && cached.expiry > now) {
    return cached.data;
  }

  // Fetch fresh data
  const data = await fetchFn();

  // Store in cache
  cache.set(key, {
    data,
    expiry: now + ttl,
  });

  return data;
}

/**
 * Invalidate a specific cache entry
 * @param key Cache key to invalidate
 */
export function invalidateCache(key: string): void {
  cache.delete(key);
}

/**
 * Clear all cache entries
 */
export function clearCache(): void {
  cache.clear();
}

/**
 * Get cache statistics
 * @returns Object with cache size and keys
 */
export function getCacheStats(): { size: number; keys: string[] } {
  return {
    size: cache.size,
    keys: Array.from(cache.keys()),
  };
}
