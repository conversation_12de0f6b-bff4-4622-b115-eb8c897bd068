import { NextRequest, NextResponse } from "next/server";
import { auth } from "~/auth";
import { ALL_MENUS, getAllMenusWithPermissions } from "@/lib/menu-list";
import { getCachedData } from "@/lib/cache";

// GET /api/user/menus - Get all menus the current user has access to
export async function GET(_req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Admin and tenant users have access to all menus
    if (session.user.role === 'admin' || session.user.role === 'tenant') {
      // Get all menus with full permissions, using cache for performance
      const cacheKey = `menus_${session.user.id}_${session.user.role}`;
      const allMenusWithPermissions = await getCachedData(
        cacheKey,
        async () => getAllMenusWithPermissions(
          session.user.id,
          session.user.role === 'tenant' ? session.user.id : undefined
        ),
        60 * 60 * 1000 // Cache for 1 hour
      );

      return NextResponse.json({
        success: true,
        menus: allMenusWithPermissions
      });
    }

    // For tenant_sale users, check permissions
    if (session.user.role === 'tenant_sale') {
      // First check if permissions are already in the session
      if (session.user.menuPermissions && session.user.menuPermissions.length > 0) {
        console.log('Using menu permissions from session for menus API request');

        // Use cache for better performance
        const cacheKey = `tenant_sale_menus_${session.user.id}`;
        const sortedPermissions = await getCachedData(
          cacheKey,
          async () => {
            // Ensure dashboard is always accessible
            const dashboardMenu = ALL_MENUS.find(menu => menu.name === 'dashboard');
            const menuPermissions = session.user.menuPermissions || [];
            const hasDashboard = menuPermissions.some(
              (p: any) => p.name === 'dashboard'
            );

            let permissions = [...menuPermissions];

            // Add dashboard if not already present
            if (!hasDashboard && dashboardMenu) {
              permissions.push({
                menuId: dashboardMenu.id,
                name: dashboardMenu.name,
                displayName: dashboardMenu.displayName,
                path: dashboardMenu.path,
                userId: session.user.id,
                tenantId: session.user.tenantId || '',
                canView: true,
                canCreate: true,
                canEdit: true,
                canDelete: true,
                order: dashboardMenu.order
              });
            }

            // Add order property to permissions and sort by order
            return permissions.map(permission => {
              const menu = ALL_MENUS.find(m => m.id === permission.menuId || m.name === permission.name);
              return {
                ...permission,
                order: menu ? menu.order : 999
              };
            }).sort((a, b) => a.order - b.order);
          },
          30 * 60 * 1000 // Cache for 30 minutes
        );

        return NextResponse.json({
          success: true,
          menus: sortedPermissions
        });
      }
      // If permissions are not in session, return only dashboard
      else {
        console.log('No menu permissions in session, returning only dashboard');

        // Get only dashboard menu with full permissions
        const dashboardMenu = ALL_MENUS.find(menu => menu.name === 'dashboard');
        if (dashboardMenu) {
          return NextResponse.json({
            success: true,
            menus: [{
              menuId: dashboardMenu.id,
              name: dashboardMenu.name,
              displayName: dashboardMenu.displayName,
              path: dashboardMenu.path,
              userId: session.user.id,
              tenantId: session.user.tenantId || '',
              canView: true,
              canCreate: true,
              canEdit: true,
              canDelete: true,
              order: dashboardMenu.order
            }]
          });
        }
      }
    }

    // Default response (no menus)
    return NextResponse.json({
      success: true,
      menus: []
    });
  } catch (error) {
    console.error("Error fetching user menus:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch user menus" },
      { status: 500 }
    );
  }
}
