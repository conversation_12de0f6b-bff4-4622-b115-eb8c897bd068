'use client';

import { useState } from "react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { formatCurrency } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DollarSign, PlusCircle, MinusCircle } from "lucide-react";

interface BalanceAdjustmentDialogProps {
  customerId: string;
  customerName: string;
  currentBalance: number;
}

export function BalanceAdjustmentDialog({
  customerId,
  customerName,
  currentBalance,
}: BalanceAdjustmentDialogProps) {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [amount, setAmount] = useState<number>(0);
  const [adjustmentType, setAdjustmentType] = useState<string>("");
  const [notes, setNotes] = useState<string>("");
  const [paymentMethod, setPaymentMethod] = useState<string>("adjustment");
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  // Determine if customer has advanced payment (negative balance)
  const hasAdvancedPayment = currentBalance < 0;
  const hasDueBalance = currentBalance > 0;
  const hasZeroBalance = currentBalance === 0;
  const advancedAmount = Math.abs(currentBalance < 0 ? currentBalance : 0);

  const handleSubmit = async () => {
    if (amount <= 0) {
      toast.error("Amount must be greater than zero");
      return;
    }

    if (!adjustmentType) {
      toast.error("Please select an adjustment type");
      return;
    }

    // Additional validation based on adjustment type
    if (adjustmentType === "decrease_due" && amount > currentBalance) {
      toast.error("Amount cannot exceed current due balance");
      return;
    }

    if (adjustmentType === "use_advance" && amount > advancedAmount) {
      toast.error("Amount cannot exceed available advanced payment");
      return;
    }

    try {
      setIsSubmitting(true);

      const response = await fetch(`/api/customers/${customerId}/adjust-balance`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          amount,
          adjustmentType,
          notes,
          paymentMethod,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to adjust balance");
      }

      toast.success("Balance adjusted successfully");
      setOpen(false);
      router.refresh();

      // Reset form
      setAmount(0);
      setAdjustmentType("");
      setNotes("");
    } catch (error: any) {
      toast.error(error.message || "An error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="flex items-center gap-2"
          data-balance-adjust-trigger
        >
          <DollarSign className="h-4 w-4" />
          <span>Manage Due/Advanced</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Adjust Customer Balance</DialogTitle>
          <DialogDescription>
            Manage due or advanced payments for {customerName}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="amount" className="text-right">
              Amount
            </Label>
            <div className="col-span-3 relative">
              <Input
                id="amount"
                type="number"
                min="0.01"
                step="0.01"
                value={amount || ""}
                onChange={(e) => setAmount(parseFloat(e.target.value) || 0)}
                className="pr-8"
              />
              <span className="absolute right-3 top-2.5 text-gray-500">৳</span>
            </div>
          </div>

          <div className="grid grid-cols-4 items-start gap-4">
            <Label className="text-right pt-2">Type</Label>
            <RadioGroup
              value={adjustmentType}
              onValueChange={setAdjustmentType}
              className="col-span-3 space-y-2"
            >
              {/* Due Payment Options */}
              <div className="mb-2 pb-2 border-b border-gray-100">
                <p className="text-sm font-medium text-gray-500 mb-2">Due Payment Options:</p>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="increase_due" id="increase_due" />
                  <Label htmlFor="increase_due" className="flex items-center gap-1">
                    <PlusCircle className="h-4 w-4 text-red-500" />
                    Increase Due Amount
                  </Label>
                </div>

                {hasDueBalance && (
                  <div className="flex items-center space-x-2 mt-2">
                    <RadioGroupItem value="decrease_due" id="decrease_due" />
                    <Label htmlFor="decrease_due" className="flex items-center gap-1">
                      <MinusCircle className="h-4 w-4 text-green-500" />
                      Decrease Due Amount
                    </Label>
                  </div>
                )}
              </div>

              {/* Advanced Payment Options */}
              <div>
                <p className="text-sm font-medium text-gray-500 mb-2">Advanced Payment Options:</p>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="add_advance" id="add_advance" />
                  <Label htmlFor="add_advance" className="flex items-center gap-1">
                    <PlusCircle className="h-4 w-4 text-green-500" />
                    Add Advanced Payment
                  </Label>
                </div>

                {hasAdvancedPayment && (
                  <div className="flex items-center space-x-2 mt-2">
                    <RadioGroupItem value="use_advance" id="use_advance" />
                    <Label htmlFor="use_advance" className="flex items-center gap-1">
                      <MinusCircle className="h-4 w-4 text-blue-500" />
                      Use Advanced Payment
                    </Label>
                  </div>
                )}
              </div>
            </RadioGroup>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="payment-method" className="text-right">
              Method
            </Label>
            <Select
              value={paymentMethod}
              onValueChange={setPaymentMethod}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select payment method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="adjustment">Adjustment</SelectItem>
                <SelectItem value="cash">Cash</SelectItem>
                <SelectItem value="bank">Bank Transfer</SelectItem>
                <SelectItem value="mobile">Mobile Banking</SelectItem>
                <SelectItem value="card">Card</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="notes" className="text-right">
              Notes
            </Label>
            <Textarea
              id="notes"
              placeholder="Reason for adjustment"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="col-span-3"
            />
          </div>

          {adjustmentType && (
            <div className="bg-blue-50 p-3 rounded-md text-sm text-blue-700 mt-2">
              {adjustmentType === "increase_due" && (
                <p>This will <strong>increase</strong> the customer's due balance by {formatCurrency(amount)}. Current due: {formatCurrency(Math.max(0, currentBalance))}</p>
              )}
              {adjustmentType === "decrease_due" && (
                <p>This will <strong>decrease</strong> the customer's due balance by {formatCurrency(amount)}. Current due: {formatCurrency(Math.max(0, currentBalance))}</p>
              )}
              {adjustmentType === "add_advance" && (
                <p>This will <strong>add</strong> {formatCurrency(amount)} as advanced payment. {hasAdvancedPayment ? `Current advanced payment: ${formatCurrency(advancedAmount)}` : ''}</p>
              )}
              {adjustmentType === "use_advance" && (
                <p>This will <strong>use</strong> {formatCurrency(amount)} from the customer's advanced payment. Current advanced payment: {formatCurrency(advancedAmount)}</p>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleSubmit}
            disabled={isSubmitting || amount <= 0 || !adjustmentType}
          >
            {isSubmitting ? "Processing..." : "Adjust Balance"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
