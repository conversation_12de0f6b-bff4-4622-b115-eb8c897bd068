'use client';

import { useState, useEffect, useCallback } from 'react';

// Cache storage
const clientCache = new Map<string, { data: any; timestamp: number }>();

/**
 * Clear a specific cache entry by URL pattern
 * @param urlPattern String or RegExp to match against cache keys
 */
export function clearCacheByUrlPattern(urlPattern: string | RegExp): void {
  const pattern = typeof urlPattern === 'string'
    ? new RegExp(urlPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'))
    : urlPattern;

  for (const key of clientCache.keys()) {
    if (pattern.test(key)) {
      clientCache.delete(key);
    }
  }
}

/**
 * Clear all cache entries
 */
export function clearAllCache(): void {
  clientCache.clear();
}

/**
 * Custom hook for fetching data with client-side caching
 * @param url The URL to fetch data from
 * @param options Fetch options
 * @param cacheDuration Cache duration in milliseconds (default: 5 minutes)
 * @returns Object containing data, loading state, error, and refetch function
 */
export function useCachedFetch<T = any>(
  url: string,
  options?: RequestInit,
  cacheDuration: number = 5 * 60 * 1000
) {
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (skipCache = false) => {
    if (!url) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const cacheKey = `${url}:${JSON.stringify(options)}`;
      const now = Date.now();
      const cached = clientCache.get(cacheKey);

      // Use cached data if available and not expired
      if (!skipCache && cached && now - cached.timestamp < cacheDuration) {
        setData(cached.data);
        setIsLoading(false);
        return;
      }

      // Fetch fresh data
      const response = await fetch(url, options);

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const result = await response.json();

      // Store in cache
      clientCache.set(cacheKey, { data: result, timestamp: now });

      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('An error occurred while fetching data'));
      console.error('Fetch error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [url, options, cacheDuration]);

  // Initial fetch - only run on client side
  useEffect(() => {
    // Only fetch if we're in the browser and have a URL
    if (typeof window !== 'undefined' && url) {
      fetchData();
    }
  }, [fetchData, url]);

  // Return data, loading state, error, and refetch function
  return { data, isLoading, error, refetch: () => fetchData(true) };
}
