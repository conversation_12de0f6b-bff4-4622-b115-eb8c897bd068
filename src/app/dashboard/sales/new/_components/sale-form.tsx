"use client";

import { useState, useEffect, useRef, FormEvent } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import {
    Card,
    CardContent,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Check, PlusCircle, Trash2, Search, RefreshCw, Plus, UserPlus } from "lucide-react";
import { formatCurrency, calculateDueAmount, cn } from "@/lib/utils";
import Link from "next/link";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogFooter,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import LoadingOverlay from "@/components/LoadingOverlay";
import { KeyboardShortcuts } from "./keyboard-shortcuts";
import { usePaymentMethods, getDefaultPaymentMethod, findPaymentMethodByCode } from "@/hooks/usePaymentMethods";

// Define types for products and customers
type Product = {
    id: string;
    name: string;
    code?: string | null;
    unit: string;
    sellingPrice: number;
    costPrice: number;
    discount?: number;
    stock?: number;
    category?: { name: string } | null;
    vendor?: { name: string } | null;
    isActive?: boolean;
};

type Customer = {
    id: string;
    name: string;
    phone?: string | null;
    email?: string | null;
    currentBalance?: number | null;
    tenantId: string;
    code?: string | null;
    address?: string | null;
    type?: "retail" | "wholesale" | null;
    isActive?: boolean | null;
    creditLimit?: number | null;
    initialDue?: number | null;
    extraCommission?: number | null;
    notes?: string | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
};

type OrderItem = {
    id: string;
    productId: string;
    productName: string;
    quantity: number;
    unitPrice: number;
    discountPercentage: number;
    total: number;
    code?: string;
    stock?: number;
    quantityError?: string;
};

// Define types for branches
type Branch = {
    id: string;
    name: string;
    code: string;
    isMain: boolean | null;
    isActive: boolean | null;
    email?: string | null;
    phone?: string | null;
    address?: string | null;
    tenantId: string;
    notes?: string | null;
    createdAt?: Date | null;
    updatedAt?: Date | null;
};

// Props type for the SaleForm component
interface SaleFormProps {
    products: any;
    customers: Customer[];
    branches: Branch[];
    initialBranchId: string;
    initialMemoNo: string;
    tenantId: string;
    inventoryData: Map<string, number>;
    sessionBranchId: string | null;
    existingOrder?: any;
    isEditMode?: boolean;
}

export function SaleForm({ products, customers = [], branches, initialBranchId, initialMemoNo, tenantId, inventoryData, sessionBranchId, existingOrder, isEditMode = false }: SaleFormProps) {
    const router = useRouter();
    const customerDropdownRef = useRef<HTMLDivElement>(null);
    const customerInputRef = useRef<HTMLInputElement>(null);
    const productDropdownRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
    const firstProductInputRef = useRef<HTMLInputElement>(null);

    // Fetch payment methods
    const { paymentMethods, isLoading: isLoadingPaymentMethods } = usePaymentMethods();
    const [date, setDate] = useState<string>(
        isEditMode && existingOrder?.date
            ? new Date(existingOrder.date).toISOString().split('T')[0]
            : new Date().toISOString().split('T')[0]
    );
    const [memoNo, setMemoNo] = useState<string>(initialMemoNo);
    const [isRefreshingCustomers, setIsRefreshingCustomers] = useState<boolean>(false);
    const [isCustomerDialogOpen, setIsCustomerDialogOpen] = useState<boolean>(false);
    const [isSubmittingCustomer, setIsSubmittingCustomer] = useState<boolean>(false);
    const [isProcessingDuePayment, setIsProcessingDuePayment] = useState<boolean>(false);
    const [newCustomer, setNewCustomer] = useState({
        name: '',
        code: '',
        phone: '',
        email: '',
        address: '',
        type: 'retail',
        creditLimit: 0,
        initialDue: 0,
        advancedPayment: 0,
        extraCommission: 0,
        notes: ''
    });

    // Branch selection state - use sessionBranchId if available (for tenant_sale users)
    // For edit mode, use the branch from the existing order
    // Otherwise, use the initialBranchId (which should be the main branch)
    const [selectedBranchId, setSelectedBranchId] = useState<string>(
        sessionBranchId ||
        (isEditMode && existingOrder?.branchId ? existingOrder.branchId : initialBranchId)
    );

    // Track the selected branch name and code for display
    const [selectedBranchName, setSelectedBranchName] = useState<string>(
        sessionBranchId ?
            (() => {
                const branch = branches.find(b => b.id === sessionBranchId);
                return branch ? `${branch.name} (${branch.code})` : "";
            })() :
            (isEditMode && existingOrder?.branchId ?
                (() => {
                    const branch = branches.find(b => b.id === existingOrder.branchId);
                    return branch ? `${branch.name} (${branch.code})` : "";
                })() :
                (() => {
                    const branch = branches.find(b => b.id === initialBranchId);
                    return branch ? `${branch.name} (${branch.code})` : "";
                })())
    );
    const [filteredProducts, setFilteredProducts] = useState<Product[]>(products);

    // Initialize customer data from existing order if in edit mode
    const initialCustomerId = isEditMode && existingOrder?.customerId ? existingOrder.customerId : "";
    const [customerId, setCustomerId] = useState<string>(initialCustomerId);
    const [customerSearch, setCustomerSearch] = useState('');
    const [allCustomers, setAllCustomers] = useState<Customer[]>(customers);
    const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>(customers);
    const [showCustomerDropdown, setShowCustomerDropdown] = useState(false);

    // Product search states
    const [productSearches, setProductSearches] = useState<{ [key: string]: string }>({});
    const [filteredProductsByRow, setFilteredProductsByRow] = useState<{ [key: string]: Product[] }>({});
    const [showProductDropdowns, setShowProductDropdowns] = useState<{ [key: string]: boolean }>({});

    // Find selected customer if needed
    const findSelectedCustomer = (id: string) => {
        return allCustomers.find((c: Customer) => c.id === id) || null;
    };

    const [customerName, setCustomerName] = useState<string>(
        isEditMode && existingOrder?.customer?.name ? existingOrder.customer.name : ""
    );
    const [customerPhone, setCustomerPhone] = useState<string>(
        isEditMode && existingOrder?.customer?.phone ? existingOrder.customer.phone : ""
    );

    const [previousDue, setPreviousDue] = useState<number>(
        isEditMode && existingOrder?.previousDue ? existingOrder.previousDue : 0
    );
    const [payPreviousDue, setPayPreviousDue] = useState<boolean>(
        isEditMode && existingOrder?.previousDuePaid ? true : false
    );
    const [previousDuePaymentAmount, setPreviousDuePaymentAmount] = useState<number>(
        isEditMode && existingOrder?.previousDuePaid ? existingOrder.previousDue : 0
    );

    const [remarks, setRemarks] = useState<string>(
        isEditMode && existingOrder?.remarks ? existingOrder.remarks : ""
    );
    const [paymentMethod, setPaymentMethod] = useState<string>(
        isEditMode && existingOrder?.paymentMethod ? existingOrder.paymentMethod : ""
    );
    const [paymentMethodSearch, setPaymentMethodSearch] = useState<string>("");
    const [showPaymentMethodDropdown, setShowPaymentMethodDropdown] = useState<boolean>(false);
    const paymentMethodDropdownRef = useRef<HTMLDivElement>(null);
    const [paymentNo, setPaymentNo] = useState<string>(
        isEditMode && existingOrder?.paymentNo ? existingOrder.paymentNo : ""
    );
    const [paidAmount, setPaidAmount] = useState<number>(
        isEditMode && existingOrder?.paidAmount ? existingOrder.paidAmount : 0
    );

    const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
    const [showLoadingOverlay, setShowLoadingOverlay] = useState<boolean>(false);
    const [redirectInfo, setRedirectInfo] = useState<{ id: string, isRedirecting: boolean }>({ id: '', isRedirecting: false });

    // Create an empty order item
    const createEmptyOrderItem = () => ({
        id: `item-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        productId: "",
        productName: "",
        quantity: 1,
        unitPrice: 0,
        discountPercentage: 0,
        total: 0,
        code: "",
        stock: 0
    });

    // Initialize order items from existing order if in edit mode
    const initialOrderItems = isEditMode && existingOrder?.items && existingOrder.items.length > 0
        ? existingOrder.items.map((item: any) => {
            // For edit mode, we need to calculate the stock based on the branch
            let stock = 0;
            if (item.productId) {
                const key = `${item.productId}-${selectedBranchId}`;
                stock = inventoryData.get(key) || 0;
            }

            return {
                id: item.id,
                productId: item.productId,
                productName: item.product?.name || "",
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                discountPercentage: item.discountPercentage || 0,
                total: item.total,
                code: item.product?.code || "",
                stock: stock
            };
        })
        : Array(5).fill(null).map(() => createEmptyOrderItem());

    // Order items state
    const [orderItems, setOrderItems] = useState<OrderItem[]>(initialOrderItems);

    // Calculate totals
    const subTotal = orderItems.reduce((sum, item) => sum + item.total, 0);
    const [extCommission, setExtCommission] = useState<number>(
        isEditMode && existingOrder?.extCommission ? existingOrder.extCommission : 0
    );
    const [extCommissionType, setExtCommissionType] = useState<"fixed" | "percentage">(
        isEditMode && existingOrder?.extCommissionType ? existingOrder.extCommissionType : "percentage"
    );

    // Calculate total after commission based on commission type
    const totalAfterCommission = extCommissionType === "percentage"
        ? subTotal - (subTotal * extCommission / 100)
        : subTotal - extCommission;

    // Calculate the due amount based on the current order total and paid amount
    const dueAmount = calculateDueAmount(totalAfterCommission, paidAmount);

    // Net bill is just the total after commission (doesn't include previous due)
    // This represents the total amount for the current order only
    const netBill = totalAfterCommission;

    // Set default payment method when payment methods are loaded
    useEffect(() => {
        if (paymentMethods.length > 0) {
            if (isEditMode && existingOrder?.paymentMethod) {
                // In edit mode, find the payment method by code and set the display name
                const method = findPaymentMethodByCode(paymentMethods, existingOrder.paymentMethod);
                if (method) {
                    setPaymentMethod(method.code);
                    setPaymentMethodSearch(method.name);
                } else {
                    // Fallback if payment method not found
                    const defaultMethod = getDefaultPaymentMethod(paymentMethods);
                    if (defaultMethod) {
                        setPaymentMethod(defaultMethod.code);
                        setPaymentMethodSearch(defaultMethod.name);
                    }
                }
            } else if (!paymentMethod) {
                // For new orders, set the default payment method
                const defaultMethod = getDefaultPaymentMethod(paymentMethods);
                if (defaultMethod) {
                    setPaymentMethod(defaultMethod.code);
                    setPaymentMethodSearch(defaultMethod.name);
                }
            }
        }
    }, [paymentMethods, isEditMode, existingOrder?.paymentMethod, paymentMethod]);

    // Filter customers based on search term
    useEffect(() => {
        if (customerSearch.trim() === '') {
            setFilteredCustomers(allCustomers);
        } else {
            const searchTerm = customerSearch.toLowerCase();

            // If we have a selected customer ID and the search includes the customer name
            if (customerId) {
                const selectedCustomer = allCustomers.find((c: Customer) => c.id === customerId);
                if (selectedCustomer) {
                    const customerNameMatches = selectedCustomer.name.toLowerCase().includes(searchTerm);
                    const customerPhoneMatches = selectedCustomer.phone && selectedCustomer.phone.toLowerCase().includes(searchTerm);
                    const customerCodeMatches = selectedCustomer.code && selectedCustomer.code.toLowerCase().includes(searchTerm);

                    if (customerNameMatches || customerPhoneMatches || customerCodeMatches) {
                        // Keep showing the selected customer
                        return;
                    }
                }
            }

            const filtered = allCustomers.filter((customer: Customer) => {
                // Search by name
                if (customer.name.toLowerCase().includes(searchTerm)) {
                    return true;
                }

                // Search by phone
                if (customer.phone && customer.phone.toLowerCase().includes(searchTerm)) {
                    return true;
                }

                // Search by email
                if (customer.email && customer.email.toLowerCase().includes(searchTerm)) {
                    return true;
                }

                // Search by customer code
                if (customer.code && customer.code.toLowerCase().includes(searchTerm)) {
                    return true;
                }

                return false;
            });

            setFilteredCustomers(filtered);
        }
    }, [customerSearch, allCustomers, customerId]);

    // Handle click outside to close dropdown
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (customerDropdownRef.current && !customerDropdownRef.current.contains(event.target as Node)) {
                setShowCustomerDropdown(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Handle customer selection
    const handleCustomerChange = (customerId: string) => {
        setCustomerId(customerId);

        if (customerId) {
            const selectedCustomer = findSelectedCustomer(customerId);
            if (selectedCustomer) {
                setCustomerName(selectedCustomer.name);
                setCustomerPhone(selectedCustomer.phone || "");

                // Update the search field to show the selected customer
                let displayText = selectedCustomer.name;

                // Add code if available
                if (selectedCustomer.code) {
                    displayText += ` (${selectedCustomer.code})`;
                }
                // Add phone if available and code is not available
                else if (selectedCustomer.phone) {
                    displayText += ` (${selectedCustomer.phone})`;
                }

                setCustomerSearch(displayText);

                // Set the extra commission from the customer
                if (selectedCustomer.extraCommission !== null && selectedCustomer.extraCommission !== undefined) {
                    setExtCommission(selectedCustomer.extraCommission);
                    // Default to percentage type when loading from customer
                    setExtCommissionType("percentage");
                }

                // In edit mode, keep the previous due from the order
                if (!isEditMode) {
                    const currentBalance = selectedCustomer.currentBalance ?? 0;

                    // If currentBalance is negative, it means the customer has advanced payment
                    if (currentBalance < 0) {
                        // Set previousDue to 0 since there's no due, but advanced payment
                        setPreviousDue(0);
                        setPayPreviousDue(false);
                        setPreviousDuePaymentAmount(0);

                        // Set paid amount to include the advanced payment (absolute value of negative balance)
                        // This will automatically apply the advanced payment to the current order
                        const advancedPayment = Math.abs(currentBalance);

                        // If advanced payment is greater than or equal to the order total, set paid amount to order total
                        // Otherwise, set it to the advanced payment amount
                        setPaidAmount(totalAfterCommission > advancedPayment ? advancedPayment : totalAfterCommission);

                        console.log(`Customer has advanced payment: ${advancedPayment}, setting paid amount to: ${totalAfterCommission > advancedPayment ? advancedPayment : totalAfterCommission}`);
                    } else if (currentBalance > 0) {
                        // Normal due handling for positive balance
                        setPreviousDue(currentBalance);

                        // Default to not paying previous due
                        setPayPreviousDue(false);
                        setPreviousDuePaymentAmount(0);

                        console.log(`Customer has due balance: ${currentBalance}, previous due payment disabled by default`);
                    } else {
                        // Customer has zero balance
                        setPreviousDue(0);
                        setPayPreviousDue(false);
                        setPreviousDuePaymentAmount(0);

                        console.log(`Customer has zero balance`);
                    }
                }
            }
        } else {
            setCustomerName("");
            setCustomerPhone("");
            setCustomerSearch("");
            setExtCommission(0); // Reset extra commission when no customer is selected
            setExtCommissionType("percentage"); // Reset to percentage type
            if (!isEditMode) {
                setPreviousDue(0);
                setPayPreviousDue(false);
                setPreviousDuePaymentAmount(0);
            }
        }

        // Close the dropdown
        setShowCustomerDropdown(false);
    };

    // Function to handle customer form input changes
    const handleCustomerInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const { name, value, type } = e.target;
        const numValue = type === 'number' ? parseFloat(value) : value;

        // Update the customer state with the new value
        setNewCustomer(prev => {
            const updatedCustomer = {
                ...prev,
                [name]: numValue
            };

            // If initialDue field is being changed and has a value greater than 0
            if (name === 'initialDue' && type === 'number' && parseFloat(value) > 0) {
                // Reset advancedPayment to 0
                updatedCustomer.advancedPayment = 0;
            }

            // If advancedPayment field is being changed and has a value greater than 0
            if (name === 'advancedPayment' && type === 'number' && parseFloat(value) > 0) {
                // Reset initialDue to 0
                updatedCustomer.initialDue = 0;
            }

            return updatedCustomer;
        });
    };

    // Function to handle customer form submission
    const handleAddCustomer = async () => {
        // Validate required fields
        if (!newCustomer.name) {
            toast.error("Customer name is required");
            return;
        }

        setIsSubmittingCustomer(true);

        try {
            // Prepare customer data
            const customerData = {
                ...newCustomer,
                tenantId
            };

            // Submit the customer data
            const response = await fetch('/api/customers', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(customerData),
            });

            const data = await response.json();

            if (!response.ok) {
                // Handle specific error for duplicate code
                if (data.code === "DUPLICATE_CODE") {
                    toast.error("This customer code is already in use. Please use a unique code.");
                    return;
                } else {
                    throw new Error(data.message || "Something went wrong");
                }
            }

            // Close the dialog and reset form
            setIsCustomerDialogOpen(false);

            // Reset the form
            setNewCustomer({
                name: '',
                code: '',
                phone: '',
                email: '',
                address: '',
                type: 'retail',
                creditLimit: 0,
                initialDue: 0,
                advancedPayment: 0,
                extraCommission: 0,
                notes: ''
            });

            toast.success("Customer created successfully");

            // Refresh the customer list
            await refreshCustomers();

            // Select the newly created customer if ID is returned
            if (data.id) {
                handleCustomerChange(data.id);
            }
        } catch (error: any) {
            console.error("Error creating customer:", error);
            toast.error(error.message || "An error occurred while creating the customer");
        } finally {
            setIsSubmittingCustomer(false);
        }
    };

    // Function to refresh customer data
    const refreshCustomers = async () => {
        setIsRefreshingCustomers(true);

        try {
            // Fetch the latest customer data from the dedicated endpoint
            const response = await fetch('/api/customers/all');
            if (!response.ok) {
                throw new Error('Failed to fetch customers');
            }

            const data = await response.json();

            if (data.success && data.customers) {
                // Update the customers list with the fresh data
                const updatedCustomers = data.customers;

                // Update both allCustomers and filteredCustomers
                setAllCustomers(updatedCustomers);

                // Update filtered customers based on current search term
                if (customerSearch.trim() === '') {
                    setFilteredCustomers(updatedCustomers);
                } else {
                    const searchTerm = customerSearch.toLowerCase();
                    const filtered = updatedCustomers.filter((customer: Customer) => {
                        return customer.name.toLowerCase().includes(searchTerm) ||
                            (customer.phone && customer.phone.toLowerCase().includes(searchTerm)) ||
                            (customer.email && customer.email.toLowerCase().includes(searchTerm));
                    });
                    setFilteredCustomers(filtered);
                }

                // If we have a selected customer, update its data
                if (customerId) {
                    const updatedSelectedCustomer = updatedCustomers.find((c: Customer) => c.id === customerId);
                    if (updatedSelectedCustomer) {
                        const currentBalance = updatedSelectedCustomer.currentBalance ?? 0;

                        // Handle advanced payment (negative balance)
                        if (currentBalance < 0) {
                            // Set previousDue to 0 since there's no due, but advanced payment
                            setPreviousDue(0);
                            setPayPreviousDue(false);
                            setPreviousDuePaymentAmount(0);

                            // Set paid amount to include the advanced payment (absolute value of negative balance)
                            const advancedPayment = Math.abs(currentBalance);

                            // If advanced payment is greater than or equal to the order total, set paid amount to order total
                            // Otherwise, set it to the advanced payment amount
                            const newPaidAmount = totalAfterCommission > advancedPayment ? advancedPayment : totalAfterCommission;
                            setPaidAmount(newPaidAmount);

                            console.log(`Refreshed customer has advanced payment: ${advancedPayment}, setting paid amount to: ${newPaidAmount}`);
                        }
                        // Handle regular due (positive balance)
                        else if (currentBalance > 0) {
                            // If the due amount has changed
                            if (currentBalance !== previousDue) {
                                console.log(`Customer due amount changed from ${previousDue} to ${currentBalance}`);
                                setPreviousDue(currentBalance);

                                // If paying previous due, update the payment amount and total paid amount
                                if (payPreviousDue) {
                                    // Calculate the difference in due amount
                                    const dueDifference = currentBalance - previousDue;

                                    // Update the previous due payment amount
                                    setPreviousDuePaymentAmount(currentBalance);

                                    // Adjust the paid amount to account for the change in due
                                    const newPaidAmount = paidAmount + dueDifference;
                                    setPaidAmount(newPaidAmount);

                                    console.log(`Updated due payment: previousDue=${previousDue}, newDue=${currentBalance}, difference=${dueDifference}, newPaidAmount=${newPaidAmount}`);
                                }
                            }
                        }
                        // Handle zero balance
                        else if (currentBalance === 0 && previousDue !== 0) {
                            setPreviousDue(0);
                            setPayPreviousDue(false);
                            setPreviousDuePaymentAmount(0);

                            console.log(`Customer balance is now zero, resetting previous due payment`);
                        }
                    }
                }

                // Show success message
                toast.success('Customer data refreshed successfully');
            } else {
                throw new Error('Failed to refresh customers');
            }
        } catch (error) {
            console.error('Error refreshing customers:', error);
            toast.error('Failed to refresh customer data');
        } finally {
            // Add a small delay to show the refresh animation
            setTimeout(() => {
                setIsRefreshingCustomers(false);
            }, 500); // Reduced delay for better UX
        }
    };

    // Function to fetch unpaid orders for a customer
    const fetchUnpaidOrders = async (customerId: string) => {
        if (!customerId) {
            setUnpaidOrders([]);
            return;
        }

        setIsLoadingOrders(true);
        try {
            const response = await fetch(`/api/customers/${customerId}/orders`);
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setUnpaidOrders(data.orders || []);
                } else {
                    console.error('Failed to fetch unpaid orders:', data.error);
                    setUnpaidOrders([]);
                }
            } else {
                console.error('Failed to fetch unpaid orders');
                setUnpaidOrders([]);
            }
        } catch (error) {
            console.error('Error fetching unpaid orders:', error);
            setUnpaidOrders([]);
        } finally {
            setIsLoadingOrders(false);
        }
    };

    // Handle payment for a specific order
    const handleOrderPayment = async (order: any, paymentAmount: number) => {
        if (!order || paymentAmount <= 0) {
            toast.error("Invalid payment amount");
            return;
        }

        if (paymentAmount > order.dueAmount) {
            toast.error(`Cannot pay more than the due amount: ${formatCurrency(order.dueAmount)}`);
            return;
        }

        // Confirm with the user
        const confirmPayment = window.confirm(
            `Pay ${formatCurrency(paymentAmount)} for order ${order.memoNo}?`
        );
        if (!confirmPayment) {
            return;
        }

        setIsProcessingDuePayment(true);

        try {
            // Prepare payment data for specific order
            const paymentData = {
                tenantId,
                customerId,
                orderId: order.id,
                amount: paymentAmount,
                paymentMethod: "cash", // Default to cash
                paymentReference: "",
                notes: `Payment for order ${order.memoNo}`,
                date: new Date().toISOString()
            };

            // Submit the payment
            const response = await fetch('/api/payments', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(paymentData),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || "Failed to process payment");
            }

            // Show success message
            toast.success(`Payment of ${formatCurrency(paymentAmount)} processed successfully for order ${order.memoNo}`);

            // Refresh customer data and unpaid orders
            await refreshCustomers();
            await fetchUnpaidOrders(customerId);
        } catch (error: any) {
            console.error("Error processing payment:", error);
            toast.error(error.message || "An error occurred while processing the payment");
        } finally {
            setIsProcessingDuePayment(false);
        }
    };

    // Handle adding a new row
    const addNewRow = () => {
        setOrderItems([
            ...orderItems,
            createEmptyOrderItem()
        ]);
    };

    // Handle removing a row
    const removeRow = (id: string) => {
        if (orderItems.length > 1) {
            setOrderItems(orderItems.filter(item => item.id !== id));
        }
    };

    // Handle change in product selection, quantity, or discount
    const updateOrderItem = (id: string, updates: Partial<OrderItem>) => {
        setOrderItems(orderItems.map(item => {
            if (item.id === id) {
                const updatedItem = { ...item, ...updates };

                // Validate quantity against stock if quantity is being updated
                if ('quantity' in updates) {
                    const stock = updatedItem.stock || 0;
                    if (updatedItem.quantity > stock) {
                        updatedItem.quantity = stock;
                        updatedItem.quantityError = `Maximum available stock is ${stock}`;
                        toast.error(`Cannot exceed available stock of ${stock}`);
                    } else {
                        updatedItem.quantityError = undefined;
                    }
                }

                // Calculate total if necessary values have changed
                if ('quantity' in updates || 'unitPrice' in updates || 'discountPercentage' in updates) {
                    const quantity = updatedItem.quantity;
                    const unitPrice = updatedItem.unitPrice;
                    const discountPercentage = updatedItem.discountPercentage;

                    const totalBeforeDiscount = quantity * unitPrice;
                    const discountAmount = totalBeforeDiscount * (discountPercentage / 100);
                    updatedItem.total = Number((totalBeforeDiscount - discountAmount).toFixed(2));
                }

                return updatedItem;
            }
            return item;
        }));
    };

    // Check if a new row needs to be added
    useEffect(() => {
        // If all rows have products selected, add a new row
        const allRowsFilled = orderItems.every(item => item.productId);
        if (allRowsFilled) {
            addNewRow();
        }
    }, [orderItems]);

    // Toggle paying previous due
    const handleTogglePreviousDue = () => {
        const newPayPreviousDue = !payPreviousDue;
        setPayPreviousDue(newPayPreviousDue);

        if (newPayPreviousDue) {
            // When enabling previous due payment, set the amount to the full previous due
            setPreviousDuePaymentAmount(previousDue);

            // Due payment is a separate transaction, so we don't modify the paid amount
            console.log(`Enabled previous due payment: previousDue=${previousDue}, paidAmount remains unchanged`);
        } else {
            // When disabling previous due payment, reset the previous due payment amount
            setPreviousDuePaymentAmount(0);

            console.log(`Disabled previous due payment, paidAmount remains unchanged`);
        }
    };

    // Update previous due payment amount
    const handlePreviousDueAmountChange = (amount: number) => {
        if (amount > previousDue) {
            amount = previousDue;
            toast.error(`Cannot pay more than the previous due amount: ${formatCurrency(previousDue)}`);
        }

        // Update the previous due payment amount
        setPreviousDuePaymentAmount(amount);

        // Due payment is a separate transaction, so we don't modify the paid amount
        console.log(`Updated previous due payment: newAmount=${amount}, paidAmount remains unchanged`);
    };



    // Handle branch change
    const handleBranchChange = (branchId: string) => {
        setSelectedBranchId(branchId);

        // Update the selected branch name with code
        const branch = branches.find(b => b.id === branchId);
        setSelectedBranchName(branch ? `${branch.name} (${branch.code})` : "");

        // Update product stock based on selected branch
        const updatedProducts = products.map((product: Product) => {
            const key = `${product.id}-${branchId}`;
            return {
                ...product,
                stock: inventoryData.get(key) || 0
            };
        });

        setFilteredProducts(updatedProducts);

        // Update stock in existing order items while preserving product names
        setOrderItems(orderItems.map(item => {
            if (item.productId) {
                const key = `${item.productId}-${branchId}`;
                const newStock = inventoryData.get(key) || 0;

                // Find the product to ensure we have the correct name
                const product = products.find((p: Product) => p.id === item.productId);

                // Determine the appropriate quantity based on stock
                let newQuantity;
                if (newStock > 0) {
                    // If there's stock, set quantity to 1 (if it was 0) or keep current quantity (if within stock limits)
                    newQuantity = item.quantity === 0 ? 1 : (newStock < item.quantity ? newStock : item.quantity);
                } else {
                    // If no stock, set quantity to 0
                    newQuantity = 0;
                }

                return {
                    ...item,
                    stock: newStock,
                    // Keep the product name if it exists
                    productName: item.productName || (product ? product.name : ""),
                    // Set quantity based on our logic above
                    quantity: newQuantity,
                    quantityError: newStock < item.quantity ? `Maximum available stock is ${newStock}` : undefined
                };
            }
            return item;
        }));
    };

    // Update filtered products when branch changes or on initial load
    useEffect(() => {
        // Only update if a branch is selected
        if (selectedBranchId) {
            handleBranchChange(selectedBranchId);

            // Reset product dropdowns when branch changes
            // This ensures we only show products with stock in the new branch
            setShowProductDropdowns({});

            // Don't clear product searches when changing branches
            // This ensures product names remain visible in the search field
            // Only update the filtered products for the dropdown
            setFilteredProductsByRow({});
        } else {
            // If no branch is selected, set all products to have 0 stock
            const productsWithNoStock = products.map((product: Product) => ({
                ...product,
                stock: 0
            }));
            setFilteredProducts(productsWithNoStock);

            // Update order items to have 0 stock
            setOrderItems(orderItems.map(item => ({
                ...item,
                stock: 0
            })));

            // Clear product dropdowns when no branch is selected
            setShowProductDropdowns({});
            setFilteredProductsByRow({});

            // Clear any product searches when branch is deselected
            setProductSearches({});
        }
    }, [selectedBranchId, products]);

    // Set initial branch on component mount if not already set
    useEffect(() => {
        // This ensures the main branch is selected by default when the component mounts
        // Only do this if no branch is currently selected and we have an initialBranchId
        if (!selectedBranchId && initialBranchId && !isEditMode) {
            setSelectedBranchId(initialBranchId);
        }

        // Ensure ext commission type is set to percentage by default
        if (!isEditMode) {
            setExtCommissionType("percentage");
        }
    }, []);

    // Initial load of customers data
    useEffect(() => {
        // If customers prop changes, update the state
        if (customers.length > 0) {
            setAllCustomers(customers);
            setFilteredCustomers(customers);
        } else {
            // If no customers are provided, fetch them
            refreshCustomers();
        }
    }, [customers]);

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent, isDraft: boolean = false) => {
        e.preventDefault();

        // Reset any previous redirection state
        setRedirectInfo({ id: '', isRedirecting: false });
        setShowLoadingOverlay(false);

        // Validate the form
        if (!memoNo) {
            toast.error("Memo number is required");
            return;
        }

        if (!date) {
            toast.error("Date is required");
            return;
        }

        if (!selectedBranchId) {
            toast.error("Branch selection is required");
            return;
        }

        console.log("Before filtering items:", JSON.stringify(orderItems, null, 2));

        const validItems = orderItems.filter(item => item.productId && item.quantity > 0);

        console.log("After filtering items:", JSON.stringify(validItems, null, 2));
        console.log("Is draft mode:", isDraft);

        // Only require products for regular orders, not for drafts
        if (validItems.length === 0 && !isDraft) {
            toast.error("At least one product is required");
            return;
        }

        // Only check stock if not saving as draft
        if (!isDraft) {
            // Check for quantities exceeding stock or zero quantities
            const stockErrors = validItems.some(item => {
                const product = filteredProducts.find((p: Product) => p.id === item.productId);
                const stock = product?.stock || 0;

                // In edit mode, we need to account for the existing order items
                if (isEditMode) {
                    // Find the original item in the existing order
                    const originalItem = existingOrder.items.find((i: any) => i.productId === item.productId);
                    const originalQuantity = originalItem ? originalItem.quantity : 0;

                    // We only care about increases in quantity
                    const quantityIncrease = item.quantity - originalQuantity;
                    if (quantityIncrease > 0 && quantityIncrease > stock) {
                        toast.error(`Insufficient stock for ${item.productName}. Available: ${stock}`);
                        return true;
                    }
                    return false;
                } else {
                    // Normal validation for new orders
                    if (item.quantity > stock) {
                        toast.error(`Insufficient stock for ${item.productName}. Available: ${stock}`);
                        return true;
                    }
                    // Check for zero quantity items
                    if (item.quantity === 0) {
                        toast.error(`Quantity for ${item.productName} cannot be zero. Please set a quantity or remove the item.`);
                        return true;
                    }
                    return false;
                }
            });

            if (stockErrors) {
                return;
            }

            // Check for products with zero stock but quantity > 0 (allowed but warn)
            const zeroStockItems = validItems.filter(item => {
                const product = filteredProducts.find((p: Product) => p.id === item.productId);
                return (product?.stock || 0) === 0 && item.quantity > 0;
            });

            if (zeroStockItems.length > 0) {
                const confirmZeroStock = window.confirm(
                    `The following items have no stock in this branch:\n${zeroStockItems.map(item => item.productName).join("\n")}\n\nDo you want to continue with the order?`
                );
                if (!confirmZeroStock) {
                    return;
                }
            }
        }

        setIsSubmitting(true);

        try {
            // Format the order data
            const orderData = {
                id: isEditMode ? existingOrder.id : undefined,
                tenantId,
                branchId: selectedBranchId,
                memoNo,
                date: new Date(date).toISOString(),
                customerId: customerId || null,
                subTotal: validItems.length > 0 ? subTotal : 0,
                totalAmount: validItems.length > 0 ? subTotal : 0,
                paidAmount: validItems.length > 0 ? paidAmount : 0,
                dueAmount: validItems.length > 0 ? dueAmount : 0,
                paymentMethod,
                paymentNo: paymentNo || null,
                // Include previous due information only if paying previous due and there is a due amount
                // But don't affect the current order's paid amount since due payment is a separate transaction
                previousDue: payPreviousDue && previousDue > 0 ? previousDue : 0,
                previousDuePaid: payPreviousDue && previousDue > 0,
                previousDuePaymentAmount: payPreviousDue && previousDue > 0 ? previousDuePaymentAmount : 0,
                // Due payment is a separate transaction, so we don't need to calculate an effective paid amount
                // The paid amount is used directly for the current order
                extCommission,
                extCommissionType,
                remarks: remarks || null,
                // For edit mode, preserve the draft status if it's already a draft
                status: isEditMode && existingOrder.status === "draft" ? "draft" : (isDraft ? "draft" : "completed"),
                // Set payment status based on the paid amount relative to the total
                // If paid amount covers the total, it's "paid"
                // If paid amount is greater than 0 but less than total, it's "partial"
                // Otherwise, it's "unpaid"
                paymentStatus: validItems.length > 0 ?
                    (paidAmount >= totalAfterCommission || Math.abs(totalAfterCommission - paidAmount) <= 0.01 ? "paid" : (paidAmount > 0 ? "partial" : "unpaid")) :
                    "unpaid",
                // For draft orders, include all items with a productId, even if quantity is 0
                items: isDraft ?
                    orderItems.filter(item => item.productId).map(item => ({
                        productId: item.productId,
                        quantity: item.quantity || 0, // Ensure quantity is at least 0
                        unitPrice: item.unitPrice || 0, // Ensure unitPrice is at least 0
                        discountPercentage: item.discountPercentage || 0,
                        total: item.total || 0 // Ensure total is at least 0
                    })) :
                    validItems.map(item => ({
                        productId: item.productId,
                        quantity: item.quantity,
                        unitPrice: item.unitPrice,
                        discountPercentage: item.discountPercentage,
                        total: item.total
                    }))
            };

            // Make the API request
            const apiUrl = isEditMode
                ? `/api/sales/${existingOrder.id}/edit`
                : '/api/sales';

            console.log(`Submitting order with status: ${orderData.status}, items: ${orderData.items.length}`);
            console.log(`Payment details: paidAmount=${orderData.paidAmount}, dueAmount=${orderData.dueAmount}, totalAmount=${orderData.totalAmount}, paymentStatus=${orderData.paymentStatus}`);

            if (isEditMode) {
                console.log(`Editing existing order: ${existingOrder.id}, original status: ${existingOrder.status}`);
            }

            const response = await fetch(apiUrl, {
                method: isEditMode ? 'PUT' : 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(orderData),
            });

            if (!response.ok) {
                const errorData = await response.json();
                console.error('Error response:', errorData);

                // Handle specific error cases
                if (errorData.message && errorData.message.includes('Insufficient stock')) {
                    throw new Error(`${errorData.message}`);
                } else {
                    throw new Error(errorData.message || "Failed to save the sale");
                }
            }

            const result = await response.json();

            // Show different success message based on whether it's a draft or regular order
            if (isDraft) {
                toast.success("Draft order saved successfully");
            } else {
                toast.success(isEditMode ? "Sale updated successfully" : "Sale created successfully");
            }

            // Show loading overlay with message about redirecting to invoice
            setShowLoadingOverlay(true);

            // Store the order ID for redirection
            setRedirectInfo({
                id: result.id,
                isRedirecting: true
            });

            // Refresh router to update data
            router.refresh();
        } catch (error: any) {
            console.error("Error saving sale:", error);
            toast.error(error.message || "An error occurred while saving the sale");
        } finally {
            setIsSubmitting(false);
        }
    };

    // Handle direct payment of customer due
    const handleDirectDuePayment = async () => {
        if (!customerId || previousDue <= 0) {
            toast.error("No customer with due amount selected");
            return;
        }

        if (previousDuePaymentAmount <= 0) {
            toast.error("Please enter a valid payment amount");
            return;
        }

        if (previousDuePaymentAmount > previousDue) {
            toast.error(`Cannot pay more than the previous due amount: ${formatCurrency(previousDue)}`);
            return;
        }

        // Confirm with the user
        const confirmPayment = window.confirm(`Are you sure you want to pay ${formatCurrency(previousDuePaymentAmount)} of the due amount for ${customerName}?`);
        if (!confirmPayment) {
            return;
        }

        setIsProcessingDuePayment(true);

        try {
            // Prepare payment data
            const paymentData = {
                tenantId,
                customerId,
                amount: previousDuePaymentAmount,
                paymentMethod: "cash", // Default to cash
                paymentReference: "",
                notes: `Due payment for ${customerName}`,
                date: new Date().toISOString()
            };

            // Submit the payment
            const response = await fetch('/api/payments', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(paymentData),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || "Failed to process payment");
            }

            // Show success message
            toast.success(`Payment of ${formatCurrency(previousDuePaymentAmount)} processed successfully`);

            // Reset the previous due payment amount
            setPreviousDuePaymentAmount(0);
            setPayPreviousDue(false);

            // Refresh customer data to update the due amount
            await refreshCustomers();
        } catch (error: any) {
            console.error("Error processing payment:", error);
            toast.error(error.message || "An error occurred while processing the payment");
        } finally {
            setIsProcessingDuePayment(false);
        }
    };

    // Handle cancel
    const handleCancel = () => {
        // Reset ext commission type to percentage before navigating away
        setExtCommissionType("percentage");
        router.push("/dashboard/sales");
    };

    // Filter products based on search term for a specific row
    const filterProducts = (id: string, searchTerm: string) => {
        if (!selectedBranchId) {
            setFilteredProductsByRow(prev => ({ ...prev, [id]: [] }));
            return;
        }

        // Use the current filteredProducts which has the correct stock information for the selected branch
        // This allows selecting products with zero stock but ensures we have the latest stock data
        const branchProducts = filteredProducts;

        if (searchTerm.trim() === '') {
            setFilteredProductsByRow(prev => ({ ...prev, [id]: branchProducts }));
            return;
        }

        const term = searchTerm.toLowerCase();
        const filtered = branchProducts.filter((product: Product) => {
            // Search by name
            if (product.name.toLowerCase().includes(term)) {
                return true;
            }

            // Search by code
            if (product.code && product.code.toLowerCase().includes(term)) {
                return true;
            }

            // Search by category
            if (product.category?.name && product.category.name.toLowerCase().includes(term)) {
                return true;
            }

            // Search by vendor
            if (product.vendor?.name && product.vendor.name.toLowerCase().includes(term)) {
                return true;
            }

            return false;
        });

        setFilteredProductsByRow(prev => ({ ...prev, [id]: filtered }));
    };

    // Handle click outside to close dropdowns
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            // Close product dropdowns
            Object.entries(productDropdownRefs.current).forEach(([id, ref]) => {
                if (ref && !ref.contains(event.target as Node)) {
                    setShowProductDropdowns(prev => ({ ...prev, [id]: false }));
                }
            });

            // Close payment method dropdown
            if (paymentMethodDropdownRef.current && !paymentMethodDropdownRef.current.contains(event.target as Node)) {
                setShowPaymentMethodDropdown(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Handle product selection
    const handleProductChange = (id: string, productId: string) => {
        // If no branch is selected, show error and return
        if (!selectedBranchId) {
            toast.error("Please select a branch first");
            return;
        }
        if (!productId) {
            updateOrderItem(id, {
                productId: "",
                productName: "",
                unitPrice: 0,
                total: 0,
                code: "",
                stock: 0,
                quantityError: undefined
            });
            setProductSearches(prev => ({ ...prev, [id]: "" }));
            return;
        }

        // Use filteredProducts which has the correct stock information for the selected branch
        const selectedProduct = filteredProducts.find((p: Product) => p.id === productId);
        if (selectedProduct) {
            // Get the stock directly from the filtered product which has the correct branch stock
            const stock = selectedProduct.stock || 0;
            // Get current quantity
            const currentItem = orderItems.find(item => item.id === id);
            let quantity = 0; // Default to 0 to avoid undefined

            // If stock is zero, set quantity to 0 and show warning
            if (stock === 0) {
                quantity = 0;
                toast.warning(`${selectedProduct.name} has no stock available in this branch`);
            }
            // If there's stock available, set quantity to 1
            else if (stock > 0) {
                // Always set to 1 when selecting a product with stock
                quantity = 1;
            }
            // Ensure quantity doesn't exceed stock (fallback)
            else if (currentItem && currentItem.quantity > stock) {
                quantity = stock;
            }

            const updates = {
                productId,
                productName: selectedProduct.name,
                unitPrice: selectedProduct.sellingPrice,
                discountPercentage: selectedProduct.discount || 0,
                code: selectedProduct.code || "",
                stock: stock,
                quantity: quantity,
                quantityError: undefined
            };

            // Calculate the total
            const unitPrice = selectedProduct.sellingPrice;
            const discountPercentage = selectedProduct.discount || 0;

            const totalBeforeDiscount = quantity * unitPrice;
            const discountAmount = totalBeforeDiscount * (discountPercentage / 100);
            const total = totalBeforeDiscount - discountAmount;

            // Update the search field to show the selected product
            let displayText = selectedProduct.name;
            if (selectedProduct.code) {
                displayText += ` (${selectedProduct.code})`;
            }
            setProductSearches(prev => ({ ...prev, [id]: displayText }));

            // Close the dropdown
            setShowProductDropdowns(prev => ({ ...prev, [id]: false }));

            updateOrderItem(id, {
                ...updates,
                total: Number(total.toFixed(2))
            });

            // Set the paid amount to match the product price when a product is selected
            // Only do this if we're not in edit mode and this is the first product being added
            if (!isEditMode) {
                // Check if this is the first product with a value
                const hasOtherProducts = orderItems.some(item =>
                    item.id !== id && item.productId && item.total > 0
                );

                if (!hasOtherProducts) {
                    // This is the first product, set paid amount to match the product price
                    setPaidAmount(total);
                }
            }
        }
    };

    // Effect to update paid amount when total amount changes
    useEffect(() => {
        // Only auto-update paid amount if we're not in edit mode
        // This ensures we don't override existing paid amounts when editing
        if (!isEditMode) {
            // Set paid amount equal to the total after commission
            setPaidAmount(totalAfterCommission);
            // Since we're setting paid amount to the full amount, due amount should be 0
            // The dueAmount variable will be automatically recalculated
        }
    }, [totalAfterCommission, isEditMode]);

    // Effect to ensure ext commission type is always set to percentage by default
    useEffect(() => {
        // If ext commission type is not set, set it to percentage
        if (!extCommissionType) {
            setExtCommissionType("percentage");
        }
    }, [extCommissionType]);

    // Previous due payment is now a separate transaction, so we don't need to allocate paid amount
    useEffect(() => {
        if (!payPreviousDue && previousDuePaymentAmount > 0) {
            // If previous due payment is disabled, reset the previous due payment amount
            setPreviousDuePaymentAmount(0);
            console.log("Previous due payment disabled: Reset previous due payment amount to 0");
        }
    }, [payPreviousDue, previousDuePaymentAmount]);

    // Handle redirection to invoice page after loading animation completes
    useEffect(() => {
        if (redirectInfo.isRedirecting && redirectInfo.id) {
            // Redirect to the invoice details page after a short delay
            const redirectTimer = setTimeout(() => {
                // Only redirect if we're still in redirecting state
                // This prevents redirection if the user has navigated away or canceled
                if (redirectInfo.isRedirecting) {
                    router.push(`/dashboard/sales/${redirectInfo.id}`);
                }
            }, 1000); // Delay to allow the loading animation to be visible

            return () => clearTimeout(redirectTimer);
        }
    }, [redirectInfo, router]);

    // Function to handle the "Create Memo" action
    const handleCreateMemo = () => {
        if (!isSubmitting) {
            // Directly call the handleSubmit function without an event
            // The form's onSubmit handler will prevent default behavior
            const form = document.querySelector('form');
            if (form) {
                form.dispatchEvent(new Event('submit', { cancelable: true }));
            }
        }
    };

    return (
        <form onSubmit={handleSubmit} className="bg-sky-100 p-4 rounded-md" suppressHydrationWarning={true}>
            {/* Keyboard Shortcuts Component */}
            <KeyboardShortcuts
                customerInputRef={customerInputRef}
                productInputRef={firstProductInputRef}
                onCreateMemo={handleCreateMemo}
            />

            {/* Loading Overlay - Only render when needed */}
            <LoadingOverlay
                isVisible={showLoadingOverlay}
                message="Sale created successfully! Redirecting to invoice..."
                onAnimationComplete={() => setShowLoadingOverlay(false)}
            />

            {/* Customer Add Dialog */}
            <Dialog open={isCustomerDialogOpen} onOpenChange={setIsCustomerDialogOpen}>
                <DialogContent className="sm:max-w-[500px]">
                    <DialogHeader>
                        <DialogTitle>Add New Customer</DialogTitle>
                        <DialogDescription>
                            Create a new customer to add to your sales.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div className="grid gap-2">
                                <Label htmlFor="name">Customer Name *</Label>
                                <Input
                                    id="name"
                                    name="name"
                                    value={newCustomer.name}
                                    onChange={handleCustomerInputChange}
                                    placeholder="Enter customer name"
                                />
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="code">Customer Code</Label>
                                <Input
                                    id="code"
                                    name="code"
                                    value={newCustomer.code}
                                    onChange={handleCustomerInputChange}
                                    placeholder="Optional unique code"
                                />
                            </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div className="grid gap-2">
                                <Label htmlFor="phone">Phone Number</Label>
                                <Input
                                    id="phone"
                                    name="phone"
                                    value={newCustomer.phone}
                                    onChange={handleCustomerInputChange}
                                    placeholder="Phone number"
                                />
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="email">Email</Label>
                                <Input
                                    id="email"
                                    name="email"
                                    type="email"
                                    value={newCustomer.email}
                                    onChange={handleCustomerInputChange}
                                    placeholder="Email address"
                                />
                            </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div className="grid gap-2">
                                <Label htmlFor="type">Customer Type</Label>
                                <Select
                                    name="type"
                                    value={newCustomer.type}
                                    onValueChange={(value) => setNewCustomer(prev => ({ ...prev, type: value }))}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="retail">Retail</SelectItem>
                                        <SelectItem value="wholesale">Wholesale</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="creditLimit">Credit Limit</Label>
                                <Input
                                    id="creditLimit"
                                    name="creditLimit"
                                    type="number"
                                    value={newCustomer.creditLimit.toString()}
                                    onChange={handleCustomerInputChange}
                                    placeholder="0.00"
                                />
                            </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div className="grid gap-2">
                                <Label htmlFor="address">Address</Label>
                                <Textarea
                                    id="address"
                                    name="address"
                                    value={newCustomer.address}
                                    onChange={handleCustomerInputChange}
                                    placeholder="Customer address"
                                    rows={2}
                                />
                            </div>

                            <div className="grid gap-2">
                                <Label htmlFor="notes">Notes</Label>
                                <Textarea
                                    id="notes"
                                    name="notes"
                                    value={newCustomer.notes}
                                    onChange={handleCustomerInputChange}
                                    placeholder="Additional notes"
                                    rows={2}
                                />
                            </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <div className="grid gap-2">
                                <Label htmlFor="initialDue">Initial Due Amount</Label>
                                <Input
                                    id="initialDue"
                                    name="initialDue"
                                    type="number"
                                    value={newCustomer.initialDue.toString()}
                                    onChange={handleCustomerInputChange}
                                    placeholder="0.00"
                                    disabled={newCustomer.advancedPayment > 0}
                                />
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="advancedPayment">Advanced Payment</Label>
                                <Input
                                    id="advancedPayment"
                                    name="advancedPayment"
                                    type="number"
                                    value={newCustomer.advancedPayment?.toString() || "0"}
                                    onChange={handleCustomerInputChange}
                                    placeholder="0.00"
                                    disabled={newCustomer.initialDue > 0}
                                />
                            </div>
                        </div>
                        <div className="grid gap-2">
                            <Label htmlFor="extraCommission">Extra Commission (%)</Label>
                            <Input
                                id="extraCommission"
                                name="extraCommission"
                                type="number"
                                value={newCustomer.extraCommission?.toString() || "0"}
                                onChange={handleCustomerInputChange}
                                placeholder="0.00"
                            />
                            <p className="text-xs text-gray-500">Additional commission percentage for this customer</p>
                        </div>

                    </div>
                    <DialogFooter>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => setIsCustomerDialogOpen(false)}
                            className="cursor-pointer"
                        >
                            Cancel
                        </Button>
                        <Button
                            type="button"
                            onClick={handleAddCustomer}
                            disabled={isSubmittingCustomer}
                            className="cursor-pointer"
                        >
                            {isSubmittingCustomer ? 'Saving...' : 'Save Customer'}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 bg-white py-2 px-4 shadow-sm">
                {/* Customer Selection */}
                <div suppressHydrationWarning={true} className="flex items-center">
                    <label className="block text-xs font-medium w-[120px] text-gray-700">Customer:</label>
                    <div className="flex-1">
                        <div className="flex items-center gap-2">
                            <div className="relative flex-1" ref={customerDropdownRef}>
                                <div className="flex items-center w-full px-2 py-1 border border-gray-200 rounded-md shadow-sm focus-within:outline-none focus-within:ring-blue-500 focus-within:border-blue-500 bg-gray-50 h-8">
                                    <Search className="h-3 w-3 text-gray-500 mr-2" />
                                    <input
                                        type="text"
                                        placeholder="Search by name or code..."
                                        value={customerSearch}
                                        onChange={(e) => {
                                            setCustomerSearch(e.target.value);
                                            setShowCustomerDropdown(true);
                                        }}
                                        onFocus={() => setShowCustomerDropdown(true)}
                                        className="w-full outline-none bg-transparent text-xs h-5"
                                        ref={customerInputRef}
                                    />
                                </div>

                                {showCustomerDropdown && (
                                    <div className="absolute z-30 left-0 w-[200px] mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                                        {filteredCustomers.length > 0 ? (
                                            filteredCustomers.map((customer) => (
                                                <div
                                                    key={customer.id}
                                                    className="px-3 py-2 hover:bg-gray-100 cursor-pointer text-xs"
                                                    onClick={() => {
                                                        handleCustomerChange(customer.id);
                                                    }}
                                                >
                                                    <div className="">
                                                        <div className="font-medium">
                                                            <Check
                                                                className={cn(
                                                                    "mr-1 h-1 w-3",
                                                                    customerId === customer.id ? "opacity-100" : "opacity-0"
                                                                )}
                                                            />
                                                            {customer.name}
                                                        </div>
                                                        {customer.code && (
                                                            <span className="text-gray-500">{customer.code}</span>
                                                        )}
                                                    </div>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="px-3 py-2 text-gray-500 text-xs">No customer found</div>
                                        )}
                                    </div>
                                )}
                            </div>

                            <div className="flex gap-1">
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="icon"
                                    onClick={() => setIsCustomerDialogOpen(true)}
                                    className="h-8 w-8 p-0 bg-green-50 hover:bg-green-100 border-green-200"
                                    title="Add new customer"
                                >
                                    <UserPlus className="h-3.5 w-3.5 text-green-600" />
                                </Button>
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="icon"
                                    onClick={refreshCustomers}
                                    disabled={isRefreshingCustomers}
                                    className="h-8 w-8 p-0 bg-blue-50 hover:bg-blue-100 border-blue-200"
                                    title="Refresh customer list"
                                >
                                    <RefreshCw className={`h-3.5 w-3.5 text-blue-600 ${isRefreshingCustomers ? 'animate-spin' : ''}`} />
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>


                {/* Customer Name */}
                <div suppressHydrationWarning={true} className="flex items-center">
                    <label className="block text-xs font-medium w-[120px] text-gray-700">Customer Name:</label>
                    <div className="flex-1">
                        <Input
                            value={customerName}
                            onChange={(e) => setCustomerName(e.target.value)}
                            placeholder={customerId ? "Selected customer name" : "Walk-in customer"}
                            className="bg-gray-50 w-full h-8 text-xs border-gray-200"
                            suppressHydrationWarning={true}
                        />
                    </div>
                </div>

                {/* Phone Number */}
                <div suppressHydrationWarning={true} className="flex items-center">
                    <label className="block text-xs font-medium w-[120px] text-gray-700">Phone No:</label>
                    <div className="flex-1">
                        <Input
                            value={customerPhone}
                            onChange={(e) => setCustomerPhone(e.target.value)}
                            className="bg-gray-50 w-full h-8 text-xs border-gray-200"
                            suppressHydrationWarning={true}
                        />
                    </div>
                </div>
            </div>


            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-2 bg-white py-2 px-4 shadow-sm">
                {/* Branch Selection */}
                <div suppressHydrationWarning={true} className="flex items-center">
                    <label className="block text-xs font-medium w-[120px] text-gray-700">Branch:</label>
                    <div className="flex-1">
                        {sessionBranchId ? (
                            <div className="bg-gray-50 w-full h-8 text-xs px-3 py-1 border border-gray-200 rounded-md flex items-center">
                                {selectedBranchName || "Your assigned branch"}
                            </div>
                        ) : (
                            <Select
                                value={selectedBranchId}
                                onValueChange={handleBranchChange}
                                disabled={isEditMode}
                            >
                                <SelectTrigger className="bg-gray-50 w-full h-8 text-xs px-3 border-gray-200">
                                    <SelectValue placeholder="Select Branch">
                                        {selectedBranchName || "Select Branch"}
                                    </SelectValue>
                                </SelectTrigger>
                                <SelectContent className="max-w-[250px]">
                                    {branches
                                        .filter(branch => branch.isActive !== false)
                                        .map((branch) => (
                                            <SelectItem key={branch.id} value={branch.id} className="py-2 text-xs">
                                                {branch.name} ({branch.code}) {branch.isMain ? "(Main)" : ""}
                                            </SelectItem>
                                        ))
                                    }
                                </SelectContent>
                            </Select>
                        )}
                    </div>
                </div>

                {/* Date Selection */}
                <div suppressHydrationWarning={true} className="flex items-center">
                    <label className="block text-xs font-medium w-[120px] text-gray-700">Date:</label>
                    <div className="flex-1">
                        <Input
                            type="date"
                            value={date}
                            onChange={(e) => setDate(e.target.value)}
                            className="bg-gray-50 w-full h-8 text-xs border-gray-200"
                            suppressHydrationWarning={true}
                        />
                    </div>
                </div>

                {/* Memo Number */}
                <div suppressHydrationWarning={true} className="flex items-center">
                    <label className="block text-xs font-medium w-[120px] text-gray-700">Memo No:</label>
                    <div className="flex-1">
                        <Input
                            type="text"
                            value={memoNo}
                            onChange={(e) => setMemoNo(e.target.value)}
                            className="bg-gray-50 w-full h-8 text-xs border-gray-200"
                            suppressHydrationWarning={true}
                        />
                    </div>
                </div>
            </div>

            {/* Show previous due information if customer has due */}
            {previousDue > 0 && (
                <div className="bg-amber-50 border border-amber-200 p-4 rounded mb-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="font-medium text-amber-800">Previous Due: {formatCurrency(previousDue)}</h3>
                            <p className="text-sm text-amber-700">This customer has a previous due amount</p>
                        </div>
                        <div className="flex items-center space-x-2">
                            <label className="flex items-center space-x-2 cursor-pointer">
                                <input
                                    type="checkbox"
                                    checked={payPreviousDue}
                                    onChange={handleTogglePreviousDue}
                                    className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                />
                                <span className="text-sm font-medium">Pay previous due now</span>
                            </label>
                        </div>
                    </div>

                    {payPreviousDue && (
                        <div className="mt-4">
                            <label className="block text-sm font-medium mb-1">Amount to Pay for Previous Due</label>
                            <div className="flex gap-2">
                                <div className="relative flex-1">
                                    <Input
                                        type="number"
                                        min="0"
                                        max={previousDue}
                                        value={previousDuePaymentAmount !== 0 ? previousDuePaymentAmount : ""}
                                        onChange={(e) => handlePreviousDueAmountChange(Number(e.target.value))}
                                        className="bg-white text-lg font-bold pr-16"
                                    />
                                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                        <span className="text-gray-500">৳</span>
                                    </div>
                                </div>
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleDirectDuePayment}
                                    disabled={isProcessingDuePayment || previousDuePaymentAmount <= 0}
                                    className="bg-green-50 hover:bg-green-100 text-green-700 border-green-200"
                                >
                                    {isProcessingDuePayment ? (
                                        <>
                                            <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                                            Processing...
                                        </>
                                    ) : (
                                        "Pay Due"
                                    )}
                                </Button>
                            </div>

                            <div className="flex flex-col gap-1 mt-2">
                                <p className="text-sm text-gray-600">
                                    <strong>Previous Due:</strong> {formatCurrency(previousDue)}
                                </p>
                                <p className="text-sm text-green-700">
                                    <strong>Amount Being Paid:</strong> {formatCurrency(previousDuePaymentAmount)}
                                </p>
                                <p className="text-sm text-gray-600">
                                    <strong>Remaining After Payment:</strong> {formatCurrency(previousDue - previousDuePaymentAmount)}
                                </p>
                                <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
                                    <p className="text-xs text-blue-700">
                                        <strong>Important:</strong> This previous due payment is a separate transaction from the current order.
                                        The customer's due balance will be reduced by {formatCurrency(previousDuePaymentAmount)}.
                                    </p>
                                    <p className="text-xs text-blue-700 mt-1">
                                        <strong>Note:</strong> To process the payment immediately, enter the amount and click the "Pay Due" button.
                                        This will create a payment record without requiring you to complete the current order.
                                    </p>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            )}

            {/* Show advanced payment information if customer has advanced payment */}
            {customerId && findSelectedCustomer(customerId) && (findSelectedCustomer(customerId)?.currentBalance ?? 0) < 0 && (
                <div className="bg-green-50 border border-green-200 p-4 rounded mb-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="font-medium text-green-800">
                                Advanced Payment: {formatCurrency(Math.abs(findSelectedCustomer(customerId)?.currentBalance ?? 0))}
                            </h3>
                            <p className="text-sm text-green-700">
                                This customer has an advanced payment balance that will be used for this order
                            </p>
                        </div>
                    </div>
                    <div className="mt-2">
                        <p className="text-xs text-green-700">
                            {paidAmount > 0 ?
                                `${formatCurrency(paidAmount)} will be deducted from the advanced payment balance.` :
                                "No amount will be deducted from the advanced payment balance."}
                        </p>
                        <p className="text-xs text-green-700 mt-1">
                            Remaining advanced payment after this order: {formatCurrency(Math.abs(findSelectedCustomer(customerId)?.currentBalance ?? 0) - paidAmount)}
                        </p>
                    </div>
                </div>
            )}

            {!selectedBranchId ? (
                <div className="bg-amber-100 border-2 border-amber-300 p-6 rounded-lg mb-6 text-center" suppressHydrationWarning={true}>
                    <div className="flex flex-col items-center justify-center">
                        <div>
                            <h3 className="font-bold text-lg text-amber-800 mb-2">Branch Selection Required</h3>
                            <p className="text-amber-700">Please select a branch above to view products and their stock</p>
                        </div>
                    </div>
                </div>
            ) : (
                <div className="mb-2 overflow-x-auto max-h-[280px] overflow-y-auto" suppressHydrationWarning={true}>
                    <table className="w-full bg-white rounded-md">
                        <thead className="bg-blue-100 z-20 sticky top-0">
                            <tr>
                                <th className="py-1 px-1 text-center text-xs">SL</th>
                                <th className="py-1 px-1 text-center text-xs">Book Name</th>
                                <th className="py-1 px-1 text-center text-xs">Qty</th>
                                <th className="py-1 px-1 text-center text-xs">Stock</th>
                                <th className="py-1 px-1 text-center text-xs">Sale Rate</th>
                                <th className="py-1 px-1 text-center text-xs">Discount(%)</th>
                                <th className="py-1 px-1 text-center text-xs">Total</th>
                                <th className="py-1 px-1 text-center text-xs">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {orderItems.map((item, index) => (
                                <tr key={item.id} className="border-b" suppressHydrationWarning={true}>
                                    <td className="py-2 px-2 text-center">{index + 1}</td>
                                    <td className="py-2 px-2" suppressHydrationWarning={true}>
                                        <div
                                            className="relative"
                                            ref={(el) => { productDropdownRefs.current[item.id] = el; }}
                                        >
                                            <div className={`flex items-center w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus-within:outline-none focus-within:ring-blue-500 focus-within:border-blue-500 ${selectedBranchId ? 'bg-white' : 'bg-gray-100'} h-8`}>
                                                <Search className="h-4 w-4 text-gray-500 mr-2" />
                                                <input
                                                    type="text"
                                                    placeholder={selectedBranchId ? "Search book..." : "Select branch first"}
                                                    value={productSearches[item.id] || (item.productName ? item.productName : '')}
                                                    onChange={(e) => {
                                                        if (!selectedBranchId) {
                                                            toast.error("Please select a branch first");
                                                            return;
                                                        }
                                                        const value = e.target.value;
                                                        setProductSearches(prev => ({ ...prev, [item.id]: value }));
                                                        filterProducts(item.id, value);
                                                        setShowProductDropdowns(prev => ({ ...prev, [item.id]: true }));
                                                    }}
                                                    onFocus={() => {
                                                        if (!selectedBranchId) {
                                                            toast.error("Please select a branch first");
                                                            return;
                                                        }
                                                        // Always refresh the filtered products for this row when focused
                                                        // This ensures we always have the latest stock information
                                                        setFilteredProductsByRow(prev => ({ ...prev, [item.id]: filteredProducts }));
                                                        setShowProductDropdowns(prev => ({ ...prev, [item.id]: true }));
                                                    }}
                                                    className="w-full outline-none bg-transparent text-xs h-5"
                                                    disabled={!selectedBranchId}
                                                    ref={index === 0 ? firstProductInputRef : undefined}
                                                />
                                            </div>

                                            {showProductDropdowns[item.id] && selectedBranchId && (

                                                <div className="fixed z-10 mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto min-w-[300px]"
                                                    style={{
                                                        width: productDropdownRefs.current[item.id]?.offsetWidth || 'auto',
                                                        top: productDropdownRefs.current[item.id] ?
                                                            productDropdownRefs.current[item.id]?.getBoundingClientRect()?.bottom + 'px' : 'auto',
                                                        left: productDropdownRefs.current[item.id] ?
                                                            productDropdownRefs.current[item.id]?.getBoundingClientRect()?.left + 'px' : 'auto'
                                                    }}
                                                >
                                                    {filteredProductsByRow[item.id]?.length > 0 ? (
                                                        filteredProductsByRow[item.id].map((product) => (
                                                            product.isActive !== false && (
                                                                <div
                                                                    key={product.id}
                                                                    className="px-3 py-3 hover:bg-gray-100 cursor-pointer text-xs"
                                                                    onClick={() => {
                                                                        handleProductChange(item.id, product.id);
                                                                    }}
                                                                >
                                                                    <div className="flex justify-between items-center">
                                                                        <div className="font-medium flex items-center">
                                                                            <Check
                                                                                className={cn(
                                                                                    "mr-1 h-3 w-3",
                                                                                    item.productId === product.id ? "opacity-100" : "opacity-0"
                                                                                )}
                                                                            />
                                                                            {product.name}
                                                                        </div>
                                                                        <div>
                                                                            {product.code && (
                                                                                <span className="text-gray-500 text-xs font-mono">{product.code}</span>
                                                                            )}
                                                                        </div>
                                                                    </div>
                                                                    <div className="flex justify-between mt-1 text-xs">
                                                                        <span className={`${(product.stock || 0) === 0 ? 'text-red-500 font-semibold' : 'text-green-600 font-semibold'}`}>
                                                                            Stock: {product.stock || 0}
                                                                        </span>
                                                                        <span className="text-gray-500">
                                                                            Price: {formatCurrency(product.sellingPrice)}
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            )

                                                        ))
                                                    ) : (
                                                        <div className="px-3 py-2 text-gray-500 text-xs">
                                                            No books found matching your search
                                                        </div>
                                                    )}
                                                </div>
                                            )}
                                        </div>
                                    </td>
                                    <td className="py-2 px-2" suppressHydrationWarning={true}>
                                        <div className="flex flex-col" suppressHydrationWarning={true}>
                                            <Input
                                                type="number"
                                                min="1"
                                                max={item.stock || 1}
                                                value={item.quantity}
                                                onChange={(e) => {
                                                    if (!selectedBranchId) {
                                                        toast.error("Please select a branch first");
                                                        return;
                                                    }
                                                    updateOrderItem(item.id, { quantity: Number(e.target.value) });
                                                }}
                                                className={`text-center h-6 text-xs w-16 ${item.quantityError ? 'border-red-500' : ''} ${!selectedBranchId ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}`}
                                                disabled={!selectedBranchId || !item.productId}
                                                suppressHydrationWarning={true}
                                            />
                                            {item.quantityError && (
                                                <span className="text-xs text-red-500 mt-1">{item.quantityError}</span>
                                            )}
                                        </div>
                                    </td>

                                    <td className="py-2 px-2" suppressHydrationWarning={true}>
                                        <div className={`text-center flex items-center justify-center h-6 text-xs w-16 py-2 border border-gray-300 rounded-md ${selectedBranchId ? 'bg-white' : 'bg-gray-100'}`}>
                                            {selectedBranchId ?
                                                (item.stock !== undefined ?
                                                    <span className={`${(item.stock || 0) === 0 ? 'text-red-500 font-semibold' : 'text-green-600 font-semibold'}`}>
                                                        {item.stock}
                                                    </span> : '0')
                                                : '—'}
                                        </div>
                                    </td>
                                    <td className="py-2 px-2" suppressHydrationWarning={true}>
                                        <Input
                                            type="number"
                                            step="0.01"
                                            value={item.unitPrice}
                                            onChange={(e) => {
                                                if (!selectedBranchId) {
                                                    toast.error("Please select a branch first");
                                                    return;
                                                }
                                                updateOrderItem(item.id, { unitPrice: Number(e.target.value) });
                                            }}
                                            className={`text-right h-6 text-xs w-24 ${!selectedBranchId || !item.productId ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}`}
                                            disabled={!selectedBranchId || !item.productId}
                                            suppressHydrationWarning={true}
                                        />
                                    </td>
                                    <td className="py-2 px-2" suppressHydrationWarning={true}>
                                        <Input
                                            type="number"
                                            min="0"
                                            max="100"
                                            step="0.01"
                                            value={item.discountPercentage}
                                            onChange={(e) => {
                                                if (!selectedBranchId) {
                                                    toast.error("Please select a branch first");
                                                    return;
                                                }
                                                updateOrderItem(item.id, { discountPercentage: Number(e.target.value) });
                                            }}
                                            className={`text-right h-6 text-xs w-20 ${!selectedBranchId || !item.productId ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}`}
                                            disabled={!selectedBranchId || !item.productId}
                                            suppressHydrationWarning={true}
                                        />
                                    </td>
                                    <td className="py-2 px-2" suppressHydrationWarning={true}>
                                        <Input
                                            type="text"
                                            value={item.total.toFixed(2)}
                                            readOnly
                                            className="text-right h-6 text-xs w-24 bg-gray-50"
                                            suppressHydrationWarning={true}
                                        />
                                    </td>
                                    <td className="py-2 px-2 text-center">
                                        {orderItems.length > 1 && (
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => {
                                                    if (!selectedBranchId) {
                                                        toast.error("Please select a branch first");
                                                        return;
                                                    }
                                                    removeRow(item.id);
                                                }}
                                                className={`text-red-500 hover:text-red-700 hover:bg-red-50 h-6 w-8 p-0 ${!selectedBranchId || !item.productId ? 'opacity-50 cursor-not-allowed' : ''}`}
                                                disabled={!selectedBranchId || !item.productId}
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        )}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8 bg-white p-4 rounded-md shadow-sm">
                {/* First Column - Payment Information */}
                <div className="flex flex-col gap-3" suppressHydrationWarning={true}>
                    <div suppressHydrationWarning={true} className="flex items-center">
                        <label className="block text-xs font-medium w-[120px] text-gray-700">Remarks:</label>
                        <div className="flex-1">
                            <Textarea
                                value={remarks}
                                onChange={(e) => setRemarks(e.target.value)}
                                placeholder="Notes"
                                className="min-h-8 text-xs bg-gray-50 w-full border-gray-200"
                                suppressHydrationWarning={true}
                            />
                        </div>
                    </div>

                    <div suppressHydrationWarning={true} className="flex items-center">
                        <label className="block text-xs font-medium w-[120px] text-gray-700">Payment No:</label>
                        <div className="flex-1">
                            <Input
                                type="text"
                                value={paymentNo}
                                onChange={(e) => setPaymentNo(e.target.value)}
                                className="bg-gray-50 h-8 text-xs w-full border-gray-200"
                                suppressHydrationWarning={true}
                            />
                        </div>
                    </div>

                    <div suppressHydrationWarning={true} className="flex items-center">
                        <label className="block text-xs font-medium w-[120px] text-gray-700">Pay Mode:</label>
                        <div className="flex-1">
                            <div className="relative w-full" ref={paymentMethodDropdownRef}>
                                <div className="flex items-center w-full px-2 py-1 border border-gray-200 rounded-md shadow-sm focus-within:outline-none focus-within:ring-blue-500 focus-within:border-blue-500 bg-gray-50 h-8">
                                    <Search className="h-3 w-3 text-gray-500 mr-2" />
                                    <input
                                        type="text"
                                        placeholder="Payment..."
                                        value={paymentMethodSearch}
                                        onChange={(e) => {
                                            setPaymentMethodSearch(e.target.value);
                                            setShowPaymentMethodDropdown(true);
                                        }}
                                        onFocus={() => setShowPaymentMethodDropdown(true)}
                                        className="w-full outline-none bg-transparent text-xs h-5"
                                        readOnly
                                    />
                                </div>

                                {showPaymentMethodDropdown && (
                                    <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg overflow-y-auto">
                                        {isLoadingPaymentMethods ? (
                                            <div className="px-3 py-2 text-xs text-gray-500">
                                                Loading payment methods...
                                            </div>
                                        ) : paymentMethods.length > 0 ? (
                                            paymentMethods.map((method) => (
                                                <div
                                                    key={method.id}
                                                    className="px-3 py-2 hover:bg-gray-100 cursor-pointer text-xs"
                                                    onClick={() => {
                                                        setPaymentMethod(method.code);
                                                        setPaymentMethodSearch(method.name);
                                                        setShowPaymentMethodDropdown(false);
                                                    }}
                                                >
                                                    <div className="flex items-center justify-between">
                                                        <span className="font-medium">{method.name}</span>
                                                        <Check className={cn("h-3 w-3", paymentMethod === method.code ? "opacity-100" : "opacity-0")} />
                                                    </div>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="px-3 py-2 text-xs text-gray-500">
                                                No payment methods available
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="flex items-center justify-start mt-4 space-x-3" suppressHydrationWarning={true}>
                        <Button
                            type="submit"
                            className="bg-blue-600 h-8 text-xs hover:bg-blue-700 border-blue-700 cursor-pointer"
                            disabled={isSubmitting}
                        >
                            Save
                        </Button>
                        <Button
                            type="button"
                            onClick={(e) => handleSubmit(e as any, true)}
                            disabled={isSubmitting}
                            className="bg-amber-500 h-8 text-xs hover:bg-amber-600 border-amber-600 cursor-pointer text-white"
                        >
                            Save as Draft
                        </Button>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={handleCancel}
                            disabled={isSubmitting}
                            className="cursor-pointer h-8 text-xs"
                        >
                            Cancel
                        </Button>
                    </div>
                </div>

                {/* Second Column - Price Information */}
                <div className="flex flex-col gap-3" suppressHydrationWarning={true}>
                    <div suppressHydrationWarning={true} className="flex items-center">
                        <label className="block text-xs font-medium w-[120px] text-gray-700">Total Price:</label>
                        <div className="flex-1">
                            <Input
                                type="text"
                                value={subTotal.toFixed(2)}
                                readOnly
                                className="text-right bg-gray-50 h-8 text-xs w-full border-gray-200"
                                suppressHydrationWarning={true}
                            />
                        </div>
                    </div>

                    <div suppressHydrationWarning={true} className="flex items-center">
                        <label className="block text-xs font-medium w-[120px] text-gray-700">Ext Commission:</label>
                        <div className="flex-1 flex gap-2">
                            <div className="w-1/2">
                                <Select
                                    value={extCommissionType}
                                    onValueChange={(value) => setExtCommissionType(value as "fixed" | "percentage")}
                                    defaultValue="percentage"
                                >
                                    <SelectTrigger className="bg-gray-50 h-8 text-xs border-gray-200">
                                        <SelectValue placeholder="Type">
                                            {extCommissionType === "percentage" ? "Percentage (%)" : "Fixed"}
                                        </SelectValue>
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="percentage">Percentage (%)</SelectItem>
                                        <SelectItem value="fixed">Fixed</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="w-1/2">
                                <Input
                                    type="number"
                                    min="0"
                                    max={extCommissionType === "percentage" ? "100" : undefined}
                                    step="0.01"
                                    value={extCommission}
                                    onChange={(e) => setExtCommission(Number(e.target.value))}
                                    className="text-right bg-gray-50 h-8 text-xs w-full border-gray-200"
                                    suppressHydrationWarning={true}
                                />
                            </div>
                        </div>
                    </div>

                    <div suppressHydrationWarning={true} className="flex items-center">
                        <label className="block text-xs font-medium w-[120px] text-gray-700">Sub Total:</label>
                        <div className="flex-1">
                            <Input
                                type="text"
                                value={totalAfterCommission.toFixed(2)}
                                readOnly
                                className="text-right bg-gray-50 h-8 text-xs w-full border-gray-200"
                                suppressHydrationWarning={true}
                            />
                        </div>
                    </div>
                </div>

                {/* Third Column - Payment Details */}
                <div className="flex flex-col gap-3" suppressHydrationWarning={true}>
                    <div suppressHydrationWarning={true} className="flex items-center">
                        <label className="block text-xs font-medium w-[120px] text-gray-700">Net Bill:</label>
                        <div className="flex-1">
                            <Input
                                type="text"
                                value={netBill.toFixed(2)}
                                readOnly
                                className="text-right bg-gray-50 h-8 text-xs w-full border-gray-200"
                                suppressHydrationWarning={true}
                            />
                        </div>
                    </div>

                    <div suppressHydrationWarning={true} className="flex items-center">
                        <label className="block text-xs font-medium w-[120px] text-gray-700">Paid Amount:</label>
                        <div className="flex-1">
                            <Input
                                type="number"
                                step="0.01"
                                value={paidAmount}
                                onChange={(e) => setPaidAmount(Number(e.target.value))}
                                className="text-right bg-gray-50 h-8 text-xs w-full border-gray-200"
                                suppressHydrationWarning={true}
                            />
                        </div>
                    </div>

                    <div suppressHydrationWarning={true} className="flex items-center">
                        <label className="block text-xs font-medium w-[120px] text-gray-700">Dues:</label>
                        <div className="flex-1">
                            <Input
                                type="number"
                                step="0.01"
                                min="0"
                                max={totalAfterCommission}
                                value={dueAmount}
                                onChange={(e) => {
                                    const newDueAmount = Number(e.target.value);
                                    // Validate the due amount
                                    if (newDueAmount < 0) {
                                        toast.error("Due amount cannot be negative");
                                        return;
                                    }
                                    if (newDueAmount > totalAfterCommission) {
                                        toast.error(`Due amount cannot exceed total amount: ${formatCurrency(totalAfterCommission)}`);
                                        return;
                                    }
                                    // Calculate the new paid amount based on the due amount
                                    const newPaidAmount = Math.max(0, totalAfterCommission - newDueAmount);
                                    setPaidAmount(newPaidAmount);
                                }}
                                onClick={() => {
                                    // Show message when clicking on disabled field
                                    if (!customerId) {
                                        toast.info("Please select a customer to edit the dues amount");
                                    } else if (payPreviousDue && previousDue > 0) {
                                        toast.info("When paying previous due, the dues field is automatically calculated. Adjust the paid amount to control both current and previous due payments.");
                                    }
                                }}
                                // Make read-only when no customer is selected or when paying previous due
                                readOnly={!customerId || (payPreviousDue && previousDue > 0)}
                                className={`text-right bg-gray-50 h-8 text-xs w-full border-gray-200 ${!customerId || (payPreviousDue && previousDue > 0) ? 'cursor-not-allowed opacity-70' : ''
                                    }`}
                                suppressHydrationWarning={true}
                            />
                        </div>
                    </div>
                </div>
            </div>


        </form >
    );
}