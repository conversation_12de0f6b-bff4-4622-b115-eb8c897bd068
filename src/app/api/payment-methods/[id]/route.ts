import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { paymentMethods } from "@/db/schema";
import { auth } from "~/auth";
import { eq, and } from "drizzle-orm";
import { revalidatePath } from "next/cache";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;

    // Get tenant ID based on user role
    let tenantId = session.user.id;
    if (session.user.role === 'tenant_sale') {
      tenantId = session.user.tenantId || session.user.id;
    }

    // Fetch the payment method
    const method = await db.query.paymentMethods.findFirst({
      where: (paymentMethods, { eq, and }) => 
        and(
          eq(paymentMethods.id, id),
          eq(paymentMethods.tenantId, tenantId)
        ),
    });

    if (!method) {
      return NextResponse.json(
        { message: "Payment method not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      paymentMethod: method,
    });
  } catch (error) {
    console.error("Error fetching payment method:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Only tenant and admin users can update payment methods
    if (session.user.role !== 'tenant' && session.user.role !== 'admin') {
      return NextResponse.json({ message: "Forbidden" }, { status: 403 });
    }

    const { id } = params;
    const data = await request.json();

    // Get tenant ID
    let tenantId = session.user.id;
    if (session.user.role === 'admin' && data.tenantId) {
      tenantId = data.tenantId;
    }

    // Check if payment method exists and belongs to tenant
    const existingMethod = await db.query.paymentMethods.findFirst({
      where: (paymentMethods, { eq, and }) => 
        and(
          eq(paymentMethods.id, id),
          eq(paymentMethods.tenantId, tenantId)
        ),
    });

    if (!existingMethod) {
      return NextResponse.json(
        { message: "Payment method not found" },
        { status: 404 }
      );
    }

    // If setting as default, unset other defaults
    if (data.isDefault && !existingMethod.isDefault) {
      await db.update(paymentMethods)
        .set({ isDefault: false })
        .where(eq(paymentMethods.tenantId, tenantId));
    }

    // Update the payment method
    const updatedMethod = await db.update(paymentMethods)
      .set({
        name: data.name || existingMethod.name,
        description: data.description !== undefined ? data.description : existingMethod.description,
        isActive: data.isActive !== undefined ? data.isActive : existingMethod.isActive,
        isDefault: data.isDefault !== undefined ? data.isDefault : existingMethod.isDefault,
        updatedAt: new Date(),
      })
      .where(eq(paymentMethods.id, id))
      .returning();

    // Revalidate relevant pages
    revalidatePath("/dashboard/payment/payment-methods");
    revalidatePath("/dashboard/sales");
    revalidatePath("/dashboard/payments");

    return NextResponse.json({
      success: true,
      message: "Payment method updated successfully",
      paymentMethod: updatedMethod[0],
    });
  } catch (error) {
    console.error("Error updating payment method:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Same as PUT but for partial updates
  return PUT(request, { params });
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Only tenant and admin users can delete payment methods
    if (session.user.role !== 'tenant' && session.user.role !== 'admin') {
      return NextResponse.json({ message: "Forbidden" }, { status: 403 });
    }

    const { id } = params;

    // Get tenant ID
    let tenantId = session.user.id;

    // Check if payment method exists and belongs to tenant
    const existingMethod = await db.query.paymentMethods.findFirst({
      where: (paymentMethods, { eq, and }) => 
        and(
          eq(paymentMethods.id, id),
          eq(paymentMethods.tenantId, tenantId)
        ),
    });

    if (!existingMethod) {
      return NextResponse.json(
        { message: "Payment method not found" },
        { status: 404 }
      );
    }

    // TODO: Check if payment method is being used in any transactions
    // For now, we'll allow deletion

    // Delete the payment method
    await db.delete(paymentMethods)
      .where(eq(paymentMethods.id, id));

    // Revalidate relevant pages
    revalidatePath("/dashboard/payment/payment-methods");
    revalidatePath("/dashboard/sales");
    revalidatePath("/dashboard/payments");

    return NextResponse.json({
      success: true,
      message: "Payment method deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting payment method:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
