import { NextRequest, NextResponse } from "next/server";
import { auth } from "../../../../../auth";
import { db } from "@/lib/db";
import { refundReasons } from "@/db/schema";
import { eq } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";

// Schema for validating reason data
const reasonSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
});

// GET /api/refunds/reasons - Get all refund reasons
export async function GET(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    try {
      // Get all active refund reasons
      const allReasons = await db.query.refundReasons.findMany({
        orderBy: (reasons, { asc }) => [asc(reasons.name)],
      });

      // If no reasons found, return an empty array
      return NextResponse.json({
        success: true,
        reasons: allReasons || [],
      });
    } catch (dbError) {
      console.error("Database error fetching refund reasons:", dbError);

      // Return a fallback set of reasons if there's a database error
      const fallbackReasons = [
        {
          id: "1",
          name: "Damaged Product",
          description: "Product was damaged during handling or shipping",
          isActive: true
        },
        {
          id: "2",
          name: "Customer Dissatisfaction",
          description: "Customer was not satisfied with the product",
          isActive: true
        },
        {
          id: "3",
          name: "Wrong Product",
          description: "Customer received the wrong product",
          isActive: true
        },
        {
          id: "4",
          name: "Quality Issue",
          description: "Product has quality issues or defects",
          isActive: true
        },
        {
          id: "5",
          name: "Other",
          description: "Other reason for refund",
          isActive: true
        }
      ];

      return NextResponse.json({
        success: true,
        reasons: fallbackReasons,
        note: "Using fallback reasons due to database error"
      });
    }
  } catch (error) {
    console.error("Error fetching refund reasons:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch refund reasons",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// POST /api/refunds/reasons - Create a new refund reason
export async function POST(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Only admin and tenant users can create refund reasons
    if (session.user.role !== 'admin' && session.user.role !== 'tenant') {
      return NextResponse.json(
        { success: false, error: "Unauthorized. Only admin and tenant users can create refund reasons." },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await req.json();
    const validationResult = reasonSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid reason data",
          details: validationResult.error.format()
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Create the refund reason
    const reasonId = uuidv4();
    const result = await db.insert(refundReasons).values({
      id: reasonId,
      name: data.name,
      description: data.description || null,
      isActive: data.isActive,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    return NextResponse.json({
      success: true,
      message: "Refund reason created successfully",
      reason: result[0],
    });
  } catch (error) {
    console.error("Error creating refund reason:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create refund reason" },
      { status: 500 }
    );
  }
}
