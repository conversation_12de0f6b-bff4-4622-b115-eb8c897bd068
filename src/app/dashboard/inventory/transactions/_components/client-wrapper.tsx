'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { usePermissionCheck } from '@/lib/check-permissions';

interface Product {
    id: string;
    name: string;
    code: string | null;
    costPrice: number;
    sellingPrice?: number;
    unit?: string;
}

interface User {
    id: string;
    fullName: string;
}

interface Branch {
    id: string;
    name: string;
    code: string;
    isMain?: boolean;
}

interface InventoryTransaction {
    id: string;
    tenantId: string;
    productId: string;
    branchId: string;
    quantity: number;
    unitPrice: number;
    type: 'in' | 'out';
    date: string;
    notes: string | null;
    performedBy: string;
    createdAt: string;
    updatedAt: string;
    product?: Product;
    user?: User;
    branch?: Branch;
}

export default function ClientWrapper() {
    // Check if user has permission to view inventory transactions
    const { isLoading: permissionLoading } = usePermissionCheck('/dashboard/inventory', 'view');

    const router = useRouter();
    const [transactions, setTransactions] = useState<InventoryTransaction[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string>('');
    const [searchTerm, setSearchTerm] = useState<string>('');
    const [filteredTransactions, setFilteredTransactions] = useState<InventoryTransaction[]>([]);
    const [transactionTypeFilter, setTransactionTypeFilter] = useState<'all' | 'in' | 'out'>('all');

    useEffect(() => {
        fetchTransactions();
    }, []);

    useEffect(() => {
        filterTransactions();
    }, [searchTerm, transactionTypeFilter, transactions]);

    const fetchTransactions = async () => {
        try {
            setLoading(true);
            setError('');

            const response = await fetch('/api/inventory/transactions');

            if (!response.ok) {
                throw new Error('Failed to fetch transaction data');
            }

            const data = await response.json();

            if (data.success) {
                setTransactions(data.transactions);
            } else {
                setError(data.error || 'Failed to load transaction data');
            }
        } catch (err) {
            setError('An error occurred while fetching transaction data');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    const filterTransactions = () => {
        let filtered = [...transactions];

        // Apply type filter
        if (transactionTypeFilter !== 'all') {
            filtered = filtered.filter(t => t.type === transactionTypeFilter);
        }

        // Apply search filter
        if (searchTerm) {
            const searchLower = searchTerm.toLowerCase();
            filtered = filtered.filter(t =>
                (t.product?.name && t.product.name.toLowerCase().includes(searchLower)) ||
                (t.product?.code && t.product.code.toLowerCase().includes(searchLower)) ||
                (t.notes && t.notes.toLowerCase().includes(searchLower)) ||
                (t.user?.fullName && t.user.fullName.toLowerCase().includes(searchLower)) ||
                (t.branch?.name && t.branch.name.toLowerCase().includes(searchLower)) ||
                (t.branch?.code && t.branch.code.toLowerCase().includes(searchLower))
            );
        }

        setFilteredTransactions(filtered);
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    };

    const formatCurrency = (amount: number) => {
        // return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'BDT' }).format(amount);
        return `৳ ${new Intl.NumberFormat('en-US', {
            style: 'decimal',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }).format(amount)}`;
    };

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
    };

    const handleTypeFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setTransactionTypeFilter(e.target.value as 'all' | 'in' | 'out');
    };

    if (permissionLoading || loading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold">Inventory Transactions</h1>
                <div className="flex space-x-2">
                    <button
                        onClick={() => router.push('/dashboard/inventory')}
                        className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 cursor-pointer"
                    >
                        Back to Inventory
                    </button>
                </div>
            </div>

            {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">Error: </strong>
                    <span className="block sm:inline">{error}</span>
                </div>
            )}

            {/* Search & Filter Bar */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-6">
                <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-4">
                    <div className="w-full md:w-1/2">
                        <input
                            type="text"
                            value={searchTerm}
                            onChange={handleSearchChange}
                            placeholder="Search by product, branch, notes, or user"
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                    </div>
                    <div className="w-full md:w-1/4">
                        <select
                            value={transactionTypeFilter}
                            onChange={handleTypeFilterChange}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        >
                            <option value="all">All Types</option>
                            <option value="in">Stock In</option>
                            <option value="out">Stock Out</option>
                        </select>
                    </div>
                </div>
            </div>

            {/* Summary cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                    <h2 className="text-sm text-gray-500 dark:text-gray-400 uppercase mb-2">Total Transactions</h2>
                    <p className="text-3xl font-bold">{transactions.length}</p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                    <h2 className="text-sm text-gray-500 dark:text-gray-400 uppercase mb-2">Stock In</h2>
                    <p className="text-3xl font-bold text-green-600">{transactions.filter(t => t.type === 'in').length}</p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                    <h2 className="text-sm text-gray-500 dark:text-gray-400 uppercase mb-2">Stock Out</h2>
                    <p className="text-3xl font-bold text-red-600">{transactions.filter(t => t.type === 'out').length}</p>
                </div>
            </div>

            {filteredTransactions.length === 0 ? (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 text-center">
                    <p className="text-gray-600 dark:text-gray-400">
                        {transactions.length === 0 ? "No transactions found." : "No transactions match your search criteria."}
                    </p>
                </div>
            ) : (
                <div className="overflow-x-auto">
                    <table className="min-w-full bg-white dark:bg-gray-800 rounded-lg shadow">
                        <thead className="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Type</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Product</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Code</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Branch</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Quantity</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Unit Price</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Total Value</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Performed By</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Notes</th>
                            </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                            {filteredTransactions.map((transaction) => (
                                <tr key={transaction.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {formatDate(transaction.date)}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${transaction.type === 'in'
                                            ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                                            : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                                            }`}>
                                            {transaction.type === 'in' ? 'Stock In' : 'Stock Out'}
                                        </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm font-medium text-gray-900 dark:text-white">{transaction.product?.name || 'Unknown Product'}</div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {transaction.product?.code || 'N/A'}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-500 dark:text-gray-400">
                                            {transaction.branch ? (
                                                <>
                                                    {transaction.branch.code && <span className="text-gray-400 mr-1">[{transaction.branch.code}]</span>}
                                                    {transaction.branch.name}
                                                    {transaction.branch.isMain && (
                                                        <span className="ml-1 text-xs bg-blue-100 text-blue-800 px-1 py-0.5 rounded">
                                                            Main
                                                        </span>
                                                    )}
                                                </>
                                            ) : 'Unknown Branch'}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {transaction.quantity} {transaction.product?.unit || 'units'}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {formatCurrency(transaction.unitPrice)}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {formatCurrency(transaction.unitPrice * transaction.quantity)}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {transaction.user?.fullName || 'Unknown User'}
                                    </td>
                                    <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                                        {transaction.notes || 'N/A'}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}
        </div>
    );
}
