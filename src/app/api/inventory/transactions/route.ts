import { NextRequest, NextResponse } from "next/server";
import { auth } from "../../../../../auth";
import { db } from "@/lib/db";
import {
  inventoryTransactions,
  products,
  tenantUsers,
  users,
  branches,
} from "@/db/schema";
import { eq, and, desc } from "drizzle-orm";

// GET /api/inventory/transactions - Get inventory transactions
export async function GET(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Parse query parameters
    const url = new URL(req.url);
    const productId = url.searchParams.get("productId");
    const type = url.searchParams.get("type") as "in" | "out" | null;
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");

    // Get tenant ID from session based on user role
    let tenantId: string;
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      // For sales users, use their associated tenant's ID
      tenantId = session.user.tenantId;
    } else {
      // For tenant users, use their own ID
      tenantId = session.user.id;
    }
    console.log('GET /api/inventory/transactions - Using tenant ID:', tenantId, 'for user role:', session.user.role);

    // Build the query conditions
    let conditions = [eq(inventoryTransactions.tenantId, tenantId)];

    if (productId) {
      conditions.push(eq(inventoryTransactions.productId, productId));
    }

    if (type) {
      conditions.push(eq(inventoryTransactions.type, type));
    }

    // if (startDate) {
    //   const startDateTime = new Date(startDate);
    //   conditions.push(inventoryTransactions.date >= startDateTime);
    // }

    // if (endDate) {
    //   const endDateTime = new Date(endDate);
    //   // Set the time to end of day
    //   endDateTime.setHours(23, 59, 59, 999);
    //   conditions.push(inventoryTransactions.date <= endDateTime);
    // }

    // Get all transactions with related data
    const allTransactions = await db
      .select({
        id: inventoryTransactions.id,
        tenantId: inventoryTransactions.tenantId,
        productId: inventoryTransactions.productId,
        branchId: inventoryTransactions.branchId,
        quantity: inventoryTransactions.quantity,
        unitPrice: inventoryTransactions.unitPrice,
        type: inventoryTransactions.type,
        date: inventoryTransactions.date,
        notes: inventoryTransactions.notes,
        performedBy: inventoryTransactions.performedBy,
        createdAt: inventoryTransactions.createdAt,
        updatedAt: inventoryTransactions.updatedAt,
        productName: products.name,
        productCode: products.code,
        productUnit: products.unit,
        userFullName: users.fullName,
        branchName: branches.name,
        branchCode: branches.code,
        branchIsMain: branches.isMain,
      })
      .from(inventoryTransactions)
      .leftJoin(products, eq(inventoryTransactions.productId, products.id))
      .leftJoin(users, eq(inventoryTransactions.performedBy, users.id))
      .leftJoin(branches, eq(inventoryTransactions.branchId, branches.id))
      .where(and(...conditions))
      .orderBy(desc(inventoryTransactions.date));

    // Transform to include nested objects
    const transformedTransactions = allTransactions.map((transaction) => ({
      id: transaction.id,
      tenantId: transaction.tenantId,
      productId: transaction.productId,
      branchId: transaction.branchId,
      quantity: transaction.quantity,
      unitPrice: transaction.unitPrice,
      type: transaction.type,
      date: transaction.date,
      notes: transaction.notes,
      performedBy: transaction.performedBy,
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
      product: transaction.productName
        ? {
            id: transaction.productId,
            name: transaction.productName,
            code: transaction.productCode,
            unit: transaction.productUnit,
          }
        : undefined,
      user: transaction.userFullName
        ? {
            id: transaction.performedBy,
            fullName: transaction.userFullName,
          }
        : undefined,
      branch: transaction.branchName
        ? {
            id: transaction.branchId,
            name: transaction.branchName,
            code: transaction.branchCode,
            isMain: transaction.branchIsMain,
          }
        : undefined,
    }));

    return NextResponse.json({
      success: true,
      transactions: transformedTransactions,
    });
  } catch (error) {
    console.error("Error fetching inventory transactions:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch transaction data" },
      { status: 500 }
    );
  }
}
