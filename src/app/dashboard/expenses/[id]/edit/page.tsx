'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { toast } from 'sonner';
import { use } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

// Define schema for form validation
const expenseSchema = z.object({
  categoryId: z.string().min(1, { message: 'Category is required' }),
  branchId: z.string().min(1, { message: 'Branch is required' }),
  amount: z.coerce.number().positive({ message: 'Amount must be greater than 0' }),
  date: z.string().min(1, { message: 'Date is required' }),
  description: z.string().optional(),
  paymentMethod: z.enum(['cash', 'card', 'bank_transfer']),
  reference: z.string().optional(),
});

type ExpenseFormValues = z.infer<typeof expenseSchema>;

interface ExpenseCategory {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
}

interface Branch {
  id: string;
  name: string;
  code: string;
  isMain: boolean;
  isActive: boolean;
}

interface Expense {
  id: string;
  categoryId: string;
  branchId: string;
  amount: number;
  date: string;
  description: string | null;
  paymentMethod: 'cash' | 'card' | 'bank_transfer';
  reference: string | null;
  performedBy: string;
  createdAt: string;
  category: ExpenseCategory;
  branch: Branch;
}

export default function EditExpensePage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const unwrappedParams = use(params);
  const expenseId = unwrappedParams.id;

  const [categories, setCategories] = useState<ExpenseCategory[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expense, setExpense] = useState<Expense | null>(null);
  const [selectedCategoryName, setSelectedCategoryName] = useState<string>("");
  const [selectedBranchName, setSelectedBranchName] = useState<string>("");

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<ExpenseFormValues>({
    resolver: zodResolver(expenseSchema),
    defaultValues: {
      categoryId: '',
      branchId: '',
      amount: 0,
      date: new Date().toISOString().split('T')[0],
      description: '',
      paymentMethod: 'cash',
      reference: '',
    },
  });

  useEffect(() => {
    fetchExpense();
    fetchCategories();
    fetchBranches();
  }, [expenseId]);

  // Update selected names when form values change
  const watchedCategoryId = watch('categoryId');
  const watchedBranchId = watch('branchId');

  useEffect(() => {
    if (watchedCategoryId && categories.length > 0) {
      const category = categories.find(cat => cat.id === watchedCategoryId);
      if (category) {
        setSelectedCategoryName(category.name);
      }
    }
  }, [watchedCategoryId, categories]);

  useEffect(() => {
    if (watchedBranchId && branches.length > 0) {
      const branch = branches.find(b => b.id === watchedBranchId);
      if (branch) {
        setSelectedBranchName(branch.name);
      }
    }
  }, [watchedBranchId, branches]);

  const fetchExpense = async () => {
    try {
      const response = await fetch(`/api/expenses?id=${expenseId}`);

      if (!response.ok) {
        if (response.status === 404) {
          router.push('/dashboard/expenses');
          return;
        }
        throw new Error(`Failed to fetch expense: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setExpense(data.expense);

        // Set selected names for display
        if (data.expense.category) {
          setSelectedCategoryName(data.expense.category.name);
        }

        if (data.expense.branch) {
          setSelectedBranchName(data.expense.branch.name);
        }

        // Format date for input field (YYYY-MM-DD)
        const expenseDate = new Date(data.expense.date);
        const formattedDate = expenseDate.toISOString().split('T')[0];

        // Set form values
        reset({
          categoryId: data.expense.categoryId,
          branchId: data.expense.branchId,
          amount: data.expense.amount,
          date: formattedDate,
          description: data.expense.description || '',
          paymentMethod: data.expense.paymentMethod,
          reference: data.expense.reference || '',
        });
      } else {
        setError(data.error || 'Failed to load expense');
        router.push('/dashboard/expenses');
      }
    } catch (error) {
      console.error('Error fetching expense:', error);
      setError('An error occurred while fetching the expense');
      router.push('/dashboard/expenses');
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/expense-categories');

      if (!response.ok) {
        throw new Error(`Failed to fetch categories: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setCategories(data.categories || []);
      } else {
        setError(data.error || 'Failed to load expense categories');
      }
    } catch (error) {
      console.error('Error fetching expense categories:', error);
      setError('An error occurred while fetching expense categories');
    }
  };

  const fetchBranches = async () => {
    try {
      const response = await fetch('/api/branches');

      if (!response.ok) {
        throw new Error(`Failed to fetch branches: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setBranches(data.branches || []);
      } else {
        setError(data.error || 'Failed to load branches');
      }
    } catch (error) {
      console.error('Error fetching branches:', error);
      setError('An error occurred while fetching branches');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: ExpenseFormValues) => {
    if (!expense) return;

    try {
      setSubmitting(true);

      const response = await fetch('/api/expenses', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: expenseId,
          ...data,
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Expense updated successfully');
        router.push('/dashboard/expenses');
      } else {
        toast.error(result.error || 'Failed to update expense');
      }
    } catch (error) {
      console.error('Error updating expense:', error);
      toast.error('An error occurred while updating the expense');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Edit Expense</h1>
        <Button
          onClick={() => router.push('/dashboard/expenses')}
          variant="outline"
          className='cursor-pointer'
        >
          Back to Expenses
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Edit Expense Details</CardTitle>
          <CardDescription>
            Update the details of this expense
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="categoryId">Expense Category</Label>
                <Select
                  onValueChange={(value) => {
                    setValue('categoryId', value);
                    const category = categories.find(cat => cat.id === value);
                    if (category) {
                      setSelectedCategoryName(category.name);
                    }
                  }}
                  defaultValue={watch('categoryId')}
                >
                  <SelectTrigger id="categoryId">
                    <SelectValue placeholder="Select a category">
                      {selectedCategoryName || "Select a category"}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {categories.length === 0 ? (
                      <SelectItem value="" data-disabled={true}>
                        No categories available
                      </SelectItem>
                    ) : (
                      categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {errors.categoryId && (
                  <p className="text-red-500 text-sm">{errors.categoryId.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="branchId">Branch</Label>
                <Select
                  onValueChange={(value) => {
                    setValue('branchId', value);
                    const branch = branches.find(b => b.id === value);
                    if (branch) {
                      setSelectedBranchName(branch.name);
                    }
                  }}
                  defaultValue={watch('branchId')}
                >
                  <SelectTrigger id="branchId">
                    <SelectValue placeholder="Select a branch">
                      {selectedBranchName || "Select a branch"}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {branches.length === 0 ? (
                      <SelectItem value="" data-disabled={true}>
                        No branches available
                      </SelectItem>
                    ) : (
                      branches.map((branch) => (
                        <SelectItem key={branch.id} value={branch.id}>
                          {branch.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {errors.branchId && (
                  <p className="text-red-500 text-sm">{errors.branchId.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="amount">Amount</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  min="0.01"
                  placeholder="0.00"
                  {...register('amount')}
                />
                {errors.amount && (
                  <p className="text-red-500 text-sm">{errors.amount.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  {...register('date')}
                  className="h-9 px-3 py-2 bg-white border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                {errors.date && (
                  <p className="text-red-500 text-sm">{errors.date.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="paymentMethod">Payment Method</Label>
                <Select
                  onValueChange={(value) => setValue('paymentMethod', value as 'cash' | 'card' | 'bank_transfer')}
                  defaultValue={watch('paymentMethod')}
                >
                  <SelectTrigger id="paymentMethod">
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cash">Cash</SelectItem>
                    <SelectItem value="card">Card</SelectItem>
                    <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                  </SelectContent>
                </Select>
                {errors.paymentMethod && (
                  <p className="text-red-500 text-sm">{errors.paymentMethod.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="reference">Reference (Optional)</Label>
                <Input
                  id="reference"
                  placeholder="e.g., Invoice #, Receipt #"
                  {...register('reference')}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                placeholder="Add details about this expense"
                rows={3}
                {...register('description')}
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-between pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/dashboard/expenses')}
              disabled={submitting}
              className="cursor-pointer border-gray-300 hover:bg-gray-100 transition-colors duration-200 py-2 px-4"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={submitting}
              className="bg-blue-600 hover:bg-blue-700 text-white cursor-pointer shadow-md hover:shadow-lg transition-all duration-200 py-2 px-4"
            >
              {submitting ? 'Updating...' : 'Update Expense'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
