'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

export default function UserSidebar() {
  const pathname = usePathname();
  
  const isActive = (path: string) => {
    return pathname === path ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : '';
  };

  return (
    <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-5 mb-6 border border-gray-200 dark:border-gray-700">
      <h3 className="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300">User Management</h3>
      <nav>
        <ul className="space-y-2">
          <li>
            <Link 
              href="/dashboard/users" 
              className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/users')}`}
            >
              All Users
            </Link>
          </li>
          <li>
            <Link 
              href="/dashboard/users/add" 
              className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/users/add')}`}
            >
              Add User
            </Link>
          </li>
        </ul>
      </nav>
    </div>
  );
}