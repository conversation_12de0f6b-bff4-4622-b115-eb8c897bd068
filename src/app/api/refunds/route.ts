import { NextRequest, NextResponse } from "next/server";
import { auth } from "../../../../auth";
import { db } from "@/lib/db";
import {
  refunds,
  refundItems,
  inventoryTransactions,
  products,
  orders,
  orderItems,
} from "@/db/schema";
import { eq, and, desc } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";

// Schema for validating refund data
const refundItemSchema = z.object({
  productId: z.string().min(1, "Product ID is required"),
  quantity: z.number().int().min(1, "Quantity must be at least 1"),
  unitPrice: z.number().min(0, "Unit price must be a positive number"),
  total: z.number().min(0, "Total must be a positive number"),
  isDamaged: z.boolean().default(false),
});

const refundSchema = z.object({
  orderId: z.string().optional(),
  customerId: z.string().optional(),
  branchId: z.string().min(1, "Branch ID is required"),
  reasonId: z.string().min(1, "Reason ID is required"),
  refundDate: z.string().min(1, "Refund date is required"),
  totalAmount: z.number().min(0, "Total amount must be a positive number"),
  notes: z.string().optional(),
  isDamaged: z.boolean().default(false),
  items: z.array(refundItemSchema).min(1, "At least one item is required"),
});

// GET /api/refunds - Get all refunds
export async function GET(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get tenant ID from session based on user role
    let tenantId: string;
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      // For sales users, use their associated tenant's ID
      tenantId = session.user.tenantId;
    } else {
      // For tenant users, use their own ID
      tenantId = session.user.id;
    }

    // Get all refunds for this tenant
    const allRefunds = await db.query.refunds.findMany({
      where: eq(refunds.tenantId, tenantId),
      orderBy: [desc(refunds.createdAt)],
      with: {
        customer: true,
        order: true,
        reason: true,
        items: {
          with: {
            product: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      refunds: allRefunds,
    });
  } catch (error) {
    console.error("Error fetching refunds:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch refunds" },
      { status: 500 }
    );
  }
}

// POST /api/refunds - Create a new refund
export async function POST(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get tenant ID from session based on user role
    let tenantId: string;
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      // For sales users, use their associated tenant's ID
      tenantId = session.user.tenantId;
    } else {
      // For tenant users, use their own ID
      tenantId = session.user.id;
    }

    // Parse and validate request body
    const body = await req.json();
    const validationResult = refundSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: "Invalid refund data", 
          details: validationResult.error.format() 
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Process the refund in a transaction
    const result = await db.transaction(async (tx) => {
      // Create the refund record
      const refundId = uuidv4();
      await tx.insert(refunds).values({
        id: refundId,
        tenantId,
        branchId: data.branchId,
        orderId: data.orderId || null,
        customerId: data.customerId || null,
        reasonId: data.reasonId,
        refundDate: new Date(data.refundDate),
        totalAmount: data.totalAmount,
        notes: data.notes || null,
        isDamaged: data.isDamaged,
        performedBy: session.user.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Process each refund item
      for (const item of data.items) {
        // Create refund item record
        const refundItemId = uuidv4();
        await tx.insert(refundItems).values({
          id: refundItemId,
          refundId,
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          total: item.total,
          isDamaged: item.isDamaged,
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        // Create inventory transaction for the refund
        // If the item is damaged, we don't add it back to inventory
        if (!item.isDamaged) {
          const transactionId = uuidv4();
          await tx.insert(inventoryTransactions).values({
            id: transactionId,
            tenantId,
            branchId: data.branchId,
            productId: item.productId,
            quantity: item.quantity,
            warehouseQuantity: 0, // Add back to store, not warehouse
            storeQuantity: item.quantity, // Add back to store
            unitPrice: item.unitPrice,
            type: "refund",
            date: new Date(data.refundDate),
            notes: `Refund${data.orderId ? ` - Order #${data.orderId}` : ''}${data.notes ? ` - ${data.notes}` : ''}`,
            performedBy: session.user.id,
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }
      }

      // Return the created refund ID
      return { refundId };
    });

    // Fetch the complete refund with related data
    const createdRefund = await db.query.refunds.findFirst({
      where: eq(refunds.id, result.refundId),
      with: {
        customer: true,
        order: true,
        reason: true,
        items: {
          with: {
            product: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: "Refund processed successfully",
      refund: createdRefund,
    });
  } catch (error) {
    console.error("Error processing refund:", error);
    return NextResponse.json(
      { success: false, error: "Failed to process refund" },
      { status: 500 }
    );
  }
}
