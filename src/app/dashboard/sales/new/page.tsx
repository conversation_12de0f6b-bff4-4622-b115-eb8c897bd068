import { Metadata } from "next";
import { redirect } from "next/navigation";
import { auth } from "~/auth";
import { db } from "@/lib/db";
import { products, customers, productCategories, vendors, inventoryTransactions, branches } from "@/db/schema";
import { eq, and, sum, desc } from "drizzle-orm";
import ClientWrapper from "./_components/client-wrapper";

export const metadata: Metadata = {
    title: "New Sale",
    description: "Create a new sale for your bookshop",
};

export default async function NewSalePage() {
    const session = await auth();
    if (!session?.user) {
        redirect("/api/auth/signin");
    }

    // Get tenant ID based on user role
    const tenantId = session.user.role === 'tenant_sale' && session.user.tenantId
        ? session.user.tenantId
        : session.user.id;

    // Get all products for this tenant
    const allProducts = await db.query.products.findMany({
        where: (products, { eq, and }) =>
            and(
                eq(products.tenantId, tenantId)
            ),
        with: {
            category: true,
            vendor: true,
        },
    });

    // Get all branches for this tenant
    const allBranches = await db.query.branches.findMany({
        where: (branches, { eq }) => eq(branches.tenantId, tenantId),
    });

    // Filter to only include active branches
    const activeBranches = allBranches.filter(branch => branch.isActive !== false);

    // Determine which branch to use initially
    let initialBranchId = '';

    // For tenant_sale users, use their assigned branch from the session
    if (session.user.role === 'tenant_sale' && session.user.branchId) {
        // Verify that the assigned branch is active
        const assignedBranchIsActive = activeBranches.some(branch => branch.id === session.user.branchId);
        initialBranchId = assignedBranchIsActive ? session.user.branchId : '';
    } else {
        // For tenant users, use the main branch or first available active branch
        const mainBranch = activeBranches.find(branch => branch.isMain);
        initialBranchId = mainBranch ? mainBranch.id : (activeBranches.length > 0 ? activeBranches[0].id : '');
    }

    // Get inventory data for products by branch
    const inventoryData = new Map();

    // Get "in" transaction quantities by product and branch
    const inTransactions = await db
        .select({
            productId: inventoryTransactions.productId,
            branchId: inventoryTransactions.branchId,
            totalIn: sum(inventoryTransactions.storeQuantity), // Only use store quantity for sales
        })
        .from(inventoryTransactions)
        .where(
            and(
                eq(inventoryTransactions.type, "in"),
                eq(inventoryTransactions.tenantId, tenantId)
            )
        )
        .groupBy(inventoryTransactions.productId, inventoryTransactions.branchId);

    // Get "out" transaction quantities by product and branch
    const outTransactions = await db
        .select({
            productId: inventoryTransactions.productId,
            branchId: inventoryTransactions.branchId,
            totalOut: sum(inventoryTransactions.storeQuantity), // Only use store quantity for sales
        })
        .from(inventoryTransactions)
        .where(
            and(
                eq(inventoryTransactions.type, "out"),
                eq(inventoryTransactions.tenantId, tenantId)
            )
        )
        .groupBy(inventoryTransactions.productId, inventoryTransactions.branchId);

    // Create maps for looking up stock by product and branch
    const inMap = new Map();
    inTransactions.forEach(t => {
        const key = `${t.productId}-${t.branchId}`;
        inMap.set(key, Number(t.totalIn || 0));
    });

    const outMap = new Map();
    outTransactions.forEach(t => {
        const key = `${t.productId}-${t.branchId}`;
        outMap.set(key, Number(t.totalOut || 0));
    });

    // Calculate stock for each product by branch (for all branches, including inactive ones)
    // We need to calculate for all branches because the inventory data might be needed
    // for existing orders that used inactive branches
    allProducts.forEach(product => {
        allBranches.forEach(branch => {
            const key = `${product.id}-${branch.id}`;
            const inStock = inMap.get(key) || 0;
            const outStock = outMap.get(key) || 0;
            const currentStock = inStock - outStock;
            inventoryData.set(key, currentStock);
        });
    });

    // Add stock information to products for the initial branch
    const productsWithStock = allProducts.map(product => {
        const key = `${product.id}-${initialBranchId}`;
        return {
            ...product,
            stock: inventoryData.get(key) || 0
        };
    });
    // Get all customers for this tenant
    const allCustomers = await db.query.customers.findMany({
        where: (customers, { eq }) => eq(customers.tenantId, tenantId),
    });

    // Generate a new sequence memo number based on existing orders for the tenant
    const lastOrder = await db.query.orders.findFirst({
        where: (orders, { eq }) => eq(orders.tenantId, tenantId),
        orderBy: (orders) => [desc(orders.createdAt)],
    });

    const memoNo = lastOrder ? (parseInt(lastOrder.memoNo) + 1).toString() : "10001"; // Start from 10001 if no previous order

    return (
        <div className="container">
            <ClientWrapper
                products={productsWithStock}
                customers={allCustomers}
                branches={allBranches}
                initialBranchId={initialBranchId}
                initialMemoNo={memoNo}
                tenantId={tenantId}
                inventoryData={inventoryData}
                sessionBranchId={session.user.branchId || null}
            />
        </div>
    );
}