import { NextRequest, NextResponse } from "next/server";
import { auth } from "../../../../auth";
import { db } from "@/lib/db";
import { branches, users } from "@/db/schema";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import { eq, and } from "drizzle-orm";

// Schema for validating branch creation
const branchCreateSchema = z.object({
  tenantId: z.string().min(1, "Tenant ID is required").optional(),
  name: z.string().min(1, "Branch name is required"),
  code: z.string().min(1, "Branch code is required"),
  address: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email().optional(),
  isMain: z.boolean().default(false),
  notes: z.string().optional(),
});

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const requestedTenantId = searchParams.get("tenantId");
    const searchQuery = searchParams.get("search")?.toLowerCase();

    // Determine the effective tenant ID based on user role
    let effectiveTenantId = session.user.id;

    if (session.user.role === "tenant_sale" && session.user.tenantId) {
      // For tenant_sale users, use their tenant's ID
      effectiveTenantId = session.user.tenantId;
    } else if (session.user.role === "tenant") {
      // For tenant users, use their own ID
      effectiveTenantId = session.user.id;
    }

    // Use requested tenantId if provided, otherwise use the effective one
    const tenantId = requestedTenantId || effectiveTenantId;

    console.log("Using tenantId for branch listing:", tenantId);

    if (!tenantId) {
      return NextResponse.json(
        { success: false, error: "Tenant ID not found" },
        { status: 400 }
      );
    }

    // Check if user has access to this tenant
    // Admin and tenant roles have full access
    // tenant_sale users need to have the correct tenantId
    if (session.user.role === "tenant_sale" && session.user.tenantId !== tenantId) {
      return NextResponse.json(
        { success: false, error: "Access denied for tenant_sale user" },
        { status: 403 }
      );
    }

    // Fetch branches for the tenant
    let branchList = await db
      .select()
      .from(branches)
      .where(eq(branches.tenantId, tenantId));

    // Filter branches if search query is provided
    if (searchQuery) {
      branchList = branchList.filter(
        (branch) =>
          branch.name.toLowerCase().includes(searchQuery) ||
          branch.code.toLowerCase().includes(searchQuery)
      );
    }

    return NextResponse.json({
      success: true,
      branches: branchList,
    });
  } catch (error) {
    console.error("Error fetching branches:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch branches" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = branchCreateSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: "Validation failed",
          details: validationResult.error.format(),
        },
        { status: 400 }
      );
    }

    // Get branch data, use tenantId from session if not provided
    // For tenant users, we need to use their user ID as the tenantId
    // For tenant_sale users, we need to use their tenant's user ID
    let effectiveTenantId = session.user.id;

    if (session.user.role === "tenant_sale" && session.user.tenantId) {
      // For tenant_sale users, use their tenant's ID
      effectiveTenantId = session.user.tenantId;
    } else if (session.user.role === "tenant") {
      // For tenant users, use their own ID
      effectiveTenantId = session.user.id;
    }

    const branchData = {
      ...validationResult.data,
      tenantId: validationResult.data.tenantId || effectiveTenantId,
    };

    if (!branchData.tenantId) {
      return NextResponse.json(
        { success: false, error: "Tenant ID not found" },
        { status: 400 }
      );
    }

    console.log("Using tenantId for branch creation:", branchData.tenantId);

    // Check if user has access to this tenant
    // Admin and tenant roles have full access
    // tenant_sale users need to have the correct tenantId
    if (
      session.user.role === "tenant_sale" &&
      session.user.tenantId !== branchData.tenantId
    ) {
      return NextResponse.json(
        { success: false, error: "Access denied for tenant_sale user" },
        { status: 403 }
      );
    }

    // Check if user exists (since branches.tenantId references users.id)
    const userExists = await db
      .select({ id: users.id, role: users.role })
      .from(users)
      .where(eq(users.id, branchData.tenantId))
      .limit(1);

    if (userExists.length === 0) {
      return NextResponse.json(
        { success: false, error: "User ID not found. The tenant ID must reference a valid user ID." },
        { status: 404 }
      );
    }

    console.log("Found user for branch creation:", userExists[0]);

    // If this is set as main branch, unset any existing main branch
    if (branchData.isMain) {
      await db
        .update(branches)
        .set({ isMain: false })
        .where(eq(branches.tenantId, branchData.tenantId));
    }

    // Create the branch
    const [branch] = await db
      .insert(branches)
      .values({
        id: uuidv4(),
        tenantId: branchData.tenantId,
        name: branchData.name,
        code: branchData.code,
        address: branchData.address,
        phone: branchData.phone,
        email: branchData.email,
        isMain: branchData.isMain,
        notes: branchData.notes,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return NextResponse.json({
      success: true,
      branch,
    });
  } catch (error) {
    console.error("Error creating branch:", error);
    // Provide more detailed error message
    let errorMessage = "Failed to create branch";
    if (error instanceof Error) {
      errorMessage = `Error creating branch: ${error.message}`;

      // Check for foreign key constraint error
      if (error.message.includes("FOREIGN KEY constraint failed")) {
        errorMessage = "Foreign key constraint failed. Make sure the tenant ID references a valid user ID.";
      }
    }

    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}
