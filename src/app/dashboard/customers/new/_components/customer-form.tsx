"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import {
    User,
    Users,
    Phone,
    Mail,
    MapPin,
    Tag,
    DollarSign,
    CreditCard,
    FileText,
    Save,
    X
} from "lucide-react";

// Define the schema for the customer form
const formSchema = z.object({
    name: z.string().min(2, {
        message: "Name must be at least 2 characters.",
    }),
    code: z.string().optional().refine(val => !val || val.trim().length > 0, {
        message: "Customer code cannot be empty if provided.",
    }),
    phone: z.string().optional(),
    email: z.string().email({
        message: "Please enter a valid email address.",
    }).optional().or(z.literal('')),
    address: z.string().optional(),
    type: z.enum(["retail", "wholesale"]),
    creditLimit: z.number().min(0, {
        message: "Credit limit must be a positive number.",
    }).optional(),
    initialDue: z.number().min(0, {
        message: "Initial due amount must be a positive number.",
    }).default(0),
    advancedPayment: z.number().min(0, {
        message: "Advanced payment amount must be a positive number.",
    }).default(0),
    extraCommission: z.number().min(0, {
        message: "Extra commission must be a positive number.",
    }).default(0),
    notes: z.string().optional(),
}).refine(
    (data) => !(data.initialDue > 0 && data.advancedPayment > 0),
    {
        message: "A customer cannot have both initial due and advanced payment at the same time",
        path: ["advancedPayment"], // Show error on advanced payment field
    }
);

type CustomerFormValues = z.infer<typeof formSchema>;

// Props type for the CustomerForm component
interface CustomerFormProps {
    defaultValues?: Partial<CustomerFormValues>;
    customerId?: string;
    tenantId: string;
}

export function CustomerForm({ defaultValues, customerId, tenantId }: CustomerFormProps) {
    const router = useRouter();
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
    const [isMounted, setIsMounted] = useState<boolean>(false);
    const isEditing = !!customerId;

    // Hydration protection
    useEffect(() => {
        setIsMounted(true);
    }, []);

    // Initialize the form with default values
    const form = useForm<CustomerFormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: "",
            code: "",
            phone: "",
            email: "",
            address: "",
            type: "retail",
            creditLimit: 0,
            initialDue: 0,
            notes: "",
            advancedPayment: 0,
            extraCommission: 0,
            ...defaultValues,
        },
    });

    // Handle form submission
    const onSubmit = async (data: CustomerFormValues) => {
        try {
            setIsSubmitting(true);

            const customerData = {
                ...data,
                tenantId,
            };

            // Determine if we're creating or updating
            const endpoint = isEditing
                ? `/api/customers/${customerId}`
                : "/api/customers";

            const method = isEditing ? "PUT" : "POST";

            const response = await fetch(endpoint, {
                method: method,
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(customerData),
            });

            const responseData = await response.json();

            if (!response.ok) {
                // Handle specific error for duplicate code
                if (responseData.code === "DUPLICATE_CODE") {
                    form.setError("code", {
                        type: "manual",
                        message: "This customer code is already in use. Please use a unique code."
                    });
                    throw new Error("This customer code is already in use. Please use a unique code.");
                } else {
                    throw new Error(responseData.message || "Something went wrong");
                }
            }

            toast.success(isEditing ? "Customer updated successfully" : "Customer created successfully");
            router.push("/dashboard/customers");
            router.refresh();
        } catch (error: any) {
            toast.error(error.message || "An error occurred");
        } finally {
            setIsSubmitting(false);
        }
    };

    // Handle cancel
    const handleCancel = () => {
        router.push("/dashboard/customers");
    };

    // Don't render the form until client-side
    if (!isMounted) {
        return (
            <div className="max-w-5xl mx-auto">
                <Card className="shadow-xl border-0 overflow-hidden bg-gradient-to-br from-white to-gray-50">
                    <CardContent className="px-8 py-6">
                        <div className="space-y-8">
                            {/* Animated loading sections */}
                            {[...Array(4)].map((_, index) => (
                                <div key={index} className="bg-white p-6 rounded-xl border border-gray-100 shadow-sm">
                                    <div className="flex items-center mb-6 animate-pulse">
                                        <div className={`rounded-full h-10 w-10 mr-3 ${index === 0 ? "bg-blue-100" :
                                            index === 1 ? "bg-green-100" :
                                                index === 2 ? "bg-purple-100" : "bg-amber-100"
                                            }`}></div>
                                        <div className="space-y-2 flex-1">
                                            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                                            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                                        </div>
                                    </div>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="animate-pulse space-y-2">
                                            <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                                            <div className="h-10 bg-gray-100 rounded-lg w-full"></div>
                                        </div>
                                        <div className="animate-pulse space-y-2">
                                            <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                                            <div className="h-10 bg-gray-100 rounded-lg w-full"></div>
                                        </div>
                                    </div>
                                </div>
                            ))}

                            <div className="flex justify-end space-x-4 mt-8 border-t border-gray-200 pt-6 animate-pulse">
                                <div className="h-10 bg-gray-200 rounded-lg w-24"></div>
                                <div className="h-10 bg-blue-200 rounded-lg w-36"></div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="mx-auto" suppressHydrationWarning>
            <Card className="shadow-md border-0 overflow-hidden bg-gradient-to-br from-white to-gray-50" suppressHydrationWarning>
                <CardContent className="px-6 py-1">
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4" suppressHydrationWarning>
                            {/* Main Form Container */}
                            <div className="bg-white p-4 rounded-xl border border-gray-100 shadow-sm">
                                {!isEditing && (
                                    <div className="mb-4 p-3 bg-blue-50 border border-blue-100 rounded-lg">
                                        <h3 className="text-sm font-medium text-blue-800 mb-1">Important Note:</h3>
                                        <p className="text-xs text-blue-700">
                                            A customer can either have an initial due amount (they owe you) OR an advanced payment (they've paid in advance), but not both at the same time.
                                            Entering a value in one field will disable the other.
                                        </p>
                                    </div>
                                )}
                                {/* Top Row - 3 columns */}
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                    <FormField
                                        control={form.control}
                                        name="name"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center text-gray-700 font-medium text-sm">
                                                    Customer Name <span className="text-red-500 ml-1">*</span>
                                                </FormLabel>
                                                <FormControl>
                                                    <div className="relative">
                                                        <Input
                                                            className="bg-white border-gray-200 rounded-lg pl-9 py-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
                                                            placeholder="Enter customer name"
                                                            {...field}
                                                            suppressHydrationWarning
                                                        />
                                                        <User className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                                                    </div>
                                                </FormControl>
                                                <FormDescription className="text-gray-500 text-xs opacity-0">
                                                    Unique identifier
                                                </FormDescription>
                                                <FormMessage className="text-red-500 text-xs" />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="code"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center text-gray-700 font-medium text-sm">
                                                    Customer Code
                                                </FormLabel>
                                                <FormControl>
                                                    <div className="relative">
                                                        <Input
                                                            className="bg-white border-gray-200 rounded-lg pl-9 py-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
                                                            placeholder="Enter customer code"
                                                            {...field}
                                                            suppressHydrationWarning
                                                        />
                                                        <Tag className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                                                    </div>
                                                </FormControl>
                                                <FormDescription className="text-gray-500 text-xs">
                                                    Unique identifier
                                                </FormDescription>
                                                <FormMessage className="text-red-500 text-xs" />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="type"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center text-gray-700 font-medium text-sm">
                                                    Customer Type <span className="text-red-500 ml-1">*</span>
                                                </FormLabel>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    defaultValue={field.value}
                                                >
                                                    <FormControl>
                                                        <div className="relative">
                                                            <SelectTrigger
                                                                className="bg-white border-gray-200 rounded-lg pl-9 py-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
                                                                suppressHydrationWarning
                                                            >
                                                                <SelectValue placeholder="Select type" />
                                                            </SelectTrigger>
                                                            <Tag className="absolute left-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none" />
                                                        </div>
                                                    </FormControl>
                                                    <SelectContent className="bg-white border border-gray-200 shadow-lg rounded-lg">
                                                        <SelectItem value="retail" className="cursor-pointer hover:bg-blue-50">
                                                            <div className="flex items-center">
                                                                <div className="bg-green-100 p-1 rounded-full mr-2">
                                                                    <User className="h-3 w-3 text-green-600" />
                                                                </div>
                                                                Retail
                                                            </div>
                                                        </SelectItem>
                                                        <SelectItem value="wholesale" className="cursor-pointer hover:bg-blue-50">
                                                            <div className="flex items-center">
                                                                <div className="bg-blue-100 p-1 rounded-full mr-2">
                                                                    <Users className="h-3 w-3 text-blue-600" />
                                                                </div>
                                                                Wholesale
                                                            </div>
                                                        </SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                <FormDescription className="text-gray-500 text-xs opacity-0">
                                                    Unique identifier
                                                </FormDescription>
                                                <FormMessage className="text-red-500 text-xs" />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                {/* Middle Row - 3 columns */}
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                    <FormField
                                        control={form.control}
                                        name="phone"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center text-gray-700 font-medium text-sm">
                                                    Phone Number
                                                </FormLabel>
                                                <FormControl>
                                                    <div className="relative">
                                                        <Input
                                                            className="bg-white border-gray-200 rounded-lg pl-9 py-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
                                                            placeholder="Enter phone number"
                                                            {...field}
                                                            suppressHydrationWarning
                                                        />
                                                        <Phone className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                                                    </div>
                                                </FormControl>
                                                <FormDescription className="text-gray-500 text-xs opacity-0">
                                                    Unique identifier
                                                </FormDescription>
                                                <FormMessage className="text-red-500 text-xs" />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="email"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center text-gray-700 font-medium text-sm">
                                                    Email Address
                                                </FormLabel>
                                                <FormControl>
                                                    <div className="relative">
                                                        <Input
                                                            className="bg-white border-gray-200 rounded-lg pl-9 py-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
                                                            placeholder="Enter email address"
                                                            {...field}
                                                            suppressHydrationWarning
                                                        />
                                                        <Mail className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                                                    </div>
                                                </FormControl>
                                                <FormDescription className="text-gray-500 text-xs opacity-0">
                                                    Unique identifier
                                                </FormDescription>
                                                <FormMessage className="text-red-500 text-xs" />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="creditLimit"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center text-gray-700 font-medium text-sm">
                                                    Credit Limit
                                                </FormLabel>
                                                <FormControl>
                                                    <div className="relative">
                                                        <Input
                                                            type="number"
                                                            className="bg-white border-gray-200 rounded-lg pl-9 py-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
                                                            placeholder="Enter credit limit"
                                                            {...field}
                                                            onChange={(e) => field.onChange(Number(e.target.value))}
                                                            suppressHydrationWarning
                                                        />
                                                        <CreditCard className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                                                    </div>
                                                </FormControl>
                                                <FormDescription className="text-gray-500 text-xs">
                                                    Max amount customer can owe
                                                </FormDescription>
                                                <FormMessage className="text-red-500 text-xs" />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-1">
                                    {!isEditing && (
                                        <div className="mb-1">
                                            <FormField
                                                control={form.control}
                                                name="initialDue"
                                                render={({ field }) => {
                                                    // Get the value of advancedPayment to conditionally disable this field
                                                    const advancedPayment = form.watch("advancedPayment");
                                                    const isDisabled = advancedPayment > 0;

                                                    return (
                                                        <FormItem>
                                                            <FormLabel className="flex items-center text-gray-700 font-medium text-sm">
                                                                Initial Due Amount
                                                            </FormLabel>
                                                            <FormControl>
                                                                <div className="relative">
                                                                    <Input
                                                                        type="number"
                                                                        className={`bg-white border-gray-200 rounded-lg pl-9 py-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200 ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                                                                        placeholder="Enter initial due amount"
                                                                        {...field}
                                                                        onChange={(e) => {
                                                                            const value = Number(e.target.value);
                                                                            field.onChange(value);

                                                                            // If user enters a value here, reset advanced payment
                                                                            if (value > 0) {
                                                                                form.setValue("advancedPayment", 0);
                                                                            }
                                                                        }}
                                                                        disabled={isDisabled}
                                                                        suppressHydrationWarning
                                                                    />
                                                                    <DollarSign className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                                                                </div>
                                                            </FormControl>
                                                            <FormDescription className="text-gray-500 text-xs">
                                                                Amount customer already owes you
                                                            </FormDescription>
                                                            <FormMessage className="text-red-500 text-xs" />
                                                            {isDisabled && (
                                                                <p className="text-amber-600 text-xs mt-1">
                                                                    Clear advanced payment to enter due amount
                                                                </p>
                                                            )}
                                                        </FormItem>
                                                    );
                                                }}
                                            />
                                        </div>
                                    )}
                                    {!isEditing && (
                                        <div className="mb-1">
                                            <FormField
                                                control={form.control}
                                                name="advancedPayment"
                                                render={({ field }) => {
                                                    // Get the value of initialDue to conditionally disable this field
                                                    const initialDue = form.watch("initialDue");
                                                    const isDisabled = initialDue > 0;

                                                    return (
                                                        <FormItem>
                                                            <FormLabel className="flex items-center text-gray-700 font-medium text-sm">
                                                                Advanced Payment Amount
                                                            </FormLabel>
                                                            <FormControl>
                                                                <div className="relative">
                                                                    <Input
                                                                        type="number"
                                                                        className={`bg-white border-gray-200 rounded-lg pl-9 py-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200 ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                                                                        placeholder="Enter advanced payment amount"
                                                                        {...field}
                                                                        onChange={(e) => {
                                                                            const value = Number(e.target.value);
                                                                            field.onChange(value);

                                                                            // If user enters a value here, reset initial due
                                                                            if (value > 0) {
                                                                                form.setValue("initialDue", 0);
                                                                            }
                                                                        }}
                                                                        disabled={isDisabled}
                                                                        suppressHydrationWarning
                                                                    />
                                                                    <DollarSign className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                                                                </div>
                                                            </FormControl>
                                                            <FormDescription className="text-gray-500 text-xs">
                                                                Amount customer has paid in advance
                                                            </FormDescription>
                                                            <FormMessage className="text-red-500 text-xs" />
                                                            {isDisabled && (
                                                                <p className="text-amber-600 text-xs mt-1">
                                                                    Clear initial due to enter advanced payment
                                                                </p>
                                                            )}
                                                        </FormItem>
                                                    );
                                                }}
                                            />
                                        </div>
                                    )}
                                    <div className="mb-1">
                                        <FormField
                                            control={form.control}
                                            name="extraCommission"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel className="flex items-center text-gray-700 font-medium text-sm">
                                                        Extra Commission (%)
                                                    </FormLabel>
                                                    <FormControl>
                                                        <div className="relative">
                                                            <Input
                                                                type="number"
                                                                className="bg-white border-gray-200 rounded-lg pl-9 py-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
                                                                placeholder="Enter extra commission percentage"
                                                                {...field}
                                                                onChange={(e) => field.onChange(Number(e.target.value))}
                                                                suppressHydrationWarning
                                                            />
                                                            <span className="absolute left-3 top-2.5 text-gray-400 text-sm">%</span>
                                                        </div>
                                                    </FormControl>
                                                    <FormDescription className="text-gray-500 text-xs">
                                                        Additional commission percentage for this customer
                                                    </FormDescription>
                                                    <FormMessage className="text-red-500 text-xs" />
                                                </FormItem>
                                            )}
                                        />
                                    </div>
                                </div>

                                {/* Initial Due Amount (only for new customers) */}


                                {/* Bottom Row - 2 columns for textareas */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <FormField
                                        control={form.control}
                                        name="address"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center text-gray-700 font-medium text-sm">
                                                    Address
                                                </FormLabel>
                                                <FormControl>
                                                    <div className="relative">
                                                        <Textarea
                                                            className="bg-white border-gray-200 rounded-lg pl-9 pt-2 h-[80px] focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
                                                            placeholder="Enter customer address"
                                                            {...field}
                                                            suppressHydrationWarning
                                                        />
                                                        <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                                    </div>
                                                </FormControl>
                                                <FormMessage className="text-red-500 text-xs" />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="notes"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="flex items-center text-gray-700 font-medium text-sm">
                                                    Notes
                                                </FormLabel>
                                                <FormControl>
                                                    <div className="relative">
                                                        <Textarea
                                                            className="bg-white border-gray-200 rounded-lg pl-9 pt-2 h-[80px] focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
                                                            placeholder="Additional notes about this customer"
                                                            {...field}
                                                            suppressHydrationWarning
                                                        />
                                                        <FileText className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                                    </div>
                                                </FormControl>
                                                <FormMessage className="text-red-500 text-xs" />
                                            </FormItem>
                                        )}
                                    />
                                </div>
                            </div>

                            {/* Form Actions */}
                            <div className="flex justify-end space-x-3 mt-1 pt-1">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleCancel}
                                    disabled={isSubmitting}
                                    className="cursor-pointer border-gray-300 hover:bg-gray-100 transition-colors duration-200 px-4 py-2 rounded-lg"
                                >
                                    <X className="h-4 w-4 mr-1" />
                                    Cancel
                                </Button>
                                <Button
                                    type="submit"
                                    disabled={isSubmitting}
                                    className="bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white cursor-pointer shadow-md hover:shadow-lg transition-all duration-200 px-4 py-2 rounded-lg"
                                >
                                    <Save className="h-4 w-4 mr-1" />
                                    {isSubmitting
                                        ? (isEditing ? "Updating..." : "Creating...")
                                        : (isEditing ? "Update Customer" : "Create Customer")
                                    }
                                </Button>
                            </div>
                        </form>
                    </Form>
                </CardContent>
            </Card>
        </div>
    );
}