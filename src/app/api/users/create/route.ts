import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { users, tenants } from '@/db/schema';
import { auth } from '~/auth';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import { createMainBranchForTenant } from '@/lib/branch-utils';

export async function POST(request: Request) {
  try {
    // Check authentication and authorization
    const session = await auth();
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Parse request body
    const { username, email, fullName, password, role, isActive, companyName } = await request.json();

    // Validate required fields
    if (!username || !email || !fullName || !password) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if user with email already exists
    const existingUser = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.email, email)
    });

    if (existingUser) {
      return NextResponse.json(
        { success: false, error: 'User with this email already exists' },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new user
    const userId = uuidv4();
    const newUser = {
      id: userId,
      username,
      email,
      fullName,
      password: hashedPassword,
      role: role || 'tenant',
      isActive: isActive !== undefined ? isActive : true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await db.insert(users).values(newUser);

    // If the user role is tenant, create a tenant record with free trial
    if (role === 'tenant') {
      // Calculate subscription end date (7 days from now)
      const subscriptionEndDate = new Date();
      subscriptionEndDate.setDate(subscriptionEndDate.getDate() + 7);

      // Use company name or generate one if not provided
      const tenantCompanyName = companyName || `${fullName}'s Company`;

      // Create tenant with free trial subscription
      await db.insert(tenants).values({
        id: uuidv4(),
        userId: userId,
        companyName: tenantCompanyName,
        subscriptionType: 'free-trial',
        subscriptionStartDate: new Date(),
        subscriptionEndDate,
        paymentStatus: 'pending',
        paymentDueDate: subscriptionEndDate,
        isActive: true,
        settings: JSON.stringify({
          theme: 'light',
          notifications: true,
          language: 'en'
        })
      });

      // Create a main branch for the tenant
      const mainBranch = await createMainBranchForTenant(userId, tenantCompanyName);

      if (!mainBranch) {
        console.error('Failed to create main branch for tenant:', userId);
      } else {
        console.log('Created main branch for tenant:', mainBranch.id);
      }
    }

    // Return success without password
    const { password: _, ...userWithoutPassword } = newUser;

    return NextResponse.json({
      success: true,
      user: userWithoutPassword,
      message: role === 'tenant' ? 'User created successfully with 7-day free trial' : 'User created successfully'
    });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create user' },
      { status: 500 }
    );
  }
}