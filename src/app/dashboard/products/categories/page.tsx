"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { DotsHorizontalIcon, PlusIcon, ReloadIcon, MagnifyingGlassIcon } from '@radix-ui/react-icons';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useSession } from 'next-auth/react';
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";

// Define the Category type
interface Category {
    id: string;
    name: string;
    description: string | null;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    productCount: number;
}

// Form schema for category creation/update
const categoryFormSchema = z.object({
    name: z.string().min(1, { message: "Category name is required" }),
    description: z.string().optional(),
    isActive: z.boolean().default(true),
});

type CategoryFormValues = z.infer<typeof categoryFormSchema>;

export default function CategoriesPage() {
    const { data: session } = useSession();
    const router = useRouter();

    const [categories, setCategories] = useState<Category[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [searchQuery, setSearchQuery] = useState('');
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [isCategoryDialogOpen, setIsCategoryDialogOpen] = useState(false);
    const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [editingCategory, setEditingCategory] = useState<Category | null>(null);
    const [userPermissions, setUserPermissions] = useState<{
        canView: boolean;
        canCreate: boolean;
        canEdit: boolean;
        canDelete: boolean;
    }>({ canView: false, canCreate: false, canEdit: false, canDelete: false });




    const form = useForm<CategoryFormValues>({
        resolver: zodResolver(categoryFormSchema),
        defaultValues: {
            name: '',
            description: '',
            isActive: true,
        },
    });

    // Fetch categories and user permissions on component mount
    useEffect(() => {
        fetchCategories();
        fetchUserPermissions();
    }, []);

    // Fetch user permissions
    const fetchUserPermissions = async () => {
        try {
            if (session?.user?.role === 'admin' || session?.user?.role === 'tenant') {
                // Admin and tenant users have full permissions
                setUserPermissions({
                    canView: true,
                    canCreate: true,
                    canEdit: true,
                    canDelete: true
                });
                return;
            }

            // For tenant_sale users, fetch their specific permissions from the API
            const response = await fetch(`/api/user/permissions?menuPath=/dashboard/products/categories`);
            const data = await response.json();

            if (data.success) {
                setUserPermissions(data.permissions);

                // If user doesn't have view permission, redirect to dashboard
                if (!data.permissions.canView) {
                    router.push('/dashboard');
                }
            } else {
                console.error('Error fetching permissions:', data.error);
                router.push('/dashboard');
            }
        } catch (error) {
            console.error('Error fetching user permissions:', error);
            router.push('/dashboard');
        }
    };

    // Reset form when dialog opens/closes or editing category changes
    useEffect(() => {
        if (isCategoryDialogOpen && editingCategory) {
            form.reset({
                name: editingCategory.name,
                description: editingCategory.description || '',
                isActive: editingCategory.isActive,
            });
        } else if (isCategoryDialogOpen) {
            form.reset({
                name: '',
                description: '',
                isActive: true,
            });
        }
    }, [isCategoryDialogOpen, editingCategory, form]);

    // Fetch categories from API
    const fetchCategories = async () => {
        setIsLoading(true);
        try {
            const params = new URLSearchParams();
            if (searchQuery) {
                params.append('search', searchQuery);
            }

            const response = await fetch(`/api/products/categories?${params.toString()}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            const data = await response.json();

            if (data.success) {
                setCategories(data.categories);
            } else {
                console.error('Failed to fetch categories:', data.error);
                toast.error(data.error || "Failed to fetch categories");
            }
        } catch (error) {
            console.error('Error fetching categories:', error);
            toast.error("An unexpected error occurred while fetching categories");
        } finally {
            setIsLoading(false);
        }
    };


    // Open category dialog for creation
    const handleAddCategory = () => {
        setEditingCategory(null);
        setIsCategoryDialogOpen(true);
    };

    // Open category dialog for editing
    const handleEditCategory = (category: Category) => {
        setEditingCategory(category);
        setIsCategoryDialogOpen(true);
    };

    // Open delete confirmation dialog
    const handleDeleteClick = (category: Category) => {
        setCategoryToDelete(category);
        setIsDeleteDialogOpen(true);
    };

    // Submit form for category creation/update
    const onSubmit = async (values: CategoryFormValues) => {
        setIsSubmitting(true);

        try {
            const isEditing = !!editingCategory;
            const endpoint = '/api/products/categories';
            const method = isEditing ? 'PUT' : 'POST';

            // Prepare request payload
            const payload = isEditing
                ? { ...values, id: editingCategory.id }
                : { ...values };

            console.log('Sending payload:', payload);

            const response = await fetch(endpoint, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload),
            });

            const data = await response.json();

            if (data.success) {
                toast.success(isEditing
                    ? `${values.name} has been updated successfully`
                    : `${values.name} has been created successfully`);

                fetchCategories();
                setIsCategoryDialogOpen(false);
            } else {
                toast.error(data.error || `Failed to ${isEditing ? 'update' : 'create'} category`);
            }
        } catch (error) {
            console.error(`Error ${editingCategory ? 'updating' : 'creating'} category:`, error);
            toast.error(`An unexpected error occurred while ${editingCategory ? 'updating' : 'creating'} the category`);
        } finally {
            setIsSubmitting(false);
        }
    };

    // Delete a category
    const handleDeleteConfirm = async () => {
        if (!categoryToDelete) return;

        setIsSubmitting(true);

        try {
            const params = new URLSearchParams();
            params.append('id', categoryToDelete.id);
            console.log('Deleting category with ID:', categoryToDelete.id);

            const response = await fetch(`/api/products/categories?${params.toString()}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            const data = await response.json();

            if (data.success) {
                toast.success(`${categoryToDelete.name} has been deleted successfully`);

                fetchCategories();
            } else {
                toast.error(data.error || "Failed to delete category");
            }
        } catch (error) {
            console.error('Error deleting category:', error);
            toast.error("An unexpected error occurred while deleting the category");
        } finally {
            setIsSubmitting(false);
            setIsDeleteDialogOpen(false);
            setCategoryToDelete(null);
        }
    };


    // Handle search input change
    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(e.target.value);
    };

    // Handle search form submission
    const handleSearchSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        handleSearch();
        fetchCategories();
    };

    const handleSearch = () => {
        if (searchQuery) {
            const searchTermEncoded = encodeURIComponent(searchQuery);
            router.push(`/dashboard/products/categories?search=${searchTermEncoded}`);
        } else {
            router.push('/dashboard/products/categories');
        }
    };

    useEffect(() => {
        fetchCategories();
    }, [searchQuery]);

    const handleReset = () => {
        setSearchQuery('');
    };

    return (
        <div className="container mx-auto py-6">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-3xl font-bold">Product Categories</h1>
                {userPermissions.canCreate && (
                    <Button onClick={handleAddCategory} className='cursor-pointer'>
                        <PlusIcon className="h-4 w-4" />
                        Add Category
                    </Button>
                )}
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>Categories</CardTitle>
                    <CardDescription>
                        Manage product categories for your inventory
                    </CardDescription>

                    {/* Search and filter */}
                    <div className="flex items-center space-x-2 mt-2">
                        <form onSubmit={(e) => {
                            e.preventDefault();
                            handleSearchSubmit(e);
                        }} className="flex-1 flex items-center space-x-2">
                            <div className="relative flex-1">
                                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    type="search"
                                    placeholder="Search categories..."
                                    className="pl-8"
                                    value={searchQuery}
                                    onChange={handleSearchChange}
                                />
                            </div>
                            <Button type="submit" variant="outline" className='cursor-pointer'>
                                Search
                            </Button>
                        </form>

                        <Button
                            variant="outline"
                            size="icon"
                            onClick={() => handleReset()}
                            disabled={isLoading}
                            className='cursor-pointer'
                        >
                            {isLoading ? (
                                <ReloadIcon className="h-4 w-4 animate-spin" />
                            ) : (
                                <ReloadIcon className="h-4 w-4" />
                            )}
                        </Button>
                    </div>
                </CardHeader>

                <CardContent>
                    {isLoading ? (
                        // Loading skeleton
                        <div className="space-y-2">
                            {[...Array(5)].map((_, index) => (
                                <div key={index} className="flex items-center space-x-4">
                                    <Skeleton className="h-12 w-full" />
                                </div>
                            ))}
                        </div>
                    ) : categories.length === 0 ? (
                        // Empty state
                        <div className="flex flex-col items-center justify-center py-8 text-center">
                            <p className="text-muted-foreground mb-4">No categories found</p>
                            {userPermissions.canCreate && (
                                <Button onClick={handleAddCategory} variant="outline" className='cursor-pointer'>
                                    Add your first category
                                </Button>
                            )}
                        </div>
                    ) : (
                        // Categories table
                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Name</TableHead>
                                        <TableHead>Description</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead className="text-center">Products</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {categories.map((category) => (
                                        <TableRow key={category.id}>
                                            <TableCell className="font-medium">{category.name}</TableCell>
                                            <TableCell className="max-w-md">
                                                {category.description || "-"}
                                            </TableCell>
                                            <TableCell>
                                                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${category.isActive
                                                    ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                                                    : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                                                    }`}>
                                                    {category.isActive ? 'Active' : 'Inactive'}
                                                </span>
                                            </TableCell>
                                            <TableCell className="text-center">{category.productCount}</TableCell>
                                            <TableCell className="text-right">
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" className="h-8 w-8 p-0 cursor-pointer">
                                                            <span className="sr-only">Open menu</span>
                                                            <DotsHorizontalIcon className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                                        <DropdownMenuSeparator />
                                                        {userPermissions.canEdit && (
                                                            <DropdownMenuItem
                                                                onClick={() => handleEditCategory(category)}
                                                                className='cursor-pointer'
                                                            >
                                                                Edit
                                                            </DropdownMenuItem>
                                                        )}
                                                        {userPermissions.canDelete && (
                                                            <DropdownMenuItem
                                                                className="text-destructive focus:text-destructive cursor-pointer"
                                                                onClick={() => handleDeleteClick(category)}
                                                                disabled={category.productCount > 0}
                                                            >
                                                                Delete
                                                            </DropdownMenuItem>
                                                        )}
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Category Create/Edit Dialog */}
            <Dialog open={isCategoryDialogOpen} onOpenChange={setIsCategoryDialogOpen}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>
                            {editingCategory ? "Edit Category" : "Create Category"}
                        </DialogTitle>
                        <DialogDescription>
                            {editingCategory
                                ? "Update the category details below"
                                : "Add a new product category to organize your inventory"}
                        </DialogDescription>
                    </DialogHeader>

                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                            <FormField
                                control={form.control}
                                name="name"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Name</FormLabel>
                                        <FormControl>
                                            <Input placeholder="Enter category name" {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="description"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Description (Optional)</FormLabel>
                                        <FormControl>
                                            <Textarea
                                                placeholder="Enter category description"
                                                {...field}
                                                value={field.value || ""}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="isActive"
                                render={({ field }) => (
                                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 p-4 border rounded-md">
                                        <FormControl>
                                            <Checkbox
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                            />
                                        </FormControl>
                                        <div className="space-y-1 leading-none">
                                            <FormLabel>Active</FormLabel>
                                            <FormDescription>
                                                Inactive categories won't appear in product dropdowns
                                            </FormDescription>
                                        </div>
                                    </FormItem>
                                )}
                            />

                            <DialogFooter>
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => setIsCategoryDialogOpen(false)}
                                    className='cursor-pointer'
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={isSubmitting} className='cursor-pointer'>
                                    {isSubmitting ? (
                                        <>
                                            <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                                            {editingCategory ? "Updating..." : "Creating..."}
                                        </>
                                    ) : (
                                        <>{editingCategory ? "Update" : "Create"}</>
                                    )}
                                </Button>
                            </DialogFooter>
                        </form>
                    </Form>
                </DialogContent>
            </Dialog>

            {/* Delete Confirmation Dialog */}
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Confirm Deletion</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete the category &quot;
                            {categoryToDelete?.name}&quot;? This action cannot be undone.
                        </DialogDescription>
                    </DialogHeader>

                    <DialogFooter>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => setIsDeleteDialogOpen(false)}
                            className='cursor-pointer'
                        >
                            Cancel
                        </Button>
                        <Button
                            type="button"
                            variant="destructive"
                            onClick={handleDeleteConfirm}
                            disabled={isSubmitting}
                            className='cursor-pointer'
                        >
                            {isSubmitting ? (
                                <>
                                    <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                                    Deleting...
                                </>
                            ) : (
                                "Delete"
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}