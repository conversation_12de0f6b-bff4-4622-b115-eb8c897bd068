import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import {
  orders,
  orderItems,
  products,
  inventoryTransactions,
  customers,
  royaltyTransactions,
  payments,
  userMenuPermissions,
  branches
} from "@/db/schema";
import { auth } from "~/auth";
import { v4 as uuidv4 } from "uuid";
import { eq, and, sum } from "drizzle-orm";
import { withMenuPermission } from "@/middleware/menuPermissions";

async function postHandler(request: NextRequest, _context: any, permissionCheck: any) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Check if user has create permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      const menuPermission = permissionCheck.permissions.find(
        (p: any) => p.menu.path === '/dashboard/sales'
      );

      if (!menuPermission?.canCreate) {
        return NextResponse.json(
          { success: false, error: "You do not have permission to create sales" },
          { status: 403 }
        );
      }
    }

    const data = await request.json();

    // Validate required fields
    if (
      !data.tenantId ||
      !data.branchId ||
      !data.memoNo ||
      !data.date
    ) {
      return NextResponse.json(
        { message: "Missing required fields" },
        { status: 400 }
      );
    }

    // For non-draft orders, require items
    if (data.status !== "draft" && (!data.items || data.items.length === 0)) {
      return NextResponse.json(
        { message: "At least one product is required for non-draft orders" },
        { status: 400 }
      );
    }

    // Ensure items array exists even for draft orders
    if (!data.items) {
      data.items = [];
    }

    // Create the order
    const orderId = uuidv4();
    const tenantId = session.user.tenantId || session.user.id;
    const customerId = data.customerId || null;

    // Use the branch ID from the request data
    const branchId = data.branchId;

    // Verify that the branch exists and belongs to this tenant
    const branch = await db.query.branches.findFirst({
      where: (branches, { eq, and }) => and(
        eq(branches.id, branchId),
        eq(branches.tenantId, tenantId)
      )
    });

    if (!branch) {
      return NextResponse.json(
        { message: "The selected branch is invalid or has been deleted." },
        { status: 400 }
      );
    }

    // Skip inventory check for draft orders
    if (data.status !== "draft") {
      // Check store stock for all items before processing the order
      for (const item of data.items) {
        // Calculate current store stock
        // For tenant_sale users, only check stock in their assigned branch
        // For tenant users, check stock across all branches
        const inStockConditions = [
          eq(inventoryTransactions.type, "in"),
          eq(inventoryTransactions.productId, item.productId),
          eq(inventoryTransactions.tenantId, tenantId)
        ];

        const outStockConditions = [
          eq(inventoryTransactions.type, "out"),
          eq(inventoryTransactions.productId, item.productId),
          eq(inventoryTransactions.tenantId, tenantId)
        ];

        // Always check stock in the selected branch
        inStockConditions.push(eq(inventoryTransactions.branchId, branchId));
        outStockConditions.push(eq(inventoryTransactions.branchId, branchId));

        const inStock = await db
          .select({
            storeTotal: sum(inventoryTransactions.storeQuantity),
          })
          .from(inventoryTransactions)
          .where(and(...inStockConditions));

        const outStock = await db
          .select({
            storeTotal: sum(inventoryTransactions.storeQuantity),
          })
          .from(inventoryTransactions)
          .where(and(...outStockConditions));

        const storeIn = Number(inStock[0]?.storeTotal || 0);
        const storeOut = Number(outStock[0]?.storeTotal || 0);
        const currentStoreStock = storeIn - storeOut;

        // Get product details for error message
        const product = await db.query.products.findFirst({
          where: and(
            eq(products.id, item.productId),
            eq(products.tenantId, tenantId)
          ),
        });

        // Check if there's enough stock in store for the sale
        if (item.quantity > currentStoreStock) {
          return NextResponse.json(
            {
              message: `Insufficient stock for ${product?.name}. Available: ${currentStoreStock}, Requested: ${item.quantity}`,
              product: product?.name,
              available: currentStoreStock,
              requested: item.quantity,
            },
            { status: 400 }
          );
        }
      }
    }

    // Use the branch from the verification step
    const branchToUse = branch;

    await db.transaction(async (tx) => {
      // Insert the order
      await tx.insert(orders).values({
        id: orderId,
        tenantId: tenantId,
        branchId: branchToUse?.id || '',
        customerId: customerId,
        memoNo: data.memoNo,
        date: new Date(data.date),
        status: data.status || "completed",
        paymentMethod: data.paymentMethod || "cash",
        paymentStatus: data.paymentStatus || (data.paidAmount >= data.totalAmount || data.dueAmount <= 0.01 ? "paid" : (data.paidAmount > 0 ? "partial" : "unpaid")),
        subTotal: data.subTotal,
        discountPercentage: data.discountPercentage || 0,
        discountAmount: data.discountAmount || 0,
        totalAmount: data.totalAmount,
        paidAmount: data.paidAmount,
        dueAmount: data.dueAmount || 0,
        previousDue: data.previousDue || 0,
        extCommission: data.extCommission || 0, // Save the extra commission
        extCommissionType: data.extCommissionType || "percentage", // Save the commission type
        notes:
          data.extCommission > 0
            ? `Commission: ${data.extCommission}${data.extCommissionType === "percentage" ? "%" : " fixed"} ${data.notes || ""}`
            : data.notes || null,
        performedBy: session.user.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Create order items and update inventory
      for (const item of data.items) {
        const itemId = uuidv4();

        // Insert order item - handle draft orders specially
        const quantity = data.status === "draft" ? (item.quantity || 0) : item.quantity;
        const unitPrice = data.status === "draft" ? (item.unitPrice || 0) : item.unitPrice;
        const discountPercentage = item.discountPercentage || 0;
        const discountAmount = data.status === "draft" ?
          (item.discountAmount || 0) :
          ((unitPrice * quantity * discountPercentage) / 100);
        const total = data.status === "draft" ? (item.total || 0) : item.total;

        console.log(`Inserting order item for draft order: productId=${item.productId}, quantity=${quantity}`);

        await tx.insert(orderItems).values({
          id: itemId,
          orderId,
          productId: item.productId,
          quantity,
          unitPrice,
          discountPercentage,
          discountAmount,
          total,
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        // Skip inventory transactions for draft orders
        let transactionId = null;

        // Get product to check for royalties
        const product = await tx.query.products.findFirst({
          where: (products, { eq }) => eq(products.id, item.productId),
          with: {
            vendor: true,
          },
        });

        // Only create inventory transactions for non-draft orders
        if (data.status !== "draft") {
          // Create inventory transaction for the sale
          transactionId = uuidv4();

          await tx.insert(inventoryTransactions).values({
            id: transactionId,
            tenantId: tenantId,
            branchId: branchToUse?.id || '',
            productId: item.productId,
            quantity: item.quantity,
            warehouseQuantity: 0, // Only deduct from store, not warehouse
            storeQuantity: item.quantity, // Deduct from store
            unitPrice: item.unitPrice,
            type: "out",
            date: new Date(data.date),
            notes: `Sale - Memo #${data.memoNo}`,
            performedBy: session.user.id,
            createdAt: new Date(),
            updatedAt: new Date(),
          });

          // If the product has royalties and a vendor, create royalty transaction
          if (
            product &&
            product.royaltyType !== "none" &&
            product.royaltyValue &&
            product.royaltyValue > 0 &&
            product.vendorId &&
            transactionId
          ) {
            const royaltyAmount =
              product.royaltyType === "fixed"
                ? (product.royaltyValue || 0) * item.quantity
                : (item.total * (product.royaltyValue || 0)) / 100;

            await tx.insert(royaltyTransactions).values({
              id: uuidv4(),
              vendorId: product.vendorId,
              productId: item.productId,
              transactionId,
              saleAmount: item.total,
              royaltyAmount,
              isPaid: false,
              createdAt: new Date(),
              updatedAt: new Date(),
            });
          }
        }
      }

      // Skip payment handling for draft orders
      if (data.status !== "draft") {
        // Handle payments
        // Use effectivePaidAmount if provided, otherwise use paidAmount
        // effectivePaidAmount is the amount paid for the current order (excluding previous due payment)
        const effectiveAmount = data.effectivePaidAmount !== undefined ? data.effectivePaidAmount : data.paidAmount;

        if (effectiveAmount > 0) {
          // Create a payment record for the current order
          await tx.insert(payments).values({
            id: uuidv4(),
            tenantId: tenantId,
            branchId: branchToUse?.id || '', // Use the same branch as the order
            customerId: customerId,
            orderId: orderId,
            amount: effectiveAmount,
            paymentMethod: data.paymentMethod || "cash",
            paymentReference: data.paymentNo || null,
            paymentType: "sale",
            notes: `Payment for order #${data.memoNo}`,
            performedBy: session.user.id,
            date: new Date(data.date),
            createdAt: new Date(),
            updatedAt: new Date(),
          });

          console.log(`Created payment record for order: amount=${effectiveAmount}, orderId=${orderId}`);
        }

        // Handle previous due payment
        if (data.payPreviousDue && data.previousDuePaymentAmount > 0) {
          console.log(`Processing previous due payment: amount=${data.previousDuePaymentAmount}, previousDue=${data.previousDue}`);

          // Generate a unique ID for the payment
          const paymentId = uuidv4();

          // Create a separate payment record for the previous due payment
          await tx.insert(payments).values({
            id: paymentId,
            tenantId: tenantId,
            branchId: branchToUse?.id || '', // Use the same branch as the order
            customerId: customerId,
            orderId: orderId, // Link to current order for reference
            amount: data.previousDuePaymentAmount,
            paymentMethod: data.paymentMethod || "cash",
            paymentReference: data.paymentNo || null,
            paymentType: "due_payment",
            notes: `Previous due payment with order #${data.memoNo}`,
            performedBy: session.user.id,
            date: new Date(data.date),
            createdAt: new Date(),
            updatedAt: new Date(),
          });

          console.log(`Previous due payment record created for customer ${customerId}, amount: ${data.previousDuePaymentAmount}, paymentId: ${paymentId}`);

          // If there's a customer, update their balance specifically for the previous due payment
          if (customerId) {
            const customer = await tx.query.customers.findFirst({
              where: (customers, { eq }) => eq(customers.id, customerId),
            });

            if (customer) {
              const currentBalance = customer.currentBalance || 0;
              // Subtract the previous due payment amount from the current balance
              const newBalance = currentBalance - data.previousDuePaymentAmount;

              console.log(`Updating customer balance for previous due payment: currentBalance=${currentBalance}, previousDuePayment=${data.previousDuePaymentAmount}, newBalance=${newBalance}`);

              // Update the customer's balance
              await tx
                .update(customers)
                .set({
                  currentBalance: newBalance,
                  updatedAt: new Date(),
                })
                .where(eq(customers.id, customerId));

              console.log(`Customer balance updated for previous due payment: newBalance=${newBalance}`);
            }
          }
        }

        // If there's a customer, update their balance
        if (customerId) {
          const customer = await tx.query.customers.findFirst({
            where: (customers, { eq }) => eq(customers.id, customerId),
          });

          if (customer) {
            // If we've already processed a previous due payment, we need to use the updated balance
            // This is because we've already subtracted the previous due payment amount from the customer's balance
            const customerBalance = customer.currentBalance || 0;
            const newOrderDue = data.dueAmount;
            let newBalance = 0;

            console.log(`Calculating new customer balance for order: currentBalance=${customerBalance}, newOrderDue=${newOrderDue}, paidAmount=${data.paidAmount}, previousDuePaid=${data.payPreviousDue}, previousDueAmount=${data.previousDuePaymentAmount}`);

            // Handle advanced payment (negative balance)
            if (customerBalance < 0) {
              // Customer has advanced payment (negative balance)
              const advancedPayment = Math.abs(customerBalance);
              console.log(`Customer has advanced payment: ${advancedPayment}`);

              // If paid amount is less than or equal to advanced payment, deduct from advanced payment
              if (data.paidAmount <= advancedPayment) {
                // Deduct paid amount from advanced payment (make balance less negative)
                newBalance = customerBalance + data.paidAmount;

                // Add any new due from this order
                newBalance += newOrderDue;

                console.log(`Using advanced payment: newBalance=${newBalance}`);
              } else {
                // Paid amount exceeds advanced payment
                // Use up all advanced payment and calculate remaining
                const remainingPaid = data.paidAmount - advancedPayment;

                // Start with zero balance (advanced payment fully used)
                newBalance = 0;

                // Add new order due and subtract any remaining paid amount
                newBalance = newOrderDue - remainingPaid;

                console.log(`Advanced payment fully used: remainingPaid=${remainingPaid}, newBalance=${newBalance}`);
              }
            } else {
              // Regular due handling for positive balance

              // For the order, we only care about the order's due amount and paid amount
              // The previous due payment has already been handled separately

              // Calculate the effective paid amount for the current order
              // If paying previous due, we need to subtract the previous due payment amount from the total paid amount
              const effectivePaidAmount = data.payPreviousDue && data.previousDuePaymentAmount > 0
                ? Math.max(0, data.paidAmount - data.previousDuePaymentAmount)
                : data.paidAmount;

              // New balance = Current balance + new order due
              // If we've already processed a previous due payment, the customerBalance already reflects that change
              newBalance = customerBalance + newOrderDue;

              console.log(`Regular due handling: customerBalance=${customerBalance}, newOrderDue=${newOrderDue}, effectivePaidAmount=${effectivePaidAmount}, newBalance=${newBalance}`);
            }

            console.log(`Final customer balance for order: ${newBalance}`);

            // Ensure balance is never negative due to rounding errors
            if (Math.abs(newBalance) < 0.01) {
              newBalance = 0;
              console.log(`Adjusted balance to zero due to rounding`);
            }

            await tx
              .update(customers)
              .set({
                currentBalance: newBalance,
                updatedAt: new Date(),
              })
              .where(eq(customers.id, customerId));
          }
        }
      }
    });

    return NextResponse.json(
      { id: orderId, message: "Order created successfully" },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating order:", error);
    return NextResponse.json(
      { message: "Failed to create order", error: (error as Error).message },
      { status: 500 }
    );
  }
}

// Export the route handlers with permission middleware
export const POST = withMenuPermission(postHandler, '/dashboard/sales');
