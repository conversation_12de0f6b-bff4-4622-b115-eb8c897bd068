'use client';

import { usePermissionCheck } from '@/lib/check-permissions';
import Link from "next/link";
import { formatDate, formatCurrency } from "@/lib/utils";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
    <PERSON>,
    CardContent,
    CardHeader,
    CardTitle,
    CardDescription
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Pencil, RefreshCw } from "lucide-react";
import { ServerActionDeleteButton } from "./server-action-delete-button";
import DirectPrintButton from "./direct-print-button";

interface OrderItem {
    id: string;
    quantity: number;
    unitPrice: number;
    discountPercentage: number | null;
    total: number;
    product: {
        name: string;
        category: {
            name: string;
        } | null;
        vendor: {
            name: string;
        } | null;
    };
}

interface Order {
    id: string;
    memoNo: string;
    date: Date;
    status: string;
    paymentMethod: string;
    paymentStatus: string;
    subTotal: number;
    discountPercentage: number | null;
    discountAmount: number | null;
    totalAmount: number;
    paidAmount: number;
    dueAmount: number | null;
    previousDue: number | null;
    extCommission: number | null;
    extCommissionType: "fixed" | "percentage" | null;
    notes: string | null;
    customer: {
        name: string;
        phone: string | null;
        email: string | null;
        address: string | null;
    } | null;
    branch: {
        name: string;
    } | null;
    performer: {
        id: string;
        fullName: string | null;
        username: string | null;
    } | null;
    items: OrderItem[];
}

interface ClientWrapperProps {
    order: Order;
}

export default function ClientWrapper({ order }: ClientWrapperProps) {
    // Check if user has permission to view sales
    const { isLoading } = usePermissionCheck('/dashboard/sales', 'view');
    const router = useRouter();
    const [isEntering, setIsEntering] = useState(true);
    const [isRefreshing, setIsRefreshing] = useState(false);

    // Add entrance animation effect
    useEffect(() => {
        // Set entering state to false after a short delay
        const timer = setTimeout(() => {
            setIsEntering(false);
        }, 300);

        return () => clearTimeout(timer);
    }, []);

    // Function to refresh the page data
    const refreshData = () => {
        setIsRefreshing(true);
        router.refresh();

        // Reset refreshing state after animation completes
        setTimeout(() => {
            setIsRefreshing(false);
        }, 1000);
    };

    // Auto-refresh when component mounts to ensure fresh data
    useEffect(() => {
        refreshData();
    }, []);

    if (isLoading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    return (
        <div className={`transition-opacity duration-500 ${isEntering ? 'opacity-0' : 'opacity-100'}`}>
            <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-2">
                    <Link href="/dashboard/sales">
                        <Button variant="outline" size="icon" className="cursor-pointer">
                            <ArrowLeft className="h-4 w-4 " />
                        </Button>
                    </Link>
                    <h1 className="text-3xl font-bold">Sale Details</h1>
                </div>
                <div className="flex space-x-2">
                    <Button
                        variant="outline"
                        size="icon"
                        onClick={refreshData}
                        disabled={isRefreshing}
                        className="cursor-pointer"
                    >
                        <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                    </Button>
                    <DirectPrintButton id={order.id} />
                    <Link href={`/dashboard/sales/${order.id}/edit`}>
                        <Button variant="outline" className="cursor-pointer flex items-center gap-2">
                            <Pencil className="h-4 w-4 ml-1" /> Edit
                        </Button>
                    </Link>
                    <ServerActionDeleteButton id={order.id} />
                </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <Card>
                    <CardHeader>
                        <CardTitle>Sale Information</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <dl className="space-y-2">
                            <div className="flex justify-between">
                                <dt className="font-medium">Memo No:</dt>
                                <dd>{order.memoNo}</dd>
                            </div>
                            <div className="flex justify-between">
                                <dt className="font-medium">Date:</dt>
                                <dd>{formatDate(order.date)}</dd>
                            </div>
                            <div className="flex justify-between">
                                <dt className="font-medium">Status:</dt>
                                <dd>
                                    <span
                                        className={`px-2 py-1 rounded text-xs ${order.status === "completed"
                                            ? "bg-green-100 text-green-800"
                                            : order.status === "pending"
                                                ? "bg-yellow-100 text-yellow-800"
                                                : "bg-red-100 text-red-800"
                                            }`}
                                    >
                                        {order.status.toUpperCase()}
                                    </span>
                                </dd>
                            </div>
                            <div className="flex justify-between">
                                <dt className="font-medium">Branch:</dt>
                                <dd>{order.branch?.name || "N/A"}</dd>
                            </div>
                            <div className="flex justify-between">
                                <dt className="font-medium">Created By:</dt>
                                <dd>{order.performer?.fullName || "N/A"}</dd>
                            </div>
                        </dl>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>Customer Information</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <dl className="space-y-2">
                            <div className="flex justify-between">
                                <dt className="font-medium">Name:</dt>
                                <dd>{order.customer?.name || "Walk-in Customer"}</dd>
                            </div>
                            {order.customer && (
                                <>
                                    <div className="flex justify-between">
                                        <dt className="font-medium">Phone:</dt>
                                        <dd>{order.customer.phone || "N/A"}</dd>
                                    </div>
                                    <div className="flex justify-between">
                                        <dt className="font-medium">Email:</dt>
                                        <dd>{order.customer.email || "N/A"}</dd>
                                    </div>
                                    <div className="flex justify-between">
                                        <dt className="font-medium">Address:</dt>
                                        <dd>{order.customer.address || "N/A"}</dd>
                                    </div>
                                </>
                            )}
                            <div className="flex justify-between">
                                <dt className="font-medium">Previous Due:</dt>
                                <dd>{formatCurrency(order.previousDue || 0)}</dd>
                            </div>
                        </dl>
                    </CardContent>
                </Card>
            </div>

            <Card className="mb-6">
                <CardHeader>
                    <CardTitle>Items</CardTitle>
                    <CardDescription>Books included in this sale</CardDescription>
                </CardHeader>
                <CardContent>
                    {order.status === "draft" && order.items.length === 0 ? (
                        <div className="bg-amber-50 border border-amber-200 text-amber-800 p-4 rounded-md mb-4">
                            <h3 className="font-bold text-lg mb-2">Draft Order - No Items</h3>
                            <p>This draft order doesn't have any items yet. You need to add items before you can convert it to a regular order.</p>
                            <div className="mt-4">
                                <Link href={`/dashboard/sales/${order.id}/edit`}>
                                    <Button variant="outline" className="cursor-pointer flex items-center gap-2 bg-amber-100 hover:bg-amber-200 border-amber-300">
                                        <Pencil className="h-4 w-4 ml-1" /> Edit Draft Order
                                    </Button>
                                </Link>
                            </div>
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left py-2 px-4">No</th>
                                        <th className="text-left py-2 px-4">Book Name</th>
                                        <th className="text-left py-2 px-4">Category</th>
                                        <th className="text-left py-2 px-4">Author</th>
                                        <th className="text-right py-2 px-4">Qty</th>
                                        <th className="text-right py-2 px-4">Unit Price</th>
                                        <th className="text-right py-2 px-4">Discount</th>
                                        <th className="text-right py-2 px-4">Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {order.items.map((item, index) => (
                                        <tr key={item.id} className="border-b">
                                            <td className="py-2 px-4">{index + 1}</td>
                                            <td className="py-2 px-4">{item.product.name}</td>
                                            <td className="py-2 px-4">{item.product.category?.name || "N/A"}</td>
                                            <td className="py-2 px-4">{item.product.vendor?.name || "N/A"}</td>
                                            <td className="py-2 px-4 text-right">{item.quantity}</td>
                                            <td className="py-2 px-4 text-right">{formatCurrency(item.unitPrice)}</td>
                                            <td className="py-2 px-4 text-right">{item.discountPercentage}%</td>
                                            <td className="py-2 px-4 text-right">{formatCurrency(item.total)}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}
                </CardContent>
            </Card>

            <Card className="mb-6">
                <CardHeader>
                    <CardTitle>Payment Information</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <dl className="space-y-2">
                                <div className="flex justify-between">
                                    <dt className="font-medium">Payment Method:</dt>
                                    <dd className="capitalize">{order.paymentMethod.replace("_", " ")}</dd>
                                </div>
                                <div className="flex justify-between">
                                    <dt className="font-medium">Payment Status:</dt>
                                    <dd>
                                        <span
                                            className={`px-2 py-1 rounded text-xs ${order.paymentStatus === "paid"
                                                ? "bg-green-100 text-green-800"
                                                : order.paymentStatus === "partial"
                                                    ? "bg-yellow-100 text-yellow-800"
                                                    : "bg-red-100 text-red-800"
                                                }`}
                                        >
                                            {order.paymentStatus.toUpperCase()}
                                        </span>
                                    </dd>
                                </div>
                            </dl>
                        </div>
                        <div>
                            <dl className="space-y-2">
                                {order.notes &&
                                    <div className="flex justify-center items-center">
                                        <dt className="font-medium">{order.notes}</dt>
                                    </div>
                                }
                                <div className="flex justify-between">
                                    <dt className="font-medium">Subtotal:</dt>
                                    <dd>{formatCurrency(order.subTotal)}</dd>
                                </div>
                                {/* <div className="flex justify-between">
                                    <dt className="font-medium">Discount ({order.discountPercentage}%):</dt>
                                    <dd>{formatCurrency(order.discountAmount || 0)}</dd>
                                </div> */}
                                {(order.extCommission || 0) > 0 && (
                                    <div className="flex justify-between">
                                        <dt className="font-medium">
                                            Commission {order.extCommissionType === "percentage" ? `(${order.extCommission}%)` : "(Fixed)"}:
                                        </dt>
                                        <dd>
                                            {formatCurrency(order.extCommissionType === "percentage"
                                                ? ((order.subTotal || 0) * (order.extCommission || 0) / 100)
                                                : (order.extCommission || 0))}
                                        </dd>
                                    </div>
                                )}
                                <div className="flex justify-between">
                                    <dt className="font-medium">Total Amount:</dt>
                                    <dd className="font-bold">{formatCurrency(order.totalAmount)}</dd>
                                </div>
                                <div className="flex justify-between">
                                    <dt className="font-medium">Paid Amount:</dt>
                                    <dd>{formatCurrency(order.paidAmount)}</dd>
                                </div>
                                <div className="flex justify-between border-t pt-2 mt-2">
                                    <dt className="font-medium">Due Amount:</dt>
                                    <dd className="font-bold">{formatCurrency(order.dueAmount || 0)}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
