import { auth } from "~/auth";
import { redirect } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import PaymentMethodForm from "../_components/payment-method-form";

export default async function NewPaymentMethodPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/");
  }

  // Only tenant and admin users can create payment methods
  if (session.user.role !== 'tenant' && session.user.role !== 'admin') {
    redirect("/dashboard");
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Add Payment Method</h1>
        <p className="text-muted-foreground">
          Create a new payment method for your business
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Payment Method Details</CardTitle>
          <CardDescription>
            Enter the details for the new payment method. This will be available for use in sales and transactions.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PaymentMethodForm />
        </CardContent>
      </Card>
    </div>
  );
}
