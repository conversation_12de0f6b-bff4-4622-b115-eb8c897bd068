import React from "react";
import { db } from "@/lib/db";
import { orders, customers, branches } from "@/db/schema";
import { desc, and, eq, sql } from "drizzle-orm";
import { auth } from "../../../../auth";
import { redirect } from "next/navigation";
import ClientWrapper from "./_components/client-wrapper";

// Add export for no caching
export const dynamic = 'force-dynamic';
export const revalidate = 0;

export default async function SalesPage({
    searchParams,
}: {
    searchParams: Promise<{ search?: string; deleted?: string; error?: string; branch?: string; status?: string }>;
}) {
    const session = await auth();
    if (!session?.user) {
        redirect("/api/auth/signin");
    }

    const tenantId = session.user.tenantId || session.user.id;
    if (!tenantId) {
        redirect("/dashboard");
    }

    const params = await searchParams;
    const search = params.search || "";
    const branchFilter = params.branch || "";
    const statusFilter = params.status || "";

    // Get all branches for this tenant
    const allBranches = await db.query.branches.findMany({
        where: eq(branches.tenantId, tenantId),
        orderBy: (branches, { asc }) => [asc(branches.name)]
    });

    // Determine which orders to fetch based on user role and branch filter
    let whereConditions = [eq(orders.tenantId, tenantId)];

    // For tenant_sale users, only show orders from their assigned branch
    if (session.user.role === 'tenant_sale' && session.user.branchId) {
        whereConditions.push(eq(orders.branchId, session.user.branchId));
    }
    // For tenant users with branch filter, filter by the selected branch
    else if (session.user.role === 'tenant' && branchFilter) {
        whereConditions.push(eq(orders.branchId, branchFilter));
    }

    // Filter by status if provided
    if (statusFilter) {
        // Ensure statusFilter is one of the allowed types
        const validStatus = statusFilter as "pending" | "completed" | "cancelled" | "draft";
        whereConditions.push(eq(orders.status, validStatus));
    }

    // Get all orders for this tenant with filtering
    let query = db.query.orders.findMany({
        where: and(...whereConditions),
        orderBy: [desc(orders.createdAt)],
        with: {
            customer: true,
            branch: true, // Include branch information
            performer: {
                columns: {
                    id: true,
                    fullName: true,
                    username: true,
                }
            },
            items: {
                with: {
                    product: true,
                },
            },
        },
    });

    // Apply search filter if provided
    if (search) {
        const customerIds = await db.select({ id: customers.id })
            .from(customers)
            .where(and(
                eq(customers.tenantId, tenantId),
                sql`LOWER(${customers.name}) LIKE LOWER(${'%' + search + '%'})`
            ));

        const customerIdList = customerIds.map(c => c.id);

        // Start with base conditions (tenant ID, possibly branch ID, and status)
        const searchWhereConditions = [...whereConditions];

        // Add search conditions
        if (customerIdList.length > 0) {
            // Create a combined SQL condition for memo number or customer ID
            const combinedCondition = sql`(LOWER(${orders.memoNo}) LIKE LOWER(${'%' + search + '%'}) OR ${orders.customerId} IN (${customerIdList.join(',')}))`;
            searchWhereConditions.push(combinedCondition);
        } else {
            searchWhereConditions.push(
                sql`LOWER(${orders.memoNo}) LIKE LOWER(${'%' + search + '%'})`
            );
        }

        query = db.query.orders.findMany({
            where: and(...searchWhereConditions),
            orderBy: [desc(orders.createdAt)],
            with: {
                customer: true,
                branch: true, // Include branch information
                performer: {
                    columns: {
                        id: true,
                        fullName: true,
                        username: true,
                    }
                },
                items: {
                    with: {
                        product: true,
                    },
                },
            },
        });
    }

    const recentOrders = await query;

    // Get user's branch information if they are a tenant_sale user
    let userBranch = null;
    if (session.user.role === 'tenant_sale' && session.user.branchId) {
        userBranch = allBranches.find(branch => branch.id === session.user.branchId) || null;
    }

    return (
        <div className="container mx-auto py-10">
            <ClientWrapper
                initialOrders={recentOrders}
                search={search}
                branches={allBranches}
                selectedBranchId={branchFilter || (session.user.role === 'tenant_sale' ? session.user.branchId : '')}
                userRole={session.user.role}
                userBranch={userBranch}
            />
        </div>
    );
}