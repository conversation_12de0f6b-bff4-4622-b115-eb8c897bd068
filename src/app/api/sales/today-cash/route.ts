import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { orders } from "@/db/schema";
import { auth } from "~/auth";
import { and, eq, sql, sum } from "drizzle-orm";
import { getCachedData } from "@/lib/cache";
import { withCacheHeaders } from "@/middleware/cacheHeaders";

async function getHandler(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ success: false, message: "Unauthorized" }, { status: 401 });
    }

    // Get tenant ID from session based on user role
    const tenantId = session.user.tenantId || session.user.id;

    // Get today's date at the start of the day (midnight)
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Format today's date as ISO string for SQL comparison
    const todayStr = today.toISOString().split('T')[0];

    // Use cache for better performance
    const cacheKey = `today_sales_${tenantId}_${todayStr}_${session.user.role}_${session.user.branchId || ''}`;

    const result = await getCachedData(
      cacheKey,
      async () => {
        // Get tomorrow's date at the start of the day (midnight)
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        // Base conditions for all sales (for total sales)
        let totalSalesConditions = [
          eq(orders.tenantId, tenantId),
          sql`${orders.date} >= ${today.getTime()}`,
          sql`${orders.date} < ${tomorrow.getTime()}`
        ];

        // For tenant_sale users, only show orders from their assigned branch
        if (session.user.role === 'tenant_sale' && session.user.branchId) {
          totalSalesConditions.push(eq(orders.branchId, session.user.branchId));
        }

        // Get total sales for today
        const totalSalesResult = await db
          .select({ total: sum(orders.totalAmount) })
          .from(orders)
          .where(and(...totalSalesConditions));

        // Cash sales conditions (add payment method filter)
        const cashSalesConditions = [
          ...totalSalesConditions,
          eq(orders.paymentMethod, "cash")
        ];

        // Get cash sales for today
        const cashSalesResult = await db
          .select({ total: sum(orders.paidAmount) })
          .from(orders)
          .where(and(...cashSalesConditions));

        return {
          totalSales: totalSalesResult[0]?.total || 0,
          cashSales: cashSalesResult[0]?.total || 0
        };
      },
      5 * 60 * 1000 // Cache for 5 minutes
    );

    const totalCashSales = result.cashSales;
    const totalSales = result.totalSales;

    return NextResponse.json({
      success: true,
      totalCashSales,
      totalSales
    });
  } catch (error) {
    console.error("Error fetching today's cash sales:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch today's cash sales",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// Export the route handler with caching
export const GET = withCacheHeaders(getHandler, 300); // Cache for 5 minutes
