import { NextRequest, NextResponse } from "next/server";
import { auth } from "../../../../auth";
import { db } from "@/lib/db";
import {
  products,
  inventoryTransactions,
  tenantUsers,
  stockTransactionItems,
  users,
  branches,
} from "@/db/schema";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import { eq, and, like, desc, asc, or, sum, count, max, sql } from "drizzle-orm";

// Schema for validating inventory updates
const inventoryUpdateSchema = z
  .object({
    productId: z.string().min(1, "Product ID is required"),
    branchId: z.string().min(1, "Branch ID is required"),
    quantity: z.number().int().min(0, "Quantity must be a positive number"),
    warehouseQuantity: z
      .number()
      .int()
      .min(0, "Warehouse quantity must be a positive number"),
    storeQuantity: z
      .number()
      .int()
      .min(0, "Store quantity must be a positive number"),
    transactionType: z.enum(["in", "out", "sale", "transfer"], {
      required_error: "Transaction type is required",
    }),
    unitPrice: z.number().min(0, "Unit price must be a positive number"),
    notes: z.string().nullable().optional(),
    referenceId: z.string().nullable().optional(),
  })
  .refine(
    (data) => data.warehouseQuantity + data.storeQuantity === data.quantity,
    {
      message:
        "Sum of warehouse and store quantities must equal total quantity",
      path: ["quantity"],
    }
  );

// GET /api/inventory - Get inventory status for products
export async function GET(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Parse query parameters
    const url = new URL(req.url);
    const queryTenantId = url.searchParams.get("tenantId");
    const branchId = url.searchParams.get("branchId");
    const productId = url.searchParams.get("productId");
    const lowStock = url.searchParams.get("lowStock") === "true";
    const search = url.searchParams.get("search");
    const sortBy = url.searchParams.get("sortBy") || "quantity";
    const sortOrder = url.searchParams.get("sortOrder") || "asc";

    // Get tenant ID from session based on user role
    let tenantId: string;
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      // For sales users, use their associated tenant's ID
      tenantId = session.user.tenantId;
    } else {
      // For tenant users, use their own ID
      tenantId = session.user.id;
    }
    console.log('GET /api/inventory - Using tenant ID:', tenantId, 'for user role:', session.user.role);

    // For this application, the user ID is used as the tenant ID
    // This is because the schema has inconsistencies where some tables reference users.id and others reference tenants.id

    // First, get all products
    const productsQuery = productId
      ? and(eq(products.id, productId), eq(products.tenantId, tenantId))
      : eq(products.tenantId, tenantId);

    const allProducts = await db.query.products.findMany({
      where: productsQuery,
      columns: {
        id: true,
        name: true,
        code: true,
        costPrice: true,
        sellingPrice: true,
        unit: true,
        categoryId: true,
        isActive: true,
      },
      with: {
        category: {
          columns: {
            name: true
          }
        }
      }
    });

    // Build the where clause for transactions based on filters
    let whereClause = and(eq(inventoryTransactions.tenantId, tenantId));

    // For tenant_sale users, only show inventory from their assigned branch
    if (session.user.role === 'tenant_sale' && session.user.branchId) {
      whereClause = and(
        whereClause,
        eq(inventoryTransactions.branchId, session.user.branchId)
      );
    }
    // Otherwise, add branch filter if specified in the request
    else if (branchId) {
      whereClause = and(
        whereClause,
        eq(inventoryTransactions.branchId, branchId)
      );
    }

    // Get "in" transaction quantities by product
    console.log('Querying inventory transactions with tenant ID:', tenantId);
    const inTransactions = await db
      .select({
        productId: inventoryTransactions.productId,
        branchId: inventoryTransactions.branchId,
        totalIn: sum(inventoryTransactions.quantity),
        warehouseIn: sum(inventoryTransactions.warehouseQuantity),
        storeIn: sum(inventoryTransactions.storeQuantity),
      })
      .from(inventoryTransactions)
      .where(and(whereClause, eq(inventoryTransactions.type, "in")))
      .groupBy(inventoryTransactions.productId, inventoryTransactions.branchId);

    console.log('In transactions found:', inTransactions.length);

    // Get "out" transaction quantities by product
    const outTransactions = await db
      .select({
        productId: inventoryTransactions.productId,
        branchId: inventoryTransactions.branchId,
        totalOut: sum(inventoryTransactions.quantity),
        warehouseOut: sum(inventoryTransactions.warehouseQuantity),
        storeOut: sum(inventoryTransactions.storeQuantity),
      })
      .from(inventoryTransactions)
      .where(and(whereClause, eq(inventoryTransactions.type, "out")))
      .groupBy(inventoryTransactions.productId, inventoryTransactions.branchId);

    // Get the most recent transaction date for each product
    const lastTransactions = await db
      .select({
        productId: inventoryTransactions.productId,
        branchId: inventoryTransactions.branchId,
        lastUpdate: max(inventoryTransactions.date),
      })
      .from(inventoryTransactions)
      .where(whereClause)
      .groupBy(inventoryTransactions.productId, inventoryTransactions.branchId);

    // Fetch branches for the tenant
    console.log('Fetching branches for tenant ID:', tenantId);
    const branchList = await db
      .select()
      .from(branches)
      .where(eq(branches.tenantId, tenantId));

    console.log('Found branches:', branchList.map(b => ({ id: b.id, name: b.name })));

    // If no branches found, try to find the branch used in the transaction
    let additionalBranches = [];
    if (branchList.length === 0 && inTransactions.length > 0) {
      console.log('No branches found, looking for branches from transactions');
      const transactionBranchIds = new Set([...inTransactions, ...outTransactions].map(t => t.branchId));
      console.log('Transaction branch IDs:', Array.from(transactionBranchIds));

      if (transactionBranchIds.size > 0) {
        // Get branch IDs as an array
        const branchIds = Array.from(transactionBranchIds);

        // Fetch each branch individually
        for (const branchId of branchIds) {
          const branch = await db
            .select()
            .from(branches)
            .where(eq(branches.id, branchId))
            .get();

          if (branch) {
            additionalBranches.push(branch);
          }
        }

        console.log('Found additional branches:', additionalBranches.map(b => ({ id: b.id, name: b.name })));
        branchList.push(...additionalBranches);
      }
    }

    // Create a map for branch lookup
    const branchMap = new Map(branchList.map((branch) => [branch.id, branch]));

    // Create maps for quick lookup - use composite key of productId-branchId
    const makeCompositeKey = (productId: string, branchId: string) =>
      `${productId}-${branchId}`;
    const inMap = new Map(
      inTransactions.map((t) => [
        makeCompositeKey(t.productId, t.branchId),
        Number(t.totalIn || 0),
      ])
    );
    const outMap = new Map(
      outTransactions.map((t) => [
        makeCompositeKey(t.productId, t.branchId),
        Number(t.totalOut || 0),
      ])
    );
    const warehouseInMap = new Map(
      inTransactions.map((t) => [
        makeCompositeKey(t.productId, t.branchId),
        Number(t.warehouseIn || 0),
      ])
    );
    const warehouseOutMap = new Map(
      outTransactions.map((t) => [
        makeCompositeKey(t.productId, t.branchId),
        Number(t.warehouseOut || 0),
      ])
    );
    const storeInMap = new Map(
      inTransactions.map((t) => [
        makeCompositeKey(t.productId, t.branchId),
        Number(t.storeIn || 0),
      ])
    );
    const storeOutMap = new Map(
      outTransactions.map((t) => [
        makeCompositeKey(t.productId, t.branchId),
        Number(t.storeOut || 0),
      ])
    );
    const dateMap = new Map(
      lastTransactions.map((t) => [
        makeCompositeKey(t.productId, t.branchId),
        t.lastUpdate,
      ])
    );

    // Create a set of unique productId-branchId combinations
    const productBranchPairs = new Set([
      ...inTransactions.map((t) => makeCompositeKey(t.productId, t.branchId)),
      ...outTransactions.map((t) => makeCompositeKey(t.productId, t.branchId)),
    ]);

    console.log('Product-branch pairs:', Array.from(productBranchPairs));
    console.log('All products:', allProducts.length);
    console.log('Branch list:', branchList.length);

    // Combine data
    const inventoryItems = [];

    // If no branches were found but we have transactions, create a dummy branch for each transaction
    if (branchList.length === 0 && productBranchPairs.size > 0) {
      console.log('Creating inventory items directly from transactions');

      // Process each product-branch pair from transactions
      for (const compositeKey of productBranchPairs) {
        const [productId, branchId] = compositeKey.split('-');
        const product = allProducts.find(p => p.id === productId);

        if (!product) {
          console.log('Product not found for ID:', productId);
          continue;
        }

        const inStock = inMap.get(compositeKey) || 0;
        const outStock = outMap.get(compositeKey) || 0;
        const currentStock = inStock - outStock;

        const warehouseInStock = warehouseInMap.get(compositeKey) || 0;
        const warehouseOutStock = warehouseOutMap.get(compositeKey) || 0;
        const warehouseStock = warehouseInStock - warehouseOutStock;

        const storeInStock = storeInMap.get(compositeKey) || 0;
        const storeOutStock = storeOutMap.get(compositeKey) || 0;
        const storeStock = storeInStock - storeOutStock;

        const lastUpdate = dateMap.get(compositeKey) || null;

        // Create a dummy branch if not found
        const branch = branchMap.get(branchId) || {
          id: branchId,
          name: 'Unknown Branch',
          code: 'UNK',
          tenantId: tenantId,
          isMain: false,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        inventoryItems.push({
          id: compositeKey,
          tenantId,
          branchId,
          productId,
          quantity: currentStock,
          warehouseStock,
          storeStock,
          lastStockUpdate: lastUpdate,
          createdAt: null,
          updatedAt: null,
          product: {
            ...product,
            retailPrice: product.sellingPrice,
            categoryName: product.category?.name || "",
          },
          branch,
        });
      }
    } else {
      // Normal processing with branches
      // For tenant_sale users, only show inventory from their assigned branch
      // Otherwise, if branchId is specified, only return inventory for that branch
      const effectiveBranchId = session.user.role === 'tenant_sale' && session.user.branchId ?
        session.user.branchId : branchId;

      if (effectiveBranchId) {
        for (const product of allProducts) {
          const compositeKey = makeCompositeKey(product.id, effectiveBranchId);

          const inStock = inMap.get(compositeKey) || 0;
          const outStock = outMap.get(compositeKey) || 0;
          const currentStock = inStock - outStock;

          const warehouseInStock = warehouseInMap.get(compositeKey) || 0;
          const warehouseOutStock = warehouseOutMap.get(compositeKey) || 0;
          const warehouseStock = warehouseInStock - warehouseOutStock;

          const storeInStock = storeInMap.get(compositeKey) || 0;
          const storeOutStock = storeOutMap.get(compositeKey) || 0;
          const storeStock = storeInStock - storeOutStock;

          const lastUpdate = dateMap.get(compositeKey) || null;

          inventoryItems.push({
            id: makeCompositeKey(product.id, effectiveBranchId),
            tenantId,
            branchId: effectiveBranchId,
            productId: product.id,
            quantity: currentStock,
            warehouseStock,
            storeStock,
            lastStockUpdate: lastUpdate,
            createdAt: null,
            updatedAt: null,
            product: {
              ...product,
              retailPrice: product.sellingPrice,
              categoryName: "",
            },
            branch: branchMap.get(effectiveBranchId) || null,
          });
        }
      } else {
        // Return inventory for all branches
        for (const product of allProducts) {
          for (const branch of branchList) {
            const compositeKey = makeCompositeKey(product.id, branch.id);

            // Skip if there's no transaction for this product-branch combination
            console.log('Checking composite key:', compositeKey, 'Has transaction:', productBranchPairs.has(compositeKey), 'Product ID filter:', productId);
            if (!productBranchPairs.has(compositeKey) && !productId) {
              console.log('Skipping item with no transactions:', compositeKey);
              continue;
            }

            const inStock = inMap.get(compositeKey) || 0;
            const outStock = outMap.get(compositeKey) || 0;
            const currentStock = inStock - outStock;

            const warehouseInStock = warehouseInMap.get(compositeKey) || 0;
            const warehouseOutStock = warehouseOutMap.get(compositeKey) || 0;
            const warehouseStock = warehouseInStock - warehouseOutStock;

            const storeInStock = storeInMap.get(compositeKey) || 0;
            const storeOutStock = storeOutMap.get(compositeKey) || 0;
            const storeStock = storeInStock - storeOutStock;

            const lastUpdate = dateMap.get(compositeKey) || null;

            inventoryItems.push({
              id: compositeKey,
              tenantId,
              branchId: branch.id,
              productId: product.id,
              quantity: currentStock,
              warehouseStock,
              storeStock,
              lastStockUpdate: lastUpdate,
              createdAt: null,
              updatedAt: null,
              product: {
                ...product,
                retailPrice: product.sellingPrice,
                categoryName: "",
              },
              branch,
            });
          }
        }
      }
    }

    // Copy for filtering
    let filteredItems = [...inventoryItems];

    // Apply search filter
    if (search) {
      filteredItems = filteredItems.filter(
        (item) =>
          item.product.name.toLowerCase().includes(search.toLowerCase()) ||
          (item.product.code &&
            item.product.code.toLowerCase().includes(search.toLowerCase())) ||
          (item.branch?.name &&
            item.branch.name.toLowerCase().includes(search.toLowerCase()))
      );
    }

    // Apply low stock filter (items with quantity <= 10)
    if (lowStock) {
      filteredItems = filteredItems.filter((item) => item.quantity <= 10);
    }

    // Apply sorting
    if (sortBy === "quantity") {
      filteredItems.sort((a, b) => {
        return sortOrder === "asc"
          ? a.quantity - b.quantity
          : b.quantity - a.quantity;
      });
    } else if (sortBy === "name") {
      filteredItems.sort((a, b) => {
        return sortOrder === "asc"
          ? a.product.name.localeCompare(b.product.name)
          : b.product.name.localeCompare(a.product.name);
      });
    } else if (sortBy === "price") {
      filteredItems.sort((a, b) => {
        return sortOrder === "asc"
          ? a.product.sellingPrice - b.product.sellingPrice
          : b.product.sellingPrice - a.product.sellingPrice;
      });
    } else if (sortBy === "lastUpdated") {
      filteredItems.sort((a, b) => {
        const dateA = a.lastStockUpdate;
        const dateB = b.lastStockUpdate;

        // Handle null dates
        if (!dateA && !dateB) return 0;
        if (!dateA) return sortOrder === "asc" ? -1 : 1;
        if (!dateB) return sortOrder === "asc" ? 1 : -1;

        return sortOrder === "asc"
          ? dateA.getTime() - dateB.getTime()
          : dateB.getTime() - dateA.getTime();
      });
    } else if (sortBy === "branch") {
      filteredItems.sort((a, b) => {
        const branchNameA = a.branch?.name || "";
        const branchNameB = b.branch?.name || "";
        return sortOrder === "asc"
          ? branchNameA.localeCompare(branchNameB)
          : branchNameB.localeCompare(branchNameA);
      });
    }

    console.log('Returning inventory items:', filteredItems.length);
    console.log('Sample item:', filteredItems.length > 0 ? JSON.stringify(filteredItems[0]) : 'No items');

    return NextResponse.json({
      success: true,
      inventory: filteredItems,
    });
  } catch (error) {
    console.error("Error fetching inventory:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch inventory data" },
      { status: 500 }
    );
  }
}

// POST /api/inventory - Add new inventory
export async function POST(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await req.json();
    const parsedData = inventoryUpdateSchema.safeParse(body);

    if (!parsedData.success) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid data",
          details: parsedData.error.format(),
        },
        { status: 400 }
      );
    }

    const data = parsedData.data;
    // Get tenant ID from session based on user role
    let tenantId: string;
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      // For sales users, use their associated tenant's ID
      tenantId = session.user.tenantId;
    } else {
      // For tenant users, use their own ID
      tenantId = session.user.id;
    }
    console.log('POST /api/inventory - Using tenant ID:', tenantId, 'for user role:', session.user.role);

    // For this application, the user ID is used as the tenant ID
    // This is because the schema has inconsistencies where some tables reference users.id and others reference tenants.id

    // Create a new transaction
    const newTransaction = {
      id: uuidv4(),
      tenantId: tenantId, // Use session.user.tenantId as the tenant ID
      branchId: data.branchId,
      productId: data.productId,
      quantity: data.quantity,
      warehouseQuantity: data.warehouseQuantity,
      storeQuantity: data.storeQuantity,
      unitPrice: data.unitPrice,
      type: data.transactionType,
      date: new Date(),
      notes: data.notes || null,
      performedBy: session.user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
      // Removed sourceBranchId and targetBranchId as they don't exist in the database
    };
    console.log('Creating inventory transaction:', newTransaction)
    // Insert the transaction
    const result = await db
      .insert(inventoryTransactions)
      .values(newTransaction)
      .returning();

    if (result.length > 0) {
      // Successfully added
      return NextResponse.json({
        success: true,
        message: "Inventory added successfully",
        transaction: result[0],
      });
    } else {
      throw new Error("Failed to insert inventory transaction");
    }
  } catch (error) {
    console.error("Error adding inventory:", error);
    return NextResponse.json(
      { success: false, error: "Failed to add inventory" },
      { status: 500 }
    );
  }
}
