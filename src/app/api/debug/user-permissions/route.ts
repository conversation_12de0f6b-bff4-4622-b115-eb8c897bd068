import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { userMenuPermissions } from "@/db/schema";
import { auth } from "~/auth";
import { eq, and } from "drizzle-orm";
import { ALL_MENUS } from "@/lib/menu-list";

// GET /api/debug/user-permissions - Get current user's menu permissions
export async function GET(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const menuPath = url.searchParams.get("menuPath") || '/dashboard/reports/orders';

    // Admin and tenant users have full permissions for all menus
    if (session.user.role === 'admin' || session.user.role === 'tenant') {
      return NextResponse.json({
        success: true,
        user: {
          id: session.user.id,
          role: session.user.role,
          tenantId: session.user.tenantId
        },
        menuPath,
        permissions: {
          canView: true,
          canCreate: true,
          canEdit: true,
          canDelete: true
        },
        message: `${session.user.role} users have full permissions`
      });
    }

    // For tenant_sale users, get their specific permissions
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      // First check if permissions are already in the session
      if (session.user.menuPermissions && session.user.menuPermissions.length > 0) {
        console.log('Using menu permissions from session for debug API request');

        // Find the permission for the specified menu
        const menuPermission = session.user.menuPermissions.find(p => p.path === menuPath);

        return NextResponse.json({
          success: true,
          user: {
            id: session.user.id,
            role: session.user.role,
            tenantId: session.user.tenantId
          },
          menuPath,
          permissions: menuPermission ? {
            canView: menuPermission.canView,
            canCreate: menuPermission.canCreate,
            canEdit: menuPermission.canEdit,
            canDelete: menuPermission.canDelete
          } : {
            canView: false,
            canCreate: false,
            canEdit: false,
            canDelete: false
          },
          source: 'session',
          allPermissions: session.user.menuPermissions
        });
      }
      // Fallback to database query if permissions are not in session
      else {
        console.log('Fetching menu permissions from database for debug API request');

        // Get user's permissions from database
        const userPermissions = await db.query.userMenuPermissions.findMany({
          where: and(
            eq(userMenuPermissions.userId, session.user.id),
            eq(userMenuPermissions.tenantId, session.user.tenantId)
          )
        });

        // Get menu details from file
        const permissions = userPermissions.map(permission => {
          const menu = ALL_MENUS.find(m => m.id === permission.menuId);
          return {
            ...permission,
            menu: menu || { path: '', name: 'unknown', displayName: 'Unknown Menu' }
          };
        });

        // Find the permission for the specified menu
        const menuPermission = permissions.find(p => p.menu.path === menuPath);

        return NextResponse.json({
          success: true,
          user: {
            id: session.user.id,
            role: session.user.role,
            tenantId: session.user.tenantId
          },
          menuPath,
          permissions: menuPermission ? {
            canView: menuPermission.canView,
            canCreate: menuPermission.canCreate,
            canEdit: menuPermission.canEdit,
            canDelete: menuPermission.canDelete
          } : {
            canView: false,
            canCreate: false,
            canEdit: false,
            canDelete: false
          },
          source: 'database',
          allPermissions: permissions.map(p => ({
            menuId: p.menuId,
            path: p.menu.path,
            name: p.menu.name,
            displayName: p.menu.displayName,
            canView: p.canView,
            canCreate: p.canCreate,
            canEdit: p.canEdit,
            canDelete: p.canDelete
          }))
        });
      }
    }

    // Default response for other roles
    return NextResponse.json({
      success: true,
      user: {
        id: session.user.id,
        role: session.user.role
      },
      menuPath,
      permissions: {
        canView: false,
        canCreate: false,
        canEdit: false,
        canDelete: false
      },
      message: `User role ${session.user.role} has no permissions by default`
    });
  } catch (error) {
    console.error("Error fetching user permissions:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch user permissions",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
