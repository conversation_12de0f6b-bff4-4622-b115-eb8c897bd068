import { NextRequest, NextResponse } from "next/server";
import { auth } from "~/auth";
import { db } from "@/lib/db";
import { productCategories, userMenuPermissions } from "@/db/schema";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import { eq, and, like, desc, asc, or } from "drizzle-orm";
import { withMenuPermission, checkMenuPermission } from "@/middleware/menuPermissions";

// Schema for validating product category creation and updates
const productCategorySchema = z.object({
  name: z.string().min(1, "Category name is required"),
  description: z.string().nullable().optional(),
  isActive: z.boolean().default(true),
});

// GET /api/products/categories - Get all product categories for the tenant
async function getHandler(req: NextRequest, context: any, permissionCheck: any) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get tenant ID directly from session
    let tenantId = session.user.id;

    // For tenant users, the tenantId is their own id
    // For tenant_sale users, use the tenantId from session
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      tenantId = session.user.tenantId;
    }

    console.log('Using tenantId:', tenantId, 'for user role:', session.user.role);

    // Check if user has view permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      // First check if permissions are in the session
      if (session.user.menuPermissions && session.user.menuPermissions.length > 0) {
        console.log('Using menu permissions from session for categories API');
        const menuPermission = session.user.menuPermissions.find(
          (p: any) => p.path === '/dashboard/products/categories'
        );

        if (!menuPermission?.canView) {
          return NextResponse.json(
            { success: false, error: "You do not have permission to view categories" },
            { status: 403 }
          );
        }
      }
      // If not in session, check from permissionCheck (which comes from middleware)
      else if (permissionCheck && permissionCheck.permissions) {
        const menuPermission = permissionCheck.permissions.find(
          (p: any) => p.menu?.path === '/dashboard/products/categories'
        );

        if (!menuPermission?.canView) {
          return NextResponse.json(
            { success: false, error: "You do not have permission to view categories" },
            { status: 403 }
          );
        }
      }
      // If neither is available, deny access
      else {
        return NextResponse.json(
          { success: false, error: "You do not have permission to view categories" },
          { status: 403 }
        );
      }
    }

    // Parse query parameters
    const url = new URL(req.url);
    const searchQuery = url.searchParams.get("search");

    // Build the query conditions
    let whereCondition;

    // Always filter by tenant ID
    if (searchQuery && searchQuery.trim() !== "") {
      // If search query is provided, filter by name using LIKE
      const searchTerm = `%${searchQuery.trim()}%`;
      whereCondition = and(
        eq(productCategories.tenantId, tenantId),
        like(productCategories.name, searchTerm)
      );
    } else {
      // If no search query, just filter by tenant ID
      whereCondition = eq(productCategories.tenantId, tenantId);
    }

    // Fetch categories from the database and include product count
    const categoriesData = await db.query.productCategories.findMany({
      where: whereCondition,
      with: {
        products: true, // Include related products
      },
      orderBy: [desc(productCategories.createdAt)], // Order by creation date (newest first)
    });

    const categoriesWithProductCount = categoriesData.map((category) => ({
      id: category.id,
      name: category.name,
      description: category.description,
      isActive: category.isActive,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
      productCount: category.products.length,
    }));

    return NextResponse.json({
      success: true,
      categories: categoriesWithProductCount,
    });
  } catch (error) {
    console.error("Error fetching product categories:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch product categories data" },
      { status: 500 }
    );
  }
}

// POST /api/products/categories - Create a new product category
async function postHandler(req: NextRequest, context: any, permissionCheck: any) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get tenant ID directly from session
    let tenantId = session.user.id;

    // For tenant users, the tenantId is their own id
    // For tenant_sale users, use the tenantId from session
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      tenantId = session.user.tenantId;
    }

    console.log('Using tenantId:', tenantId, 'for user role:', session.user.role);

    // Check if user has create permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      // First check if permissions are in the session
      if (session.user.menuPermissions && session.user.menuPermissions.length > 0) {
        console.log('Using menu permissions from session for categories API (POST)');
        const menuPermission = session.user.menuPermissions.find(
          (p: any) => p.path === '/dashboard/products/categories'
        );

        if (!menuPermission?.canCreate) {
          return NextResponse.json(
            { success: false, error: "You do not have permission to create categories" },
            { status: 403 }
          );
        }
      }
      // If not in session, check from permissionCheck (which comes from middleware)
      else if (permissionCheck && permissionCheck.permissions) {
        const menuPermission = permissionCheck.permissions.find(
          (p: any) => p.menu?.path === '/dashboard/products/categories'
        );

        if (!menuPermission?.canCreate) {
          return NextResponse.json(
            { success: false, error: "You do not have permission to create categories" },
            { status: 403 }
          );
        }
      }
      // If neither is available, deny access
      else {
        return NextResponse.json(
          { success: false, error: "You do not have permission to create categories" },
          { status: 403 }
        );
      }
    }

    // Parse and validate the request body
    const body = await req.json();
    console.log('Received category data:', body);

    const { name, description, isActive } = body;

    if (!name) {
      return NextResponse.json(
        { success: false, error: "Category name is required" },
        { status: 400 }
      );
    }

    // Check if category with the same name already exists for this tenant
    const existingCategory = await db.query.productCategories.findFirst({
      where: and(
        eq(productCategories.tenantId, tenantId),
        eq(productCategories.name, name)
      ),
    });

    if (existingCategory) {
      return NextResponse.json(
        { success: false, error: "A category with this name already exists" },
        { status: 400 }
      );
    }

    // Generate unique category ID
    const categoryId = uuidv4();

    // Create new category
    const newCategory = {
      id: categoryId,
      tenantId: tenantId,
      name,
      description,
      isActive: isActive ?? true,
    };

    await db.insert(productCategories).values(newCategory);

    return NextResponse.json({
      success: true,
      category: newCategory,
    });
  } catch (error) {
    console.error("Error creating product category:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create product category" },
      { status: 500 }
    );
  }
}

// PUT /api/products/categories - Update an existing product category
async function putHandler(req: NextRequest, context: any, permissionCheck: any) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get tenant ID directly from session
    let tenantId = session.user.id;

    // For tenant users, the tenantId is their own id
    // For tenant_sale users, use the tenantId from session
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      tenantId = session.user.tenantId;
    }

    console.log('Using tenantId:', tenantId, 'for user role:', session.user.role);

    // Check if user has edit permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      // First check if permissions are in the session
      if (session.user.menuPermissions && session.user.menuPermissions.length > 0) {
        console.log('Using menu permissions from session for categories API (PUT)');
        const menuPermission = session.user.menuPermissions.find(
          (p: any) => p.path === '/dashboard/products/categories'
        );

        if (!menuPermission?.canEdit) {
          return NextResponse.json(
            { success: false, error: "You do not have permission to edit categories" },
            { status: 403 }
          );
        }
      }
      // If not in session, check from permissionCheck (which comes from middleware)
      else if (permissionCheck && permissionCheck.permissions) {
        const menuPermission = permissionCheck.permissions.find(
          (p: any) => p.menu?.path === '/dashboard/products/categories'
        );

        if (!menuPermission?.canEdit) {
          return NextResponse.json(
            { success: false, error: "You do not have permission to edit categories" },
            { status: 403 }
          );
        }
      }
      // If neither is available, deny access
      else {
        return NextResponse.json(
          { success: false, error: "You do not have permission to edit categories" },
          { status: 403 }
        );
      }
    }

    // Parse request body
    const body = await req.json();
    const { id, name, description, isActive } = body;

    // Validate required fields
    if (!id || !name) {
      return NextResponse.json(
        {
          success: false,
          error: "Missing required fields: id and name are required",
        },
        { status: 400 }
      );
    }

    // Check if category exists and belongs to the specified tenant
    const existingCategory = await db.query.productCategories.findFirst({
      where: and(
        eq(productCategories.id, id),
        eq(productCategories.tenantId, tenantId)
      ),
    });

    if (!existingCategory) {
      return NextResponse.json(
        {
          success: false,
          error: "Category not found or does not belong to this tenant",
        },
        { status: 404 }
      );
    }

    // Check if another category with the same name exists for this tenant
    const duplicateCategory = await db.query.productCategories.findFirst({
      where: and(
        eq(productCategories.tenantId, tenantId),
        eq(productCategories.name, name),
        eq(productCategories.id, id)
      ),
    });

    if (duplicateCategory && duplicateCategory.id !== id) {
      return NextResponse.json(
        {
          success: false,
          error: "Another category with this name already exists",
        },
        { status: 400 }
      );
    }

    // Update the category
    await db
      .update(productCategories)
      .set({
        name,
        description,
        isActive: isActive ?? existingCategory.isActive,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(productCategories.id, id),
          eq(productCategories.tenantId, tenantId)
        )
      );

    return NextResponse.json({
      success: true,
      message: "Category updated successfully",
    });
  } catch (error) {
    console.error("Error updating product category:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update product category" },
      { status: 500 }
    );
  }
}

// DELETE /api/products/categories - Delete a product category
async function deleteHandler(req: NextRequest, context: any, permissionCheck: any) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get tenant ID directly from session
    let tenantId = session.user.id;

    // For tenant users, the tenantId is their own id
    // For tenant_sale users, use the tenantId from session
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      tenantId = session.user.tenantId;
    }

    console.log('Using tenantId:', tenantId, 'for user role:', session.user.role);

    // Check if user has delete permission for tenant_sale users
    if (session.user.role === 'tenant_sale') {
      // First check if permissions are in the session
      if (session.user.menuPermissions && session.user.menuPermissions.length > 0) {
        console.log('Using menu permissions from session for categories API (DELETE)');
        const menuPermission = session.user.menuPermissions.find(
          (p: any) => p.path === '/dashboard/products/categories'
        );

        if (!menuPermission?.canDelete) {
          return NextResponse.json(
            { success: false, error: "You do not have permission to delete categories" },
            { status: 403 }
          );
        }
      }
      // If not in session, check from permissionCheck (which comes from middleware)
      else if (permissionCheck && permissionCheck.permissions) {
        const menuPermission = permissionCheck.permissions.find(
          (p: any) => p.menu?.path === '/dashboard/products/categories'
        );

        if (!menuPermission?.canDelete) {
          return NextResponse.json(
            { success: false, error: "You do not have permission to delete categories" },
            { status: 403 }
          );
        }
      }
      // If neither is available, deny access
      else {
        return NextResponse.json(
          { success: false, error: "You do not have permission to delete categories" },
          { status: 403 }
        );
      }
    }

    // Parse the URL to get the category ID
    const url = new URL(req.url);
    const id = url.searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { success: false, error: "Category ID is required" },
        { status: 400 }
      );
    }

    // Check if category exists and belongs to the specified tenant
    const existingCategory = await db.query.productCategories.findFirst({
      where: and(
        eq(productCategories.id, id),
        eq(productCategories.tenantId, tenantId)
      ),
      with: {
        products: true,
      },
    });

    if (!existingCategory) {
      return NextResponse.json(
        {
          success: false,
          error: "Category not found or does not belong to this tenant",
        },
        { status: 404 }
      );
    }

    // Check if this category has associated products
    if (existingCategory.products && existingCategory.products.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error:
            "Cannot delete this category because it has associated products",
        },
        { status: 400 }
      );
    }

    // Delete the category
    await db
      .delete(productCategories)
      .where(
        and(
          eq(productCategories.id, id),
          eq(productCategories.tenantId, tenantId)
        )
      );

    return NextResponse.json({
      success: true,
      message: "Category deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting product category:", error);
    return NextResponse.json(
      { success: false, error: "Failed to delete product category" },
      { status: 500 }
    );
  }
}

// Export the route handlers with permission middleware
export const GET = withMenuPermission(getHandler, '/dashboard/products/categories');
export const POST = withMenuPermission(postHandler, '/dashboard/products/categories');
export const PUT = withMenuPermission(putHandler, '/dashboard/products/categories');
export const DELETE = withMenuPermission(deleteHandler, '/dashboard/products/categories');