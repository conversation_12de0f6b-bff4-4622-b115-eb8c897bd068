import type { NextConfig } from "next";

// Set the timezone to Asia/Dhaka
process.env.TZ = 'Asia/Dhaka';

const nextConfig: NextConfig = {
  /* config options here */
  env: {
    TZ: 'Asia/Dhaka',
  },
  // Enable image optimization
  images: {
    formats: ['image/avif', 'image/webp'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
  // Optimize performance
  poweredByHeader: false,
  reactStrictMode: true,
  // Optimize page loading
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn'],
    } : false,
  },
};

export default nextConfig;
