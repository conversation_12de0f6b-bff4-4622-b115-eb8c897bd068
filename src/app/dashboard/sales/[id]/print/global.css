/* Reset styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html.print-page {
  display: block !important;
  width: 100% !important;
}

html.print-page body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
    "Helvetica Neue", <PERSON><PERSON>, sans-serif;
  background-color: white !important;
  color: #333;
  line-height: 1.5;
  display: block !important;
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Hide any potential dashboard components */
.flex.h-screen.bg-gray-100,
div[class*="w-64"],
header,
nav,
.sidebar,
aside {
  display: none !important;
}

.print-container {
  display: block !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  background: white !important;
  z-index: 9999 !important;
}

/* Bengali font support */
@font-face {
  font-family: 'Kalpurush';
  src: url('https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@400;700&display=swap');
  font-weight: normal;
  font-style: normal;
}

/* Table styles */
table {
  border-collapse: collapse;
  width: 100%;
}

table, th, td {
  border: 1px solid #000;
}

th, td {
  padding: 4px;
}

/* Print-specific styles */
@media print {
  @page {
    size: A4;
    margin: 5mm;
  }

  body {
    padding: 0 !important;
    margin: 0 !important;
    background: white !important;
    font-size: 12pt;
  }

  .print\:hidden {
    display: none !important;
  }

  * {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    color-adjust: exact;
  }

  /* Ensure the invoice fits on one page */
  .print-container {
    page-break-inside: avoid;
  }
}
