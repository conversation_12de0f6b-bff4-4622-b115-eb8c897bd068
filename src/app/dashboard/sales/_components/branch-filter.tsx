"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";

interface Branch {
    id: string;
    name: string;
    isMain?: boolean | null;
    code?: string;
    isActive?: boolean | null;
}

interface BranchFilterProps {
    branches: Branch[];
    selectedBranchId: string | null | undefined;
}

export function BranchFilter({ branches, selectedBranchId }: BranchFilterProps) {
    const router = useRouter();
    const [branchId, setBranchId] = useState(selectedBranchId || "");

    // Update local state when prop changes
    useEffect(() => {
        setBranchId(selectedBranchId || "");
    }, [selectedBranchId]);

    const handleBranchChange = (value: string) => {
        setBranchId(value);

        // Get current URL search params
        const url = new URL(window.location.href);
        const searchParams = new URLSearchParams(url.search);

        // Update or remove branch parameter
        if (value) {
            searchParams.set("branch", value);
        } else {
            searchParams.delete("branch");
        }

        // Preserve search parameter if it exists
        const searchTerm = searchParams.get("search");
        if (!searchTerm) {
            searchParams.delete("search");
        }

        // Navigate to the new URL
        router.push(`/dashboard/sales?${searchParams.toString()}`);
    };

    const handleClear = () => {
        setBranchId("");

        // Get current URL search params
        const url = new URL(window.location.href);
        const searchParams = new URLSearchParams(url.search);

        // Remove branch parameter
        searchParams.delete("branch");

        // Preserve search parameter if it exists
        const searchTerm = searchParams.get("search");
        if (!searchTerm) {
            searchParams.delete("search");
        }

        // Navigate to the new URL
        router.push(`/dashboard/sales?${searchParams.toString()}`);
    };

    return (
        <div className="relative w-full">
            <Select value={branchId} onValueChange={handleBranchChange}>
                <SelectTrigger className="w-full cursor-pointer h-9">
                    <SelectValue placeholder="Filter by branch" />
                </SelectTrigger>
                <SelectContent>
                    {branches.map((branch) => (
                        <SelectItem key={branch.id} value={branch.id} className="cursor-pointer">
                            {branch.code ? `[${branch.code}] ` : ''}{branch.name} {branch.isMain ? "(Main)" : ""}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>

            {branchId && (
                <Button
                    variant="ghost"
                    onClick={handleClear}
                    className="absolute right-0 top-0 h-9 px-2"
                >
                    ✕
                </Button>
            )}
        </div>
    );
}
