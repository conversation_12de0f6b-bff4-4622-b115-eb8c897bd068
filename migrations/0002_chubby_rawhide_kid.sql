PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_user_menu_permissions` (
	`id` text PRIMARY KEY NOT NULL,
	`user_id` text NOT NULL,
	`menu_id` text NOT NULL,
	`tenant_id` text NOT NULL,
	`can_view` integer DEFAULT true,
	`can_create` integer DEFAULT false,
	`can_edit` integer DEFAULT false,
	`can_delete` integer DEFAULT false,
	`created_at` integer,
	`updated_at` integer,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`tenant_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
INSERT INTO `__new_user_menu_permissions`("id", "user_id", "menu_id", "tenant_id", "can_view", "can_create", "can_edit", "can_delete", "created_at", "updated_at") SELECT "id", "user_id", "menu_id", "tenant_id", "can_view", "can_create", "can_edit", "can_delete", "created_at", "updated_at" FROM `user_menu_permissions`;--> statement-breakpoint
DROP TABLE `user_menu_permissions`;--> statement-breakpoint
ALTER TABLE `__new_user_menu_permissions` RENAME TO `user_menu_permissions`;--> statement-breakpoint
PRAGMA foreign_keys=ON;