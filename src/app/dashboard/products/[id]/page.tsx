'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Loader2, ArrowLeft, Save } from "lucide-react";
import { use } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useSession } from 'next-auth/react';

const productSchema = z.object({
    name: z.string().min(1, { message: 'Product name is required' }),
    code: z.string().optional().nullable(),
    description: z.string().optional().nullable(),
    costPrice: z.coerce.number().min(0, { message: 'Cost price must be a positive number' }),
    sellingPrice: z.coerce.number().min(1, { message: 'MRP Price required and must be a positive number' }),
    discount: z.coerce.number().min(0).max(100).default(0),
    categoryId: z.string().optional().nullable(),
    vendorId: z.string().optional().nullable(),
    unit: z.string().min(1, { message: 'Unit is required' }),
    royaltyType: z.enum(['none', 'fixed', 'percentage']).default('none'),
    royaltyValue: z.coerce.number().min(0).default(0),
    isActive: z.boolean().default(true),
    sku: z.string().optional().nullable(),
    weight: z.coerce.number().min(0).optional().nullable(),
});

type ProductFormValues = z.infer<typeof productSchema>;

export default function EditProductPage({ params }: { params: any }) {
    const router = useRouter();
    const { data: session } = useSession();
    const resolvedParams = use(params) as { id: string };
    const productId = resolvedParams.id;
    const [categories, setCategories] = useState<{ id: string; name: string; isActive?: boolean }[]>([]);
    const [vendors, setVendors] = useState<{ id: string; name: string; isActive?: boolean; vendorCode?: string; userId?: string }[]>([]);
    const [loading, setLoading] = useState(true);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);
    const [userPermissions, setUserPermissions] = useState<{
        canView: boolean;
        canCreate: boolean;
        canEdit: boolean;
        canDelete: boolean;
    }>({ canView: false, canCreate: false, canEdit: false, canDelete: false });

    const {
        register,
        handleSubmit,
        formState: { errors },
        reset,
        setValue,
        watch,
    } = useForm<ProductFormValues>({
        resolver: zodResolver(productSchema),
        defaultValues: {
            name: '',
            code: '',
            description: '',
            costPrice: 0,
            sellingPrice: 0,
            discount: 0,
            categoryId: '',
            vendorId: '',
            unit: '',
            royaltyType: 'none',
            royaltyValue: 0,
            isActive: true,

        },
    });

    const royaltyType = watch('royaltyType');

    useEffect(() => {
        fetchProduct();
        fetchCategories();
        fetchVendors();
    }, [productId]);

    // Check user permissions when session is loaded
    useEffect(() => {
        if (session) {
            fetchUserPermissions();
        }
    }, [session]);

    // Fetch user permissions
    const fetchUserPermissions = async () => {
        try {
            if (!session) {
                return; // Wait for session to be loaded
            }

            if (session?.user?.role === 'admin' || session?.user?.role === 'tenant') {
                // Admin and tenant users have full permissions
                setUserPermissions({
                    canView: true,
                    canCreate: true,
                    canEdit: true,
                    canDelete: true
                });
                return;
            }

            // For tenant_sale users, fetch their specific permissions from the API
            const response = await fetch(`/api/user/permissions?menuPath=/dashboard/products`);
            const data = await response.json();

            if (data.success) {
                setUserPermissions(data.permissions);

                // If user doesn't have edit permission, redirect to dashboard
                if (!data.permissions.canEdit) {
                    router.push('/dashboard');
                }
            } else {
                console.error('Error fetching permissions:', data.error);
                router.push('/dashboard');
            }
        } catch (error) {
            console.error('Error fetching user permissions:', error);
            router.push('/dashboard');
        }
    };

    const fetchProduct = async () => {
        try {
            setLoading(true);
            const response = await fetch(`/api/products?id=${productId}`);

            if (!response.ok) {
                if (response.status === 404) {
                    throw new Error('Product not found');
                }
                throw new Error('Failed to fetch product');
            }

            const data = await response.json();

            if (data.success && data.product) {
                const product = data.product;

                // Update form values
                reset({
                    name: product.name,
                    code: product.code,
                    description: product.description,
                    costPrice: product.costPrice,
                    sellingPrice: product.sellingPrice,
                    discount: product.discount,
                    categoryId: product.categoryId,
                    vendorId: product.vendorId,
                    unit: product.unit,
                    royaltyType: product.royaltyType,
                    royaltyValue: product.royaltyValue,
                    isActive: product.isActive,
                });
            } else {
                setError(data.error || 'Failed to load product');
            }
        } catch (err: any) {
            setError(err.message || 'An error occurred while fetching product');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    const fetchCategories = async () => {
        try {
            const response = await fetch('/api/products/categories');

            if (!response.ok) {
                throw new Error('Failed to fetch categories');
            }

            const data = await response.json();

            if (data.success) {
                setCategories(data.categories);
            } else {
                setError(data.error || 'Failed to load categories');
            }
        } catch (err: any) {
            setError('An error occurred while fetching categories');
            console.error(err);
        }
    };

    const fetchVendors = async () => {
        try {
            // Use the same API endpoint as the product add page
            // This endpoint already filters for active vendors
            const response = await fetch('/api/products/vendors');

            if (!response.ok) {
                throw new Error('Failed to fetch vendors');
            }

            const data = await response.json();

            if (data.success) {
                setVendors(data.vendors);
            } else {
                setError(data.error || 'Failed to load vendors');
            }
        } catch (err: any) {
            setError('An error occurred while fetching vendors');
            console.error(err);
        }
    };

    const onSubmit = async (data: ProductFormValues) => {
        try {
            setSubmitting(true);
            setError(null);
            setSuccess(null);

            const response = await fetch(`/api/products?id=${productId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Failed to update product');
            }

            if (result.success) {
                setSuccess('Product updated successfully');
                setTimeout(() => {
                    router.push('/dashboard/products');
                    router.refresh();
                }, 1500);
            } else {
                setError(result.error || 'Failed to update product');
            }
        } catch (err: any) {
            setError(err.message || 'An error occurred while updating product');
            console.error(err);
        } finally {
            setSubmitting(false);
        }
    };

    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

    const handleDeleteClick = () => {
        setIsDeleteDialogOpen(true);
    };

    const handleDeleteProduct = async () => {
        try {
            setSubmitting(true);
            setError(null);

            const response = await fetch(`/api/products?id=${productId}`, {
                method: 'DELETE',
            });

            const data = await response.json();

            if (data.success) {
                setSuccess('Product deleted successfully');
                setTimeout(() => {
                    router.push('/dashboard/products');
                    router.refresh();
                }, 1500);
            } else {
                setError(data.error || 'Failed to delete product');
            }
        } catch (err: any) {
            setError('An error occurred while deleting the product');
            console.error(err);
        } finally {
            setSubmitting(false);
            setIsDeleteDialogOpen(false);
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-[40vh]">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
            </div>
        );
    }

    return (
        <div className="container mx-auto px-2 py-2">
            <div className="flex justify-between items-center mb-2">
                <h1 className="text-lg font-bold">Edit Product</h1>
                <Button
                    variant="outline"
                    onClick={() => router.push('/dashboard/products')}
                    className='cursor-pointer h-8 text-xs'
                >
                    <ArrowLeft className="mr-1 h-3 w-3" />
                    Back
                </Button>
            </div>

            {error && (
                <Alert variant="destructive" className="mb-2 py-1 px-2 text-xs">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {success && (
                <Alert className="mb-2 py-1 px-2 text-xs bg-green-50 text-green-800 border-green-200">
                    <AlertDescription>{success}</AlertDescription>
                </Alert>
            )}

            <div className="bg-white rounded border border-gray-100 p-3">
                <div className="flex justify-between items-center mb-2">
                    <h2 className="text-sm font-medium">Product Information</h2>
                </div>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <div className="col-span-3">
                            <Label htmlFor="name" className="block text-xs font-medium mb-1">
                                Product Name *
                            </Label>
                            <Input
                                id="name"
                                {...register('name')}
                                placeholder="Enter product name"
                                className="h-8 text-sm px-2 py-1 border-gray-200"
                            />
                            {errors.name && (
                                <p className="mt-0.5 text-xs text-red-500">{errors.name.message}</p>
                            )}
                        </div>

                        <div>
                            <Label htmlFor="code" className="block text-xs font-medium mb-1">
                                Product Code
                            </Label>
                            <Input
                                id="code"
                                {...register('code')}
                                placeholder="Enter code (optional)"
                                className="h-8 text-sm px-2 py-1 border-gray-200"
                            />
                            {errors.code && (
                                <p className="mt-0.5 text-xs text-red-500">{errors.code.message}</p>
                            )}
                        </div>

                        <div>
                            <Label htmlFor="vendorId" className="block text-xs font-medium mb-1">
                                Vendor (Optional)
                            </Label>
                            <select
                                id="vendorId"
                                {...register('vendorId')}
                                className="w-full h-8 px-2 py-1 text-sm border border-gray-200 rounded focus:outline-none focus:border-blue-500 dark:bg-gray-700 dark:text-white cursor-pointer"
                            >
                                <option value="">Select vendor</option>
                                {vendors.map((vendor) => (
                                    vendor.isActive === true && (
                                        <option key={vendor.id} value={vendor.id}>
                                            {vendor.name}
                                        </option>
                                    )
                                ))}
                            </select>
                            {errors.vendorId && (
                                <p className="mt-0.5 text-xs text-red-500">{errors.vendorId.message}</p>
                            )}
                        </div>

                        <div>
                            <Label htmlFor="categoryId" className="block text-xs font-medium mb-1">
                                Category (Optional)
                            </Label>
                            <select
                                id="categoryId"
                                {...register('categoryId')}
                                className="w-full h-8 px-2 py-1 text-sm border border-gray-200 rounded focus:outline-none focus:border-blue-500 dark:bg-gray-700 dark:text-white cursor-pointer"
                            >
                                <option value="">Select category</option>
                                {categories.map((category) => (
                                    category.isActive === true && (
                                        <option key={category.id} value={category.id}>
                                            {category.name}
                                        </option>
                                    )
                                ))}
                            </select>
                            {errors.categoryId && (
                                <p className="mt-0.5 text-xs text-red-500">{errors.categoryId.message}</p>
                            )}
                        </div>

                        <div>
                            <Label htmlFor="unit" className="block text-xs font-medium mb-1">
                                Unit *
                            </Label>
                            <select
                                id="unit"
                                {...register('unit')}
                                className="w-full h-8 px-2 py-1 text-sm border border-gray-200 rounded focus:outline-none focus:border-blue-500 dark:bg-gray-700 dark:text-white cursor-pointer"
                            >
                                <option value="">Select Unit</option>
                                <option value="pcs">pcs</option>
                                <option value="m">m</option>
                                <option value="g">g</option>
                            </select>
                            {errors.unit && (
                                <p className="mt-0.5 text-xs text-red-500">{errors.unit.message}</p>
                            )}
                        </div>

                        <div>
                            <Label htmlFor="isActive" className="text-xs font-medium mb-1 flex items-center">
                                <Checkbox
                                    id="isActive"
                                    checked={watch('isActive')}
                                    onCheckedChange={(checked) => {
                                        setValue('isActive', checked === true);
                                    }}
                                    className="h-3 w-3 mr-1"
                                />
                                <span className="text-xs">Product is Active</span>
                            </Label>
                        </div>

                        <div>
                            <Label htmlFor="costPrice" className="block text-xs font-medium mb-1">
                                Cost Price *
                            </Label>
                            <Input
                                id="costPrice"
                                type="number"
                                step="0.01"
                                {...register('costPrice')}
                                className="h-8 text-sm px-2 py-1 border-gray-200"
                            />
                            {errors.costPrice && (
                                <p className="mt-0.5 text-xs text-red-500">{errors.costPrice.message}</p>
                            )}
                        </div>

                        <div>
                            <Label htmlFor="sellingPrice" className="block text-xs font-medium mb-1">
                                MRP *
                            </Label>
                            <Input
                                id="sellingPrice"
                                type="number"
                                step="0.01"
                                {...register('sellingPrice')}
                                className="h-8 text-sm px-2 py-1 border-gray-200"
                            />
                            {errors.sellingPrice && (
                                <p className="mt-0.5 text-xs text-red-500">{errors.sellingPrice.message}</p>
                            )}
                        </div>

                        <div>
                            <Label htmlFor="discount" className="block text-xs font-medium mb-1">
                                Discount %
                            </Label>
                            <Input
                                id="discount"
                                type="number"
                                min="0"
                                max="100"
                                {...register('discount')}
                                className="h-8 text-sm px-2 py-1 border-gray-200"
                            />
                            {errors.discount && (
                                <p className="mt-0.5 text-xs text-red-500">{errors.discount.message}</p>
                            )}
                        </div>

                        <div>
                            <Label className="block text-xs font-medium mb-1">Royalty type</Label>
                            <select
                                defaultValue={watch('royaltyType')}
                                id="royaltyType"
                                {...register('royaltyType')}
                                className="w-full h-8 px-2 py-1 text-sm border border-gray-200 rounded focus:outline-none focus:border-blue-500 dark:bg-gray-700 dark:text-white cursor-pointer"
                            >
                                <option value="none">None</option>
                                <option value="fixed">Fixed</option>
                                <option value="percentage">Percentage</option>
                            </select>
                        </div>

                        {royaltyType !== 'none' && (
                            <div>
                                <Label htmlFor="royaltyValue" className="block text-xs font-medium mb-1">
                                    {royaltyType === 'fixed' ? 'Royalty Amount' : 'Royalty %'}
                                </Label>
                                <Input
                                    id="royaltyValue"
                                    type="number"
                                    step={royaltyType === 'fixed' ? '0.01' : '0.1'}
                                    min="0"
                                    max={royaltyType === 'percentage' ? '100' : undefined}
                                    {...register('royaltyValue')}
                                    className="h-8 text-sm px-2 py-1 border-gray-200"
                                />
                                {errors.royaltyValue && (
                                    <p className="mt-0.5 text-xs text-red-500">{errors.royaltyValue.message}</p>
                                )}
                            </div>
                        )}

                        <div className="col-span-3">
                            <Label htmlFor="description" className="block text-xs font-medium mb-1">
                                Description
                            </Label>
                            <Textarea
                                id="description"
                                {...register('description')}
                                placeholder="Enter product description"
                                className="min-h-[80px] text-sm px-2 py-1 border-gray-200"
                            />
                            {errors.description && (
                                <p className="mt-0.5 text-xs text-red-500">{errors.description.message}</p>
                            )}
                        </div>
                    </div>

                    <div className="flex justify-between mt-3">
                        <Button
                            className='cursor-pointer h-8 text-xs'
                            type="button"
                            variant="destructive"
                            onClick={handleDeleteClick}
                            disabled={submitting}
                        >
                            Delete
                        </Button>
                        <Button
                            type="submit"
                            disabled={submitting}
                            className='cursor-pointer h-8 text-xs'
                        >
                            {submitting ? (
                                <>
                                    <Loader2 className="h-3 w-3 animate-spin mr-1" />
                                    Saving...
                                </>
                            ) : (
                                <>
                                    <Save className="h-3 w-3 mr-1" />
                                    Save
                                </>
                            )}
                        </Button>
                    </div>
                </form>
            </div>

            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent className="max-w-sm p-3">
                    <DialogHeader className="pb-2">
                        <DialogTitle className="text-sm">Delete Product</DialogTitle>
                        <DialogDescription className="text-xs mt-1">
                            Are you sure you want to delete this product? This will also remove all inventory data.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter className="pt-2 space-x-1">
                        <Button
                            variant="outline"
                            onClick={() => setIsDeleteDialogOpen(false)}
                            disabled={submitting}
                            className='cursor-pointer h-7 text-xs'
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={handleDeleteProduct}
                            disabled={submitting}
                            className='cursor-pointer h-7 text-xs'
                        >
                            {submitting ? 'Deleting...' : 'Delete'}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}

