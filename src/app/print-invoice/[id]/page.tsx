import { Metadata } from "next";
import { redirect } from "next/navigation";
import <PERSON> from "next/link";
import { auth } from "../../../../auth";
import { db } from "@/lib/db";
import { orders, tenants, users } from "@/db/schema";
import { formatDate, formatCurrency } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import AutoPrint from "./AutoPrint";
import PrintButton from "./PrintButton";
import PrintStyles from "./PrintStyles";
import BanglaNumberConverter from "@/components/BanglaNumberConverter";
import MemoPaidStamp from "@/components/MemoPaidStamp";
import DraftWatermark from "@/components/DraftWatermark";
import { eq } from "drizzle-orm";

export const metadata: Metadata = {
    title: "Print Invoice",
    description: "Print a sales invoice",
};

interface PrintPageProps {
    params: Promise<{ id: string }>;
}

export default async function PrintPage({ params }: PrintPageProps) {
    const session = await auth();
    if (!session?.user) {
        redirect("/api/auth/signin");
    }

    const { id } = await params;
    if (!id) {
        redirect("/dashboard/sales");
    }

    // Get the order with branch, customer, and items information
    const order = await db.query.orders.findFirst({
        where: (orders, { eq, and }) =>
            and(
                eq(orders.id, id),
                eq(orders.tenantId, session.user.tenantId || session.user.id)
            ),
        with: {
            branch: true,
            customer: true,
            items: {
                with: {
                    product: {
                        with: {
                            category: true,
                            vendor: true
                        }
                    }
                }
            },
            performer: {
                columns: {
                    fullName: true
                }
            }
        }
    });

    console.log(order, 'order-details')

    if (!order) {
        redirect("/dashboard/sales");
    }

    // Get tenant information for company details
    const tenantInfo = await db.query.tenants.findFirst({
        where: (tenants, { eq }) => eq(tenants.userId, session.user.tenantId || session.user.id)
    });

    // Fetch user and tenant data
    const userData = await db.query.users.findFirst({
        where: eq(users.id, session.user.id),
    });

    // console.log(userData, 'userdata-------');

    // If branch doesn't have address, use tenant address as fallback
    const branchAddress = order.branch?.address;
    const branchPhone = order.branch?.phone;
    const companyName = tenantInfo?.companyName || "Your Company Name";

    return (
        <div className="print-container bg-white min-h-screen">
            <div className="max-w-4xl mx-auto p-4">
                {/* Navigation buttons */}
                <div className="mb-8 print:hidden flex justify-between">
                    <Link href={`/dashboard/sales/${id}`}>
                        <Button variant="outline" className="flex items-center">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Details
                        </Button>
                    </Link>
                    <PrintButton />
                </div>

                {/* Invoice content */}
                <div className="p-4 rounded shadow print:shadow-none relative">
                    {/* Memo Paid Stamp - Only show when payment status is "paid" */}
                    {order.paymentStatus === "paid" && (
                        <MemoPaidStamp
                            className="absolute top-1/3 left-60"
                        />
                    )}

                    {/* Draft Watermark - Only show for draft orders */}
                    {order.status === "draft" && (
                        <DraftWatermark />
                    )}

                    {/* Header - Bengali Style */}
                    <div className="mb-4 text-center">
                        <div className="flex items-center justify-center mb-2">
                            {/* Logo on left */}
                            {/* <div className="w-24 h-24 relative">
                                <Image
                                    src={logoUrl}
                                    alt={companyName}
                                    width={80}
                                    height={80}
                                    style={{ objectFit: 'contain' }}
                                />
                            </div> */}

                            {/* Center header with Bengali text */}
                            <div className="text-center flex items-center justify-center flex-col gap-1">
                                <h1 className="text-2xl font-bold mb-1">{order?.branch?.name}</h1>
                                <p className="text-sm mt-1">{branchAddress}</p>
                                {branchPhone && (
                                    <p className="text-sm mt-[100px]">📞{branchPhone}</p>
                                )}
                                {(!branchPhone && !branchAddress) && (
                                    <div className="h-25"></div>
                                )}

                            </div>

                            {/* Invoice details on right */}
                            {/* <div className="text-left">
                                <p className="mb-1"><strong>মেমো নং:</strong> {order.memoNo}</p>
                                <p><strong>তারিখ:</strong> {formatDate(order.date)}</p>
                            </div> */}
                        </div>
                    </div>

                    {/* Customer Information */}
                    <div className="mb-4 border-b py-2">
                        <div className="flex items-start justify-between mb-2">
                            <div>
                                <p><strong>কাস্টমার নাম:</strong> {order.customer?.name || "ওয়াক-ইন কাস্টমার"}</p>
                                <p><strong>ঠিকানা:</strong> {order.customer?.address || "N/A"}</p>
                                <p><strong>ফোন নম্বর:</strong> {order.customer?.phone || "N/A"}</p>
                            </div>
                            <div>
                                <p className="mb-1"><strong>মেমো নং:</strong> {order.memoNo}</p>
                                <p><strong>তারিখ:</strong> {formatDate(order.date)}</p>
                                <p><strong>বিক্রেতা:</strong> {order.performer?.fullName || "N/A"}</p>
                            </div>
                        </div>
                    </div>

                    {/* Items Table - Bengali Style */}
                    <div className="mb-4">
                        <table className="w-full border-collapse">
                            <thead>
                                <tr className="">
                                    <th className="py-2 px-2 text-center border border-gray-500">নং</th>
                                    <th className="py-2 px-2 text-center border border-gray-500">বিবরণ</th>
                                    <th className="py-2 px-2 text-center border border-gray-500">সংখ্যা</th>
                                    <th className="py-2 px-2 text-center border border-gray-500">বিক্রয় মূল্য</th>
                                    <th className="py-2 px-2 text-center border border-gray-500">ডিসকাউন্ট</th>
                                    <th className="py-2 px-2 text-center border border-gray-500">মোট টাকা</th>
                                </tr>
                            </thead>
                            <tbody>
                                {order.items.map((item, index) => (
                                    <tr key={item.id} className="">
                                        <td className="py-1 px-2 text-center border border-gray-500">{index + 1}</td>
                                        <td className="py-1 px-2 text-left border border-gray-500">
                                            <div className="flex flex-col">
                                                <span>{item.product.name}</span>
                                                {item.product.category?.name && (
                                                    <span className="text-xs text-gray-600">
                                                        {item.product.category.name}
                                                    </span>
                                                )}
                                            </div>
                                        </td>
                                        <td className="py-1 px-2 text-center border border-gray-500">{item.quantity}</td>
                                        <td className="py-1 px-2 text-center border border-gray-500">{formatCurrency(item.unitPrice)}</td>
                                        <td className="py-1 px-2 text-center border border-gray-500">{item.discountPercentage}%</td>
                                        <td className="py-1 px-2 text-center border border-gray-500">{formatCurrency(item.total)}</td>
                                    </tr>
                                ))}

                                {/* Empty rows to match the photo layout */}
                                {Array.from({ length: Math.max(0, 10 - order.items.length) }).map((_, index) => (
                                    <tr key={`empty-${index}`} className="">
                                        <td className="py-1 px-2 border border-gray-500">&nbsp;</td>
                                        <td className="py-1 px-2 border border-gray-500">&nbsp;</td>
                                        <td className="py-1 px-2 border border-gray-500">&nbsp;</td>
                                        <td className="py-1 px-2 border border-gray-500">&nbsp;</td>
                                        <td className="py-1 px-2 border border-gray-500">&nbsp;</td>
                                        <td className="py-1 px-2 border border-gray-500">&nbsp;</td>
                                    </tr>
                                ))}
                            </tbody>
                            <tbody>
                                <tr className="">
                                    <td colSpan={2} className="py-1 px-2 text-right border border-gray-500 font-bold">সর্বমোট</td>
                                    <td className="py-1 px-2 text-center border border-gray-500 font-bold">{order.items.reduce((sum, item) => sum + item.quantity, 0)}</td>
                                    <td className="py-1 px-2 text-center border border-gray-500"></td>
                                    <td className="py-1 px-2 text-center border border-gray-500"></td>
                                    <td className="py-1 px-2 text-center border border-gray-500 font-bold">{formatCurrency(order.subTotal || 0)}</td>
                                </tr>

                            </tbody>
                        </table>


                        <div className="w-full flex justify-end">
                            <div className="w-[340px] ">
                                <table >
                                    <tbody className="w-[350px]">
                                        {/* <tr className="" >
                                            <td className="py-1 px-2 text-right border border-gray-500">ডিসকাউন্ট {order.discountPercentage || 0}%</td>
                                            <td className="py-1 px-2 text-right border border-gray-500">{formatCurrency(order.discountAmount || 0)}</td>
                                        </tr> */}
                                        {(order.extCommission || 0) > 0 && (
                                            <tr className="" >
                                                <td className="py-1 px-2 text-right border border-gray-500">
                                                    কমিশন {order.extCommissionType === "percentage" ? `${order.extCommission || 0}%` : "ফিক্সড"}
                                                </td>
                                                <td className="py-1 px-2 text-right border border-gray-500">
                                                    {formatCurrency(order.extCommissionType === "percentage"
                                                        ? ((order.subTotal || 0) * (order.extCommission || 0) / 100)
                                                        : (order.extCommission || 0))}
                                                </td>
                                            </tr>
                                        )}

                                        <tr className="">
                                            <td className="py-1 px-2 text-right border border-gray-500 font-bold">মোট মূল্য</td>
                                            <td className="py-1 px-2 text-right border border-gray-500 font-bold">{formatCurrency(order.totalAmount || 0)}</td>
                                        </tr>
                                        <tr className="">
                                            <td className="py-1 px-2 text-right border border-gray-500">পূর্ব বকেয়া/জমা</td>
                                            <td className="py-1 px-2 text-right border border-gray-500">{formatCurrency(order.previousDue || 0)}</td>
                                        </tr>
                                        <tr className="">
                                            <td className="py-1 px-2 text-right border border-gray-500 font-bold">সর্বমোট</td>
                                            <td className="py-1 px-2 text-right border border-gray-500 font-bold">{formatCurrency((order.totalAmount || 0) + (order.previousDue || 0))}</td>
                                        </tr>
                                        <tr className="">
                                            <td className="py-1 px-2 text-right border border-gray-500">পরিশোধিত অর্থ</td>
                                            <td className="py-1 px-2 text-right border border-gray-500">{formatCurrency(order.paidAmount || 0)}</td>
                                        </tr>
                                        <tr className="">
                                            <td className="py-1 px-2 text-right border border-gray-500 font-bold">বর্তমান ব্যালেন্স</td>
                                            <td className="py-1 px-2 text-right border border-gray-500 font-bold">{formatCurrency(order.dueAmount || 0)}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                        </div>
                    </div>

                    {/* Amount in words */}
                    <div className="mb-4 text-sm">
                        <p><strong>কথায়:</strong> <BanglaNumberConverter numb={(order.totalAmount || 0)} showCurrency={true} /></p>
                    </div>

                    {/* Signature Section */}
                    <div className="mt-8 flex justify-between h-20 items-end">
                        <div className="text-center">
                            <div className="border-t border-black w-32">
                                <p className="text-sm">ক্রেতার স্বাক্ষর</p>
                            </div>
                        </div>
                        <div className="text-center">
                            <div className="border-t border-black pt-1 w-32">
                                <p className="text-sm">বিক্রেতার স্বাক্ষর</p>
                                <p className="text-xs mt-1">{order.performer?.fullName || ""}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <PrintStyles />
            <AutoPrint />
        </div>
    );
}