'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

interface Product {
    id: string;
    name: string;
    code: string | null;
    costPrice: number;
}

interface Branch {
    id: string;
    name: string;
    code: string;
}

const formSchema = z.object({
    branchId: z.string().min(1, { message: 'Branch is required' }),
    productId: z.string().min(1, { message: 'Product is required' }),
    quantity: z.coerce.number().int().min(1, { message: 'Quantity must be a positive number' }),
    unitPrice: z.coerce.number().min(0, { message: 'Unit price must be a positive number' }),
    transactionType: z.enum(['in', 'out', 'adjustment'], {
        required_error: "Transaction type is required"
    }),
    location: z.enum(['warehouse', 'store'], {
        required_error: "Location is required"
    }),
    adjustmentType: z.enum(['increase', 'decrease'], {
        required_error: "Adjustment type is required"
    }),
    reason: z.string().min(1, { message: 'Reason is required' }),
    notes: z.string().optional().nullable(),
});

type FormValues = z.infer<typeof formSchema>;

export default function StockAdjustmentPage() {
    const router = useRouter();
    const [products, setProducts] = useState<Product[]>([]);
    const [branches, setBranches] = useState<Branch[]>([]);

    const [loading, setLoading] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
    const [success, setSuccess] = useState<string | null>(null);

    const {
        register,
        handleSubmit,
        formState: { errors },
        reset,
        watch,
        setValue
    } = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            branchId: '',
            productId: '',
            quantity: 1,
            unitPrice: 0,
            transactionType: 'in',
            location: 'warehouse',
            adjustmentType: 'increase',
            reason: '',
            notes: null,
        },
    });

    // Watch for changes in productId to update unit price
    const watchProductId = watch('productId');
    const watchTransactionType = watch('transactionType');

    useEffect(() => {
        fetchProducts();
    }, []);

    useEffect(() => {
        if (watchProductId && products.length > 0) {
            const product = products.find(p => p.id === watchProductId);
            if (product) {
                setSelectedProduct(product);
                setValue('unitPrice', product.costPrice);
            }
        }
    }, [watchProductId, products, setValue]);

    const fetchProducts = async () => {
        try {
            setLoading(true);
            const response = await fetch('/api/products');

            if (!response.ok) {
                throw new Error('Failed to fetch products');
            }

            const data = await response.json();

            if (data.success) {
                setProducts(data.products);
            } else {
                setError(data.error || 'Failed to load products');
            }
        } catch (err) {
            setError('An error occurred while fetching products');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    const fetchBranches = async () => {
        try {
            setLoading(true);
            const response = await fetch('/api/branches');

            if (!response.ok) {
                throw new Error('Failed to fetch branches');
            }

            const data = await response.json();

            if (data.success) {
                setBranches(data.branches);
            } else {
                setError(data.error || 'Failed to load branches');
            }
        } catch (err) {
            setError('An error occurred while fetching branches');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    const onSubmit = async (data: FormValues) => {
        try {
            setSubmitting(true);
            setError(null);

            const inventoryData = {
                branchId: data.branchId,
                productId: data.productId,
                quantity: data.quantity,
                warehouseQuantity: data.location === 'warehouse' ? data.quantity : 0,
                storeQuantity: data.location === 'store' ? data.quantity : 0,
                unitPrice: selectedProduct?.costPrice || 0,
                transactionType: data.adjustmentType === 'increase' ? 'in' : 'out',
                notes: `Adjustment: ${data.reason}`,
            };

            console.log('Submitting inventory adjustment data:', inventoryData);

            const response = await fetch('/api/inventory', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(inventoryData),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to process adjustment');
            }

            const result = await response.json();
            setSuccess(`Inventory ${data.adjustmentType === 'increase' ? 'increased' : 'decreased'} successfully`);

            // Refresh inventory data
            fetchInventory();

            // Reset form
            reset();
        } catch (err: any) {
            setError(err.message || 'An error occurred during adjustment');
            console.error('Error adjusting inventory:', err);
        } finally {
            setSubmitting(false);
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold">Adjust Inventory</h1>
                <div className="flex space-x-2">
                    <button
                        onClick={() => router.push('/dashboard/inventory/transactions')}
                        className="bg-purple-500 hover:bg-purple-600 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 cursor-pointer"
                    >
                        Transaction History
                    </button>
                    <button
                        onClick={() => router.back()}
                        className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 cursor-pointer"
                    >
                        Back to Inventory
                    </button>
                </div>
            </div>

            {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">Error: </strong>
                    <span className="block sm:inline">{error}</span>
                </div>
            )}

            {success && (
                <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">Success: </strong>
                    <span className="block sm:inline">{success}</span>
                </div>
            )}

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <form onSubmit={handleSubmit(onSubmit)}>
                    <div className="mb-6">
                        <label htmlFor="transactionType" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Transaction Type *
                        </label>
                        <div className="flex space-x-4">
                            <label className="inline-flex items-center">
                                <input
                                    type="radio"
                                    {...register('transactionType')}
                                    value="in"
                                    className="form-radio h-5 w-5 text-blue-600"
                                />
                                <span className="ml-2 text-gray-700 dark:text-gray-300">Stock In</span>
                            </label>
                            <label className="inline-flex items-center">
                                <input
                                    type="radio"
                                    {...register('transactionType')}
                                    value="out"
                                    className="form-radio h-5 w-5 text-red-600"
                                />
                                <span className="ml-2 text-gray-700 dark:text-gray-300">Stock Out</span>
                            </label>
                            <label className="inline-flex items-center">
                                <input
                                    type="radio"
                                    {...register('transactionType')}
                                    value="adjustment"
                                    className="form-radio h-5 w-5 text-amber-600"
                                />
                                <span className="ml-2 text-gray-700 dark:text-gray-300">Set Exact Quantity</span>
                            </label>
                        </div>
                        {errors.transactionType && (
                            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.transactionType.message}</p>
                        )}
                    </div>

                    <div className="mb-6">
                        <label htmlFor="productId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Product *
                        </label>
                        <select
                            id="productId"
                            {...register('productId')}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        >
                            <option value="">Select a product</option>
                            {products.map((product) => (
                                <option key={product.id} value={product.id}>
                                    {product.name} {product.code ? `(${product.code})` : ''}
                                </option>
                            ))}
                        </select>
                        {errors.productId && (
                            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.productId.message}</p>
                        )}

                        {selectedProduct && (
                            <div className="mt-2 text-sm text-blue-600 dark:text-blue-400">
                                Current price: ${selectedProduct.costPrice.toFixed(2)}
                            </div>
                        )}
                    </div>

                    <div className="mb-6">
                        <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {watchTransactionType === 'adjustment' ? 'Set Quantity To *' : 'Quantity *'}
                        </label>
                        <input
                            type="number"
                            id="quantity"
                            {...register('quantity')}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                            min="0"
                        />
                        {errors.quantity && (
                            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.quantity.message}</p>
                        )}
                    </div>

                    <div className="mb-6">
                        <label htmlFor="unitPrice" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Unit Price *
                        </label>
                        <input
                            type="number"
                            id="unitPrice"
                            {...register('unitPrice')}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                            min="0"
                            step="0.01"
                        />
                        {errors.unitPrice && (
                            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.unitPrice.message}</p>
                        )}
                    </div>

                    <div className="mb-6">
                        <label htmlFor="location" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Location *
                        </label>
                        <select
                            id="location"
                            {...register('location')}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        >
                            <option value="warehouse">Warehouse</option>
                            <option value="store">Store</option>
                        </select>
                        {errors.location && (
                            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.location.message}</p>
                        )}
                    </div>

                    <div className="mb-6">
                        <label htmlFor="reason" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Reason *
                        </label>
                        <textarea
                            id="reason"
                            {...register('reason')}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                            rows={3}
                        ></textarea>
                        {errors.reason && (
                            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.reason.message}</p>
                        )}
                    </div>

                    <div className="flex justify-end">
                        <button
                            type="button"
                            onClick={() => reset()}
                            className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 mr-4 cursor-pointer"
                            disabled={submitting}
                        >
                            Reset
                        </button>
                        <button
                            type="submit"
                            className={`font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-opacity-50 cursor-pointer ${watchTransactionType === 'in'
                                ? 'bg-green-500 hover:bg-green-600 text-white focus:ring-green-500'
                                : watchTransactionType === 'out'
                                    ? 'bg-red-500 hover:bg-red-600 text-white focus:ring-red-500'
                                    : 'bg-amber-500 hover:bg-amber-600 text-white focus:ring-amber-500'
                                }`}
                            disabled={submitting}
                        >
                            {submitting ? 'Processing...' : watchTransactionType === 'in'
                                ? 'Add Stock'
                                : watchTransactionType === 'out'
                                    ? 'Remove Stock'
                                    : 'Set Stock Quantity'
                            }
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}

function fetchInventory() {
    throw new Error('Function not implemented.');
}
