import { createClient } from '@libsql/client';
import { drizzle } from 'drizzle-orm/libsql';
import * as schema from '../db/schema';

// Global cache for database connections
let clientInstance: ReturnType<typeof createClient> | null = null;
let dbInstance: ReturnType<typeof drizzle<typeof schema>> | null = null;

// Function to get or create a database client
function getClient() {
  if (!clientInstance) {
    clientInstance = createClient({
      url: process.env.TURSO_DATABASE_URL as string,
      authToken: process.env.TURSO_AUTH_TOKEN as string,
    });
  }
  return clientInstance;
}

// Function to get or create a Drizzle ORM instance
function getDb() {
  if (!dbInstance) {
    const client = getClient();
    dbInstance = drizzle(client, { schema });
  }
  return dbInstance;
}

// Export the database instance
export const db = getDb();