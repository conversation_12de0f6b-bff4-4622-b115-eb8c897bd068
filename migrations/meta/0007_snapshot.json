{"version": "6", "dialect": "sqlite", "id": "115c59bd-8c85-4dee-ba3b-4b8990a3df94", "prevId": "a43a5f47-059a-4c1b-ab3c-2af07ae1f7cc", "tables": {"branch_users": {"name": "branch_users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "branch_id": {"name": "branch_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"branch_users_branch_id_branches_id_fk": {"name": "branch_users_branch_id_branches_id_fk", "tableFrom": "branch_users", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "branch_users_user_id_users_id_fk": {"name": "branch_users_user_id_users_id_fk", "tableFrom": "branch_users", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "branches": {"name": "branches", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_main": {"name": "is_main", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"branches_tenant_id_users_id_fk": {"name": "branches_tenant_id_users_id_fk", "tableFrom": "branches", "tableTo": "users", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "customers": {"name": "customers", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'retail'"}, "credit_limit": {"name": "credit_limit", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "current_balance": {"name": "current_balance", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "extra_commission": {"name": "extra_commission", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"customers_tenant_id_users_id_fk": {"name": "customers_tenant_id_users_id_fk", "tableFrom": "customers", "tableTo": "users", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "dealer_payments": {"name": "dealer_payments", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "branch_id": {"name": "branch_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "dealer_id": {"name": "dealer_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "purchase_id": {"name": "purchase_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "amount": {"name": "amount", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "payment_method_id": {"name": "payment_method_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_method": {"name": "payment_method", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'cash'"}, "payment_reference": {"name": "payment_reference", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_type": {"name": "payment_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'purchase_payment'"}, "date": {"name": "date", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "performed_by": {"name": "performed_by", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"dealer_payments_tenant_id_users_id_fk": {"name": "dealer_payments_tenant_id_users_id_fk", "tableFrom": "dealer_payments", "tableTo": "users", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "dealer_payments_branch_id_branches_id_fk": {"name": "dealer_payments_branch_id_branches_id_fk", "tableFrom": "dealer_payments", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "dealer_payments_dealer_id_dealers_id_fk": {"name": "dealer_payments_dealer_id_dealers_id_fk", "tableFrom": "dealer_payments", "tableTo": "dealers", "columnsFrom": ["dealer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "dealer_payments_purchase_id_dealer_purchases_id_fk": {"name": "dealer_payments_purchase_id_dealer_purchases_id_fk", "tableFrom": "dealer_payments", "tableTo": "dealer_purchases", "columnsFrom": ["purchase_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "dealer_payments_payment_method_id_payment_methods_id_fk": {"name": "dealer_payments_payment_method_id_payment_methods_id_fk", "tableFrom": "dealer_payments", "tableTo": "payment_methods", "columnsFrom": ["payment_method_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "dealer_payments_performed_by_users_id_fk": {"name": "dealer_payments_performed_by_users_id_fk", "tableFrom": "dealer_payments", "tableTo": "users", "columnsFrom": ["performed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "dealer_purchase_items": {"name": "dealer_purchase_items", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "purchase_id": {"name": "purchase_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "unit_price": {"name": "unit_price", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "discount_percentage": {"name": "discount_percentage", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "discount_amount": {"name": "discount_amount", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "tax_percentage": {"name": "tax_percentage", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "tax_amount": {"name": "tax_amount", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "total": {"name": "total", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"dealer_purchase_items_purchase_id_dealer_purchases_id_fk": {"name": "dealer_purchase_items_purchase_id_dealer_purchases_id_fk", "tableFrom": "dealer_purchase_items", "tableTo": "dealer_purchases", "columnsFrom": ["purchase_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "dealer_purchase_items_product_id_products_id_fk": {"name": "dealer_purchase_items_product_id_products_id_fk", "tableFrom": "dealer_purchase_items", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "dealer_purchases": {"name": "dealer_purchases", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "branch_id": {"name": "branch_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "dealer_id": {"name": "dealer_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "purchase_no": {"name": "purchase_no", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "date": {"name": "date", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "sub_total": {"name": "sub_total", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "discount_percentage": {"name": "discount_percentage", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "discount_amount": {"name": "discount_amount", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "tax_amount": {"name": "tax_amount", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "total_amount": {"name": "total_amount", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "paid_amount": {"name": "paid_amount", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "due_amount": {"name": "due_amount", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "payment_method_id": {"name": "payment_method_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_method": {"name": "payment_method", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'cash'"}, "payment_status": {"name": "payment_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'unpaid'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'completed'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "performed_by": {"name": "performed_by", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"dealer_purchases_tenant_id_users_id_fk": {"name": "dealer_purchases_tenant_id_users_id_fk", "tableFrom": "dealer_purchases", "tableTo": "users", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "dealer_purchases_branch_id_branches_id_fk": {"name": "dealer_purchases_branch_id_branches_id_fk", "tableFrom": "dealer_purchases", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "dealer_purchases_dealer_id_dealers_id_fk": {"name": "dealer_purchases_dealer_id_dealers_id_fk", "tableFrom": "dealer_purchases", "tableTo": "dealers", "columnsFrom": ["dealer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "dealer_purchases_payment_method_id_payment_methods_id_fk": {"name": "dealer_purchases_payment_method_id_payment_methods_id_fk", "tableFrom": "dealer_purchases", "tableTo": "payment_methods", "columnsFrom": ["payment_method_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "dealer_purchases_performed_by_users_id_fk": {"name": "dealer_purchases_performed_by_users_id_fk", "tableFrom": "dealer_purchases", "tableTo": "users", "columnsFrom": ["performed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "dealers": {"name": "dealers", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "dealer_code": {"name": "dealer_code", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "contact_person": {"name": "contact_person", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "current_balance": {"name": "current_balance", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "credit_limit": {"name": "credit_limit", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "payment_terms": {"name": "payment_terms", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"dealers_tenant_id_users_id_fk": {"name": "dealers_tenant_id_users_id_fk", "tableFrom": "dealers", "tableTo": "users", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "expense_categories": {"name": "expense_categories", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"expense_categories_tenant_id_users_id_fk": {"name": "expense_categories_tenant_id_users_id_fk", "tableFrom": "expense_categories", "tableTo": "users", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "expenses": {"name": "expenses", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "branch_id": {"name": "branch_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "category_id": {"name": "category_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "amount": {"name": "amount", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "date": {"name": "date", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_method_id": {"name": "payment_method_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_method": {"name": "payment_method", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'cash'"}, "reference": {"name": "reference", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "performed_by": {"name": "performed_by", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"expenses_tenant_id_users_id_fk": {"name": "expenses_tenant_id_users_id_fk", "tableFrom": "expenses", "tableTo": "users", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "expenses_branch_id_branches_id_fk": {"name": "expenses_branch_id_branches_id_fk", "tableFrom": "expenses", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "expenses_category_id_expense_categories_id_fk": {"name": "expenses_category_id_expense_categories_id_fk", "tableFrom": "expenses", "tableTo": "expense_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "expenses_payment_method_id_payment_methods_id_fk": {"name": "expenses_payment_method_id_payment_methods_id_fk", "tableFrom": "expenses", "tableTo": "payment_methods", "columnsFrom": ["payment_method_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "expenses_performed_by_users_id_fk": {"name": "expenses_performed_by_users_id_fk", "tableFrom": "expenses", "tableTo": "users", "columnsFrom": ["performed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "inventory_transactions": {"name": "inventory_transactions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "branch_id": {"name": "branch_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "warehouse_quantity": {"name": "warehouse_quantity", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "store_quantity": {"name": "store_quantity", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "unit_price": {"name": "unit_price", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "date": {"name": "date", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "performed_by": {"name": "performed_by", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"inventory_transactions_tenant_id_users_id_fk": {"name": "inventory_transactions_tenant_id_users_id_fk", "tableFrom": "inventory_transactions", "tableTo": "users", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "inventory_transactions_branch_id_branches_id_fk": {"name": "inventory_transactions_branch_id_branches_id_fk", "tableFrom": "inventory_transactions", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "inventory_transactions_product_id_products_id_fk": {"name": "inventory_transactions_product_id_products_id_fk", "tableFrom": "inventory_transactions", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "inventory_transactions_performed_by_users_id_fk": {"name": "inventory_transactions_performed_by_users_id_fk", "tableFrom": "inventory_transactions", "tableTo": "users", "columnsFrom": ["performed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "order_items": {"name": "order_items", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "order_id": {"name": "order_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "unit_price": {"name": "unit_price", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "discount_percentage": {"name": "discount_percentage", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "discount_amount": {"name": "discount_amount", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "total": {"name": "total", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"order_items_order_id_orders_id_fk": {"name": "order_items_order_id_orders_id_fk", "tableFrom": "order_items", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "order_items_product_id_products_id_fk": {"name": "order_items_product_id_products_id_fk", "tableFrom": "order_items", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "orders": {"name": "orders", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "branch_id": {"name": "branch_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "memo_no": {"name": "memo_no", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "date": {"name": "date", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'completed'"}, "payment_method_id": {"name": "payment_method_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_method": {"name": "payment_method", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'cash'"}, "payment_status": {"name": "payment_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'paid'"}, "sub_total": {"name": "sub_total", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "discount_percentage": {"name": "discount_percentage", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "discount_amount": {"name": "discount_amount", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "total_amount": {"name": "total_amount", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "paid_amount": {"name": "paid_amount", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "due_amount": {"name": "due_amount", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "previous_due": {"name": "previous_due", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "ext_commission": {"name": "ext_commission", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "ext_commission_type": {"name": "ext_commission_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'percentage'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "performed_by": {"name": "performed_by", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"orders_tenant_id_users_id_fk": {"name": "orders_tenant_id_users_id_fk", "tableFrom": "orders", "tableTo": "users", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "orders_branch_id_branches_id_fk": {"name": "orders_branch_id_branches_id_fk", "tableFrom": "orders", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "orders_customer_id_customers_id_fk": {"name": "orders_customer_id_customers_id_fk", "tableFrom": "orders", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "orders_payment_method_id_payment_methods_id_fk": {"name": "orders_payment_method_id_payment_methods_id_fk", "tableFrom": "orders", "tableTo": "payment_methods", "columnsFrom": ["payment_method_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "orders_performed_by_users_id_fk": {"name": "orders_performed_by_users_id_fk", "tableFrom": "orders", "tableTo": "users", "columnsFrom": ["performed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "payment_methods": {"name": "payment_methods", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "is_default": {"name": "is_default", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"payment_methods_tenant_id_users_id_fk": {"name": "payment_methods_tenant_id_users_id_fk", "tableFrom": "payment_methods", "tableTo": "users", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "payments": {"name": "payments", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "branch_id": {"name": "branch_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "order_id": {"name": "order_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "amount": {"name": "amount", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "payment_method_id": {"name": "payment_method_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_method": {"name": "payment_method", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'cash'"}, "payment_reference": {"name": "payment_reference", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_type": {"name": "payment_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "performed_by": {"name": "performed_by", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "date": {"name": "date", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"payments_tenant_id_users_id_fk": {"name": "payments_tenant_id_users_id_fk", "tableFrom": "payments", "tableTo": "users", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payments_branch_id_branches_id_fk": {"name": "payments_branch_id_branches_id_fk", "tableFrom": "payments", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payments_customer_id_customers_id_fk": {"name": "payments_customer_id_customers_id_fk", "tableFrom": "payments", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "payments_order_id_orders_id_fk": {"name": "payments_order_id_orders_id_fk", "tableFrom": "payments", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "payments_payment_method_id_payment_methods_id_fk": {"name": "payments_payment_method_id_payment_methods_id_fk", "tableFrom": "payments", "tableTo": "payment_methods", "columnsFrom": ["payment_method_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "payments_performed_by_users_id_fk": {"name": "payments_performed_by_users_id_fk", "tableFrom": "payments", "tableTo": "users", "columnsFrom": ["performed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "product_categories": {"name": "product_categories", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"product_categories_tenant_id_users_id_fk": {"name": "product_categories_tenant_id_users_id_fk", "tableFrom": "product_categories", "tableTo": "users", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "products": {"name": "products", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "category_id": {"name": "category_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "vendor_id": {"name": "vendor_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "unit": {"name": "unit", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "cost_price": {"name": "cost_price", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "selling_price": {"name": "selling_price", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "discount": {"name": "discount", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "royalty_type": {"name": "royalty_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'none'"}, "royalty_value": {"name": "royalty_value", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "sku": {"name": "sku", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "weight": {"name": "weight", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"products_tenant_id_users_id_fk": {"name": "products_tenant_id_users_id_fk", "tableFrom": "products", "tableTo": "users", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "products_category_id_product_categories_id_fk": {"name": "products_category_id_product_categories_id_fk", "tableFrom": "products", "tableTo": "product_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "products_vendor_id_vendors_id_fk": {"name": "products_vendor_id_vendors_id_fk", "tableFrom": "products", "tableTo": "vendors", "columnsFrom": ["vendor_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "refund_items": {"name": "refund_items", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "refund_id": {"name": "refund_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "unit_price": {"name": "unit_price", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "total": {"name": "total", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_damaged": {"name": "is_damaged", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"refund_items_refund_id_refunds_id_fk": {"name": "refund_items_refund_id_refunds_id_fk", "tableFrom": "refund_items", "tableTo": "refunds", "columnsFrom": ["refund_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "refund_items_product_id_products_id_fk": {"name": "refund_items_product_id_products_id_fk", "tableFrom": "refund_items", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "refund_reasons": {"name": "refund_reasons", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "refunds": {"name": "refunds", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "branch_id": {"name": "branch_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "order_id": {"name": "order_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "reason_id": {"name": "reason_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "refund_date": {"name": "refund_date", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "total_amount": {"name": "total_amount", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_damaged": {"name": "is_damaged", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "performed_by": {"name": "performed_by", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"refunds_tenant_id_users_id_fk": {"name": "refunds_tenant_id_users_id_fk", "tableFrom": "refunds", "tableTo": "users", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "refunds_branch_id_branches_id_fk": {"name": "refunds_branch_id_branches_id_fk", "tableFrom": "refunds", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "refunds_order_id_orders_id_fk": {"name": "refunds_order_id_orders_id_fk", "tableFrom": "refunds", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "refunds_customer_id_customers_id_fk": {"name": "refunds_customer_id_customers_id_fk", "tableFrom": "refunds", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "refunds_reason_id_refund_reasons_id_fk": {"name": "refunds_reason_id_refund_reasons_id_fk", "tableFrom": "refunds", "tableTo": "refund_reasons", "columnsFrom": ["reason_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "refunds_performed_by_users_id_fk": {"name": "refunds_performed_by_users_id_fk", "tableFrom": "refunds", "tableTo": "users", "columnsFrom": ["performed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "royalty_transactions": {"name": "royalty_transactions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "vendor_id": {"name": "vendor_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "transaction_id": {"name": "transaction_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sale_amount": {"name": "sale_amount", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "royalty_amount": {"name": "royalty_amount", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_paid": {"name": "is_paid", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "payment_date": {"name": "payment_date", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"royalty_transactions_vendor_id_vendors_id_fk": {"name": "royalty_transactions_vendor_id_vendors_id_fk", "tableFrom": "royalty_transactions", "tableTo": "vendors", "columnsFrom": ["vendor_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "royalty_transactions_product_id_products_id_fk": {"name": "royalty_transactions_product_id_products_id_fk", "tableFrom": "royalty_transactions", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "royalty_transactions_transaction_id_inventory_transactions_id_fk": {"name": "royalty_transactions_transaction_id_inventory_transactions_id_fk", "tableFrom": "royalty_transactions", "tableTo": "inventory_transactions", "columnsFrom": ["transaction_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "sessions": {"name": "sessions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "stock_transaction_items": {"name": "stock_transaction_items", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "transaction_id": {"name": "transaction_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "unit_price": {"name": "unit_price", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "discount": {"name": "discount", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "tax": {"name": "tax", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "total": {"name": "total", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"stock_transaction_items_transaction_id_inventory_transactions_id_fk": {"name": "stock_transaction_items_transaction_id_inventory_transactions_id_fk", "tableFrom": "stock_transaction_items", "tableTo": "inventory_transactions", "columnsFrom": ["transaction_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "stock_transaction_items_product_id_products_id_fk": {"name": "stock_transaction_items_product_id_products_id_fk", "tableFrom": "stock_transaction_items", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "tenant_users": {"name": "tenant_users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"tenant_users_tenant_id_users_id_fk": {"name": "tenant_users_tenant_id_users_id_fk", "tableFrom": "tenant_users", "tableTo": "users", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "tenant_users_user_id_users_id_fk": {"name": "tenant_users_user_id_users_id_fk", "tableFrom": "tenant_users", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "tenant_license_management": {"name": "tenant_license_management", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "domain": {"name": "domain", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "company_name": {"name": "company_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "logo_url": {"name": "logo_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "subscription_type": {"name": "subscription_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'free-trial'"}, "subscription_start_date": {"name": "subscription_start_date", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "subscription_end_date": {"name": "subscription_end_date", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_status": {"name": "payment_status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'pending'"}, "last_payment_date": {"name": "last_payment_date", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_due_date": {"name": "payment_due_date", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "settings": {"name": "settings", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"tenant_license_management_user_id_users_id_fk": {"name": "tenant_license_management_user_id_users_id_fk", "tableFrom": "tenant_license_management", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_menu_permissions": {"name": "user_menu_permissions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "menu_id": {"name": "menu_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "can_view": {"name": "can_view", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "can_create": {"name": "can_create", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "can_edit": {"name": "can_edit", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "can_delete": {"name": "can_delete", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"user_menu_permissions_user_id_users_id_fk": {"name": "user_menu_permissions_user_id_users_id_fk", "tableFrom": "user_menu_permissions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_menu_permissions_tenant_id_users_id_fk": {"name": "user_menu_permissions_tenant_id_users_id_fk", "tableFrom": "user_menu_permissions", "tableTo": "users", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'tenant'"}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "last_login": {"name": "last_login", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "vendors": {"name": "vendors", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "vendor_code": {"name": "vendor_code", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"vendors_tenant_id_users_id_fk": {"name": "vendors_tenant_id_users_id_fk", "tableFrom": "vendors", "tableTo": "users", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "vendors_user_id_users_id_fk": {"name": "vendors_user_id_users_id_fk", "tableFrom": "vendors", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "verification_tokens": {"name": "verification_tokens", "columns": {"identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires": {"name": "expires", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}