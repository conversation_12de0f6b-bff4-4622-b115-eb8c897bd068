import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { vendors, users } from "@/db/schema";
import { auth } from "~/auth";
import { desc, sql } from "drizzle-orm";

// GET /api/vendors/direct - Get all vendors directly from the database
export async function GET() {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    console.log("Session user:", JSON.stringify(session.user, null, 2));
    
    // Get all vendors directly from the database without any filters
    // This is for debugging purposes only
    const allVendors = await db.query.vendors.findMany({
      orderBy: [desc(vendors.createdAt)],
      with: {
        user: {
          columns: {
            id: true,
            username: true,
            fullName: true,
            email: true,
            role: true,
          },
        },
      },
    });

    console.log(`Found ${allVendors.length} total vendors in the database`);
    
    // Get vendor count by tenant
    const vendorsByTenant = await db
      .select({
        tenantId: vendors.tenantId,
        count: sql`count(*)`,
      })
      .from(vendors)
      .groupBy(vendors.tenantId);
    
    console.log("Vendors by tenant:", vendorsByTenant);

    return NextResponse.json({
      success: true,
      vendors: allVendors,
      vendorsByTenant: vendorsByTenant,
      debug: {
        sessionUser: {
          id: session.user.id,
          role: session.user.role,
          tenantId: session.user.tenantId,
        }
      }
    });
  } catch (error) {
    console.error("Error fetching all vendors directly:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch vendors directly" },
      { status: 500 }
    );
  }
}
