import { Metadata } from "next";
import Link from "next/link";
import { redirect } from "next/navigation";
import { auth } from "~/auth";
import { db } from "@/lib/db";
import { customers, orders } from "@/db/schema";
import { eq } from "drizzle-orm";
import { PaymentForm } from "./_components/payment-form";

export const metadata: Metadata = {
    title: "Record Payment",
    description: "Record a payment from a customer",
};

export default async function NewPaymentPage({
    searchParams,
}: {
    searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
    const session = await auth();
    if (!session?.user) {
        redirect("/api/auth/signin");
    }

    const tenantId = session.user.id;
    const customerId = typeof (await searchParams).customerId === 'string' ? (await searchParams).customerId : undefined;
    const orderId = typeof (await searchParams).orderId === 'string' ? (await searchParams).orderId : undefined;

    // Get all customers with outstanding balances
    const allCustomers = await db.query.customers.findMany({
        where: (customers, { eq }) => eq(customers.tenantId, tenantId),
    });

    // If customerId is provided, get customer details
    let selectedCustomer = null;
    if (customerId) {
        selectedCustomer = await db.query.customers.findFirst({
            where: (customers, { eq }) => eq(customers.id, customerId as any),
        });
    }

    // If orderId is provided, get order details
    let selectedOrder = null;
    if (orderId) {
        selectedOrder = await db.query.orders.findFirst({
            where: (orders, { eq }) => eq(orders.id, orderId as any),
            with: {
                customer: true,
            },
        });

        // If order has a customer but no customerId was provided, set the selected customer
        if (selectedOrder?.customerId && !customerId) {
            selectedCustomer = await db.query.customers.findFirst({
                where: (customers, { eq }) => eq(customers.id, selectedOrder!.customerId!),
            });
        }
    }

    return (
        <div className="container py-10">
            <div className="flex items-center justify-between mb-6">
                <div>
                    <h1 className="text-3xl font-bold">Record Payment</h1>
                    <p className="text-muted-foreground">Record a payment from a customer</p>
                </div>
                <Link href="/dashboard/sales" className="text-sm bg-sky-100 p-4 text-blue-600 hover:underline">
                    Back to Sales
                </Link>
            </div>

            <PaymentForm
                customers={allCustomers}
                selectedCustomer={selectedCustomer as any}
                selectedOrder={selectedOrder as any}
                tenantId={tenantId}
            />
        </div>
    );
} 