import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { NextRequest } from 'next/server';
import { db } from '@/lib/db';
import { users, menus, userMenuPermissions } from '@/db/schema';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcryptjs';
import * as categoryApi from '@/app/api/products/categories/route';
import { auth } from '~/auth';

// Mock the auth module
vi.mock('~/auth', () => ({
  auth: vi.fn(),
}));

describe('Category Permissions', () => {
  // Test IDs
  const testTenantId = uuidv4();
  const testTenantSaleId = uuidv4();
  const testMenuId = uuidv4();
  
  // Setup test data
  beforeAll(async () => {
    // Create test menu for categories
    await db.insert(menus).values({
      id: testMenuId,
      name: 'categories',
      displayName: 'Categories',
      path: '/dashboard/products/categories',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    // Create tenant user
    await db.insert(users).values({
      id: testTenantId,
      username: 'test-tenant',
      email: '<EMAIL>',
      password: await bcrypt.hash('password123', 10),
      fullName: 'Test Tenant',
      role: 'tenant',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    // Create tenant_sale user
    await db.insert(users).values({
      id: testTenantSaleId,
      username: 'test-tenant-sale',
      email: '<EMAIL>',
      password: await bcrypt.hash('password123', 10),
      fullName: 'Test Tenant Sale',
      role: 'tenant_sale',
      tenantId: testTenantId,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });
  });
  
  // Clean up test data
  afterAll(async () => {
    await db.delete(userMenuPermissions).where(1 === 1);
    await db.delete(users).where(1 === 1);
    await db.delete(menus).where(1 === 1);
  });
  
  it('tenant_sale user with view permission can view categories', async () => {
    // Set up permissions for tenant_sale user (view only)
    await db.insert(userMenuPermissions).values({
      id: uuidv4(),
      userId: testTenantSaleId,
      menuId: testMenuId,
      tenantId: testTenantId,
      canView: true,
      canCreate: false,
      canEdit: false,
      canDelete: false,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    // Mock auth to return tenant_sale user
    (auth as any).mockResolvedValue({
      user: {
        id: testTenantSaleId,
        role: 'tenant_sale',
        tenantId: testTenantId
      }
    });
    
    // Create a mock request
    const req = new NextRequest('http://localhost:3000/api/products/categories');
    
    // Call the GET handler
    const response = await categoryApi.GET(req);
    const data = await response.json();
    
    // Expect successful response
    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
  });
  
  it('tenant_sale user without create permission cannot create categories', async () => {
    // Set up permissions for tenant_sale user (view only)
    await db.delete(userMenuPermissions).where(1 === 1);
    await db.insert(userMenuPermissions).values({
      id: uuidv4(),
      userId: testTenantSaleId,
      menuId: testMenuId,
      tenantId: testTenantId,
      canView: true,
      canCreate: false,
      canEdit: false,
      canDelete: false,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    // Mock auth to return tenant_sale user
    (auth as any).mockResolvedValue({
      user: {
        id: testTenantSaleId,
        role: 'tenant_sale',
        tenantId: testTenantId
      }
    });
    
    // Create a mock request
    const req = new NextRequest('http://localhost:3000/api/products/categories', {
      method: 'POST',
      body: JSON.stringify({
        name: 'Test Category',
        description: 'Test Description'
      })
    });
    
    // Call the POST handler
    const response = await categoryApi.POST(req);
    const data = await response.json();
    
    // Expect error response
    expect(response.status).toBe(403);
    expect(data.success).toBe(false);
    expect(data.error).toContain('permission');
  });
  
  it('tenant_sale user with create permission can create categories', async () => {
    // Set up permissions for tenant_sale user (with create permission)
    await db.delete(userMenuPermissions).where(1 === 1);
    await db.insert(userMenuPermissions).values({
      id: uuidv4(),
      userId: testTenantSaleId,
      menuId: testMenuId,
      tenantId: testTenantId,
      canView: true,
      canCreate: true,
      canEdit: false,
      canDelete: false,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    // Mock auth to return tenant_sale user
    (auth as any).mockResolvedValue({
      user: {
        id: testTenantSaleId,
        role: 'tenant_sale',
        tenantId: testTenantId
      }
    });
    
    // Create a mock request
    const req = new NextRequest('http://localhost:3000/api/products/categories', {
      method: 'POST',
      body: JSON.stringify({
        name: 'Test Category',
        description: 'Test Description'
      })
    });
    
    // Call the POST handler
    const response = await categoryApi.POST(req);
    const data = await response.json();
    
    // Expect successful response
    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
  });
});
