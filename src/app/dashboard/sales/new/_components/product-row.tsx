"use client";

import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { formatCurrency } from "@/lib/utils";

type Product = {
    id: string;
    name: string;
    code?: string | null;
    unit: string;
    sellingPrice: number;
    costPrice: number;
    discount?: number;
    category?: { name: string } | null;
    vendor?: { name: string } | null;
    stock?: number;
};

type OrderItem = {
    id: string;
    productId: string;
    productName: string;
    quantity: number;
    unitPrice: number;
    discountPercentage: number;
    total: number;
    code?: string;
    stock?: number;
};

interface ProductRowProps {
    index: number;
    item: OrderItem;
    products: Product[];
    updateItem: (updates: Partial<OrderItem>) => void;
    removeItem: () => void;
    showRemove: boolean;
}

export function ProductRow({
    index,
    item,
    products,
    updateItem,
    removeItem,
    showRemove
}: ProductRowProps) {
    // Handle product selection
    const handleProductChange = (productId: string) => {
        if (!productId) {
            updateItem({
                productId: "",
                productName: "",
                unitPrice: 0,
                total: 0,
                code: "",
                stock: 0
            });
            return;
        }

        const selectedProduct = products.find(p => p.id === productId);
        if (selectedProduct) {
            const updates = {
                productId,
                productName: selectedProduct.name,
                unitPrice: selectedProduct.sellingPrice,
                discountPercentage: selectedProduct.discount || 0,
                code: selectedProduct.code || "",
                stock: selectedProduct.stock || 0,
            };

            // Calculate the total
            const quantity = item.quantity;
            const unitPrice = selectedProduct.sellingPrice;
            const discountPercentage = selectedProduct.discount || 0;

            const totalBeforeDiscount = quantity * unitPrice;
            const discountAmount = totalBeforeDiscount * (discountPercentage / 100);
            const total = totalBeforeDiscount - discountAmount;

            updateItem({
                ...updates,
                total: Number(total.toFixed(2))
            });
        }
    };

    // Handle quantity change
    const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const quantity = Number(e.target.value);
        updateItem({ quantity });
    };

    // Handle unit price change
    const handleUnitPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const unitPrice = Number(e.target.value);
        updateItem({ unitPrice });
    };

    // Handle discount change
    const handleDiscountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const discountPercentage = Number(e.target.value);
        updateItem({ discountPercentage });
    };

    // Get the selected product
    const selectedProduct = products.find(p => p.id === item.productId);

    return (
        <tr className="border-b hover:bg-gray-50">
            <td className="py-2 px-4">{index + 1}</td>
            <td className="py-2 px-4">
                <Select value={item.productId} onValueChange={handleProductChange}>
                    <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select book" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="">Select book</SelectItem>
                        {products.map((product) => (
                            <SelectItem key={product.id} value={product.id}>
                                {product.name}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </td>
            <td className="py-2 px-4">
                <Input
                    type="number"
                    min="1"
                    value={item.quantity}
                    onChange={handleQuantityChange}
                    className="text-center"
                />
            </td>

            <td className="py-2 px-4">
                <div className={`text-sm text-center ${item.stock === 0 ? 'text-red-500 font-semibold' : ''}`}>
                    {item.stock !== undefined ? `${item.stock} ${selectedProduct?.unit || ''}` : '0'}
                </div>
            </td>
            <td className="py-2 px-4">
                <Input
                    type="number"
                    step="0.01"
                    value={item.unitPrice}
                    onChange={handleUnitPriceChange}
                    className="text-right"
                />
            </td>
            <td className="py-2 px-4">
                <Input
                    type="number"
                    min="0"
                    max="100"
                    step="0.01"
                    value={item.discountPercentage}
                    onChange={handleDiscountChange}
                    className="text-right"
                />
            </td>
            <td className="py-2 px-4">
                <Input
                    type="text"
                    value={formatCurrency(item.total)}
                    readOnly
                    className="text-right bg-gray-50"
                />
            </td>
            <td className="py-2 px-4 text-center">
                {showRemove && (
                    <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={removeItem}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                    >
                        <Trash2 className="h-4 w-4" />
                    </Button>
                )}
            </td>
        </tr>
    );
}