import { NextRequest, NextResponse } from "next/server";
import { auth } from "~/auth";
import { db } from "@/lib/db";
import { products, inventoryTransactions, branches } from "@/db/schema";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import { eq, and, sum } from "drizzle-orm";

// Schema for validating stock transfers
const transferSchema = z.object({
  productId: z.string().min(1, "Product ID is required"),
  branchId: z.string().min(1, "Branch ID is required"),
  quantity: z.number().int().min(1, "Quantity must be at least 1"),
  notes: z.string().nullable().optional(),
});

// POST /api/inventory/warehouse-to-store - Transfer stock from warehouse to store
export async function POST(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Parse and validate the request body
    const body = await req.json();
    console.log("Received warehouse-to-store transfer request:", body);

    const validationResult = transferSchema.safeParse(body);

    if (!validationResult.success) {
      console.error("Validation failed:", validationResult.error.errors);
      return NextResponse.json(
        { success: false, error: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { productId, branchId, quantity, notes } = validationResult.data;

    // Get tenant ID from session based on user role
    let tenantId: string;
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      // For sales users, use their associated tenant's ID
      tenantId = session.user.tenantId;
    } else if (session.user.tenantId) {
      // For tenant users with tenantId
      tenantId = session.user.tenantId;
    } else {
      // Fallback to user ID
      tenantId = session.user.id;
    }
    console.log('POST /api/inventory/warehouse-to-store - Using tenant ID:', tenantId, 'for user role:', session.user.role);

    // Check if product exists and belongs to the tenant
    const product = await db.query.products.findFirst({
      where: and(eq(products.id, productId), eq(products.tenantId, tenantId)),
    });

    if (!product) {
      return NextResponse.json(
        {
          success: false,
          error: "Product not found or does not belong to this tenant",
        },
        { status: 404 }
      );
    }

    // Verify branch exists and belongs to the tenant
    const branch = await db
      .select()
      .from(branches)
      .where(
        and(
          eq(branches.id, branchId),
          eq(branches.tenantId, tenantId)
        )
      )
      .limit(1);

    if (branch.length === 0) {
      return NextResponse.json(
        { success: false, error: "Branch not found or access denied" },
        { status: 404 }
      );
    }

    // Calculate current warehouse stock for this branch and product
    const inventoryData = await db
      .select({
        warehouseStock: sum(inventoryTransactions.warehouseQuantity),
      })
      .from(inventoryTransactions)
      .where(
        and(
          eq(inventoryTransactions.tenantId, tenantId),
          eq(inventoryTransactions.branchId, branchId),
          eq(inventoryTransactions.productId, productId),
          eq(inventoryTransactions.type, "in")
        )
      );

    const outInventoryData = await db
      .select({
        warehouseStock: sum(inventoryTransactions.warehouseQuantity),
      })
      .from(inventoryTransactions)
      .where(
        and(
          eq(inventoryTransactions.tenantId, tenantId),
          eq(inventoryTransactions.branchId, branchId),
          eq(inventoryTransactions.productId, productId),
          eq(inventoryTransactions.type, "out")
        )
      );

    const warehouseIn = Number(inventoryData[0]?.warehouseStock || 0);
    const warehouseOut = Number(outInventoryData[0]?.warehouseStock || 0);
    const currentWarehouseStock = warehouseIn - warehouseOut;

    // Check if there's enough stock in warehouse for transfer
    if (quantity > currentWarehouseStock) {
      return NextResponse.json(
        {
          success: false,
          error: `Not enough stock in warehouse. Available: ${currentWarehouseStock}`,
        },
        { status: 400 }
      );
    }

    // Create two transactions: one to reduce warehouse stock, one to increase store stock
    const outTransactionId = uuidv4();

    // First, decrease warehouse stock (out transaction for warehouse)
    await db.insert(inventoryTransactions).values({
      id: outTransactionId,
      tenantId,
      branchId,
      productId,
      quantity: 0, // Total remains unchanged
      warehouseQuantity: quantity, // Reduce warehouse by quantity
      storeQuantity: 0, // Store unchanged in this transaction
      unitPrice: product.costPrice,
      type: "out", // Out from warehouse
      date: new Date(),
      notes: notes || `Transfer from Warehouse to Store`,
      performedBy: session.user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Second, increase store stock (in transaction for store)
    const inTransactionId = uuidv4();
    await db.insert(inventoryTransactions).values({
      id: inTransactionId,
      tenantId,
      branchId,
      productId,
      quantity: 0, // Total remains unchanged
      warehouseQuantity: 0, // Warehouse unchanged in this transaction
      storeQuantity: quantity, // Increase store by quantity
      unitPrice: product.costPrice,
      type: "in", // In to store
      date: new Date(),
      notes: notes || `Transfer from Warehouse to Store`,
      performedBy: session.user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return NextResponse.json({
      success: true,
      message: "Stock successfully transferred from warehouse to store",
      quantity,
      outTransactionId,
      inTransactionId,
    });
  } catch (error) {
    console.error("Error transferring stock:", error);
    return NextResponse.json(
      { success: false, error: "Failed to transfer stock" },
      { status: 500 }
    );
  }
}
