'use client';

import { useEffect, useState } from 'react';

interface LoadingOverlayProps {
  message?: string;
  isVisible: boolean;
  onAnimationComplete?: () => void;
}

export default function LoadingOverlay({
  message = 'Processing...',
  isVisible,
  onAnimationComplete
}: LoadingOverlayProps) {
  const [isExiting, setIsExiting] = useState(false);
  const [shouldRender, setShouldRender] = useState(isVisible);

  useEffect(() => {
    // When becoming visible, render immediately
    if (isVisible) {
      setShouldRender(true);
    }

    // When hiding, start exit animation
    if (!isVisible && !isExiting && shouldRender) {
      setIsExiting(true);
      const timer = setTimeout(() => {
        setIsExiting(false);
        // Only remove from DOM after animation completes
        setShouldRender(false);
        if (onAnimationComplete) {
          onAnimationComplete();
        }
      }, 500); // Match this with the CSS transition duration

      return () => clearTimeout(timer);
    }
  }, [isVisible, isExiting, onAnimationComplete, shouldRender]);

  // Don't render anything if not visible and not exiting
  if (!shouldRender) return null;

  return (
    <div
      className={`fixed inset-0 bg-black/50 flex flex-col items-center justify-center z-[9999] transition-opacity duration-500 ${isVisible ? 'opacity-100' : 'opacity-0'
        }`}
      style={{
        pointerEvents: isVisible ? 'auto' : 'none',
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0
      }}
    >
      <div className="bg-white rounded-lg p-8 max-w-md w-full shadow-xl transform transition-transform duration-500 ease-out">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-500 mb-4"></div>
          <p className="text-lg font-medium text-gray-700">{message}</p>
          <div className="mt-4 w-full bg-gray-200 rounded-full h-2.5">
            <div className="bg-blue-600 h-2.5 rounded-full animate-pulse w-full"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
