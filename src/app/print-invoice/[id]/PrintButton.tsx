"use client";

import { useState } from "react";

export default function PrintButton() {
    const [isPrinting, setIsPrinting] = useState(false);

    function handlePrint() {
        setIsPrinting(true);

        // Short delay to update UI before printing
        setTimeout(() => {
            window.print();
            setIsPrinting(false);
        }, 100);
    }

    return (
        <button
            onClick={handlePrint}
            disabled={isPrinting}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-blue-300 print:hidden"
        >
            {isPrinting ? "Printing..." : "Print Invoice"}
        </button>
    );
}