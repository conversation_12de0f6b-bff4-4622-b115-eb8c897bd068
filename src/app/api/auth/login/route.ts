import { db } from "../../../../lib/db";
import { users, sessions, tenants, tenantUsers, userMenuPermissions } from "../../../../db/schema";
import { v4 as uuidv4 } from "uuid";
import bcrypt from "bcryptjs";
import { eq, and } from "drizzle-orm";
import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json();

    // Find user by email
    const user = await db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .get();

    if (!user) {
      return NextResponse.json(
        { success: false, error: "Invalid credentials" },
        { status: 401 }
      );
    }

    // Verify password
    const passwordMatch = await bcrypt.compare(password, user.password);

    if (!passwordMatch) {
      return NextResponse.json(
        { success: false, error: "Invalid credentials" },
        { status: 401 }
      );
    }

    let tenantId = null;

    // Check if user is active and has a valid tenant
    if (user.role === "tenant") {
      // For tenant users, check direct tenant record
      const tenantInfo = await db
        .select()
        .from(tenants)
        .where(eq(tenants.userId, user.id))
        .get();

      if (!tenantInfo) {
        return NextResponse.json(
          { success: false, error: "User is not associated with any tenant" },
          { status: 401 }
        );
      } else {
        // Check if tenant is active and not expired subscription
        const now = new Date();
        tenantId = tenantInfo.id;

        if (!tenantInfo.isActive) {
          return NextResponse.json(
            { success: false, error: "Tenant account is inactive" },
            { status: 401 }
          );
        }

        // Check if subscription has expired or doesn't exist
        if (!tenantInfo.subscriptionEndDate || tenantInfo.subscriptionEndDate < now) {
          return NextResponse.json(
            { success: false, error: "Tenant subscription has expired" },
            { status: 401 }
          );
        }
      }
    } else if (user.role === "tenant_sale") {
      // For tenant_sale users, find their associated tenant through tenantUsers
      const tenantUser = await db
        .select({
          tenantId: tenantUsers.tenantId,
        })
        .from(tenantUsers)
        .where(eq(tenantUsers.userId, user.id))
        .get();

      if (!tenantUser) {
        return NextResponse.json(
          {
            success: false,
            error: "Sales user is not associated with any tenant",
          },
          { status: 401 }
        );
      }

      // Get the tenant owner (user with tenant role)
      const tenantOwner = await db
        .select()
        .from(users)
        .where(eq(users.id, tenantUser.tenantId))
        .get();

      if (!tenantOwner || !tenantOwner.isActive) {
        return NextResponse.json(
          { success: false, error: "Tenant account is inactive" },
          { status: 401 }
        );
      }

      // Get tenant details to check subscription
      const tenantInfo = await db
        .select()
        .from(tenants)
        .where(eq(tenants.userId, tenantOwner.id))
        .get();

      if (!tenantInfo) {
        return NextResponse.json(
          { success: false, error: "Associated tenant not found" },
          { status: 401 }
        );
      }

      // Check if tenant is active and subscription is valid
      const now = new Date();

      if (!tenantInfo.isActive) {
        return NextResponse.json(
          { success: false, error: "Tenant account is inactive" },
          { status: 401 }
        );
      }

      // Check if subscription has expired or doesn't exist
      if (!tenantInfo.subscriptionEndDate || tenantInfo.subscriptionEndDate < now) {
        return NextResponse.json(
          { success: false, error: "Tenant subscription has expired" },
          { status: 401 }
        );
      }

      // Check if user has any menu permissions
      const permissions = await db
        .select()
        .from(userMenuPermissions)
        .where(
          and(
            eq(userMenuPermissions.userId, user.id),
            eq(userMenuPermissions.tenantId, tenantOwner.id)
          )
        )
        .all();

      if (!permissions || permissions.length === 0) {
        return NextResponse.json(
          { success: false, error: "User has no menu permissions" },
          { status: 401 }
        );
      }

      tenantId = tenantOwner.id;
    }

    // Create session
    const sessionId = uuidv4();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // Session expires in 7 days

    await db.insert(sessions).values({
      id: sessionId,
      userId: user.id,
      expiresAt: Math.floor(expiresAt.getTime() / 1000), // Convert to Unix timestamp
    });

    // Set session cookie
    const response = NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.fullName,
        role: user.role,
        tenantId: tenantId,
      },
    });

    response.cookies.set({
      name: "sessionId",
      value: sessionId,
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      maxAge: 60 * 60, // 1 hour in seconds
      path: "/",
    });

    return response;
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      { success: false, error: "Failed to login" },
      { status: 500 }
    );
  }
}
