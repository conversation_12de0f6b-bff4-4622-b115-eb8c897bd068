import { NextRequest, NextResponse } from "next/server";
import { auth } from "../../../../../../auth";
import { db } from "@/lib/db";
import { refundReasons } from "@/db/schema";
import { eq } from "drizzle-orm";
import { z } from "zod";

// Schema for validating reason data
const reasonSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
});

// GET /api/refunds/reasons/[id] - Get a specific refund reason
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    const reasonId = (await params).id;

    // Get the refund reason
    const reason = await db.query.refundReasons.findFirst({
      where: eq(refundReasons.id, reasonId),
    });

    if (!reason) {
      return NextResponse.json(
        { success: false, error: "Refund reason not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      reason,
    });
  } catch (error) {
    console.error("Error fetching refund reason:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch refund reason" },
      { status: 500 }
    );
  }
}

// PUT /api/refunds/reasons/[id] - Update a refund reason
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Only admin and tenant users can update refund reasons
    if (session.user.role !== 'admin' && session.user.role !== 'tenant') {
      return NextResponse.json(
        { success: false, error: "Unauthorized. Only admin and tenant users can update refund reasons." },
        { status: 403 }
      );
    }

    const reasonId = (await params).id;

    // Check if reason exists
    const existingReason = await db.query.refundReasons.findFirst({
      where: eq(refundReasons.id, reasonId),
    });

    if (!existingReason) {
      return NextResponse.json(
        { success: false, error: "Refund reason not found" },
        { status: 404 }
      );
    }

    // Parse and validate request body
    const body = await req.json();
    const validationResult = reasonSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid reason data",
          details: validationResult.error.format()
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Update the refund reason
    const result = await db
      .update(refundReasons)
      .set({
        name: data.name,
        description: data.description || null,
        isActive: data.isActive,
        updatedAt: new Date(),
      })
      .where(eq(refundReasons.id, reasonId))
      .returning();

    return NextResponse.json({
      success: true,
      message: "Refund reason updated successfully",
      reason: result[0],
    });
  } catch (error) {
    console.error("Error updating refund reason:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update refund reason" },
      { status: 500 }
    );
  }
}

// DELETE /api/refunds/reasons/[id] - Delete a refund reason
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Only admin and tenant users can delete refund reasons
    if (session.user.role !== 'admin' && session.user.role !== 'tenant') {
      return NextResponse.json(
        { success: false, error: "Unauthorized. Only admin and tenant users can delete refund reasons." },
        { status: 403 }
      );
    }

    const reasonId = (await params).id;

    // Check if reason exists
    const existingReason = await db.query.refundReasons.findFirst({
      where: eq(refundReasons.id, reasonId),
    });

    if (!existingReason) {
      return NextResponse.json(
        { success: false, error: "Refund reason not found" },
        { status: 404 }
      );
    }

    // Check if the reason is being used in any refunds
    const refundsUsingReason = await db.query.refunds.findFirst({
      where: (refunds, { eq }) => eq(refunds.reasonId, reasonId),
    });

    if (refundsUsingReason) {
      return NextResponse.json(
        { 
          success: false, 
          error: "Cannot delete refund reason as it is being used in existing refunds. You can deactivate it instead." 
        },
        { status: 400 }
      );
    }

    // Delete the refund reason
    await db.delete(refundReasons).where(eq(refundReasons.id, reasonId));

    return NextResponse.json({
      success: true,
      message: "Refund reason deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting refund reason:", error);
    return NextResponse.json(
      { success: false, error: "Failed to delete refund reason" },
      { status: 500 }
    );
  }
}
