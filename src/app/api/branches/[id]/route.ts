import { NextRequest, NextResponse } from "next/server";
import { auth } from "../../../../../auth";
import { db } from "@/lib/db";
import { branches, users, branchUsers, inventoryTransactions, orders, payments } from "@/db/schema";
import { z } from "zod";
import { eq, sql } from "drizzle-orm";

// Schema for validating branch updates
const branchUpdateSchema = z.object({
  name: z.string().optional().nullable().or(z.literal('')),
  code: z.string().optional().nullable().or(z.literal('')),
  address: z.string().optional().nullable().or(z.literal('')),
  phone: z.string().optional().nullable().or(z.literal('')),
  email: z.string().optional().nullable().or(z.literal('')),
  isMain: z.boolean().optional().nullable().default(false),
  isActive: z.boolean().optional().nullable().default(true),
  notes: z.string().optional().nullable().or(z.literal('')),
});

// GET /api/branches/[id] - Get a single branch
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    const branchId = (await params).id;

    // Get the branch
    const branch = await db
      .select()
      .from(branches)
      .where(eq(branches.id, branchId))
      .limit(1);

    if (branch.length === 0) {
      return NextResponse.json(
        { success: false, error: "Branch not found" },
        { status: 404 }
      );
    }

    // Determine the effective tenant ID based on user role
    let effectiveTenantId = session.user.id;

    if (session.user.role === "tenant_sale" && session.user.tenantId) {
      // For tenant_sale users, use their tenant's ID
      effectiveTenantId = session.user.tenantId;
    } else if (session.user.role === "tenant") {
      // For tenant users, use their own ID
      effectiveTenantId = session.user.id;
    }

    // Check if user has access to this branch
    // Admin and tenant roles have full access
    // tenant_sale users need to have the correct tenantId
    if (
      session.user.role === "tenant_sale" &&
      branch[0].tenantId !== effectiveTenantId
    ) {
      return NextResponse.json(
        { success: false, error: "Access denied for tenant_sale user" },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      branch: branch[0],
    });
  } catch (error) {
    console.error("Error fetching branch:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch branch" },
      { status: 500 }
    );
  }
}

// PATCH /api/branches/[id] - Update a branch
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    const branchId = (await params).id;

    // Get the branch to check if it exists and if user has access
    const existingBranch = await db
      .select()
      .from(branches)
      .where(eq(branches.id, branchId))
      .limit(1);

    if (existingBranch.length === 0) {
      return NextResponse.json(
        { success: false, error: "Branch not found" },
        { status: 404 }
      );
    }

    // Determine the effective tenant ID based on user role
    let effectiveTenantId = session.user.id;

    if (session.user.role === "tenant_sale" && session.user.tenantId) {
      // For tenant_sale users, use their tenant's ID
      effectiveTenantId = session.user.tenantId;
    } else if (session.user.role === "tenant") {
      // For tenant users, use their own ID
      effectiveTenantId = session.user.id;
    }

    // Check if user has access to this branch
    // Admin and tenant roles have full access
    // tenant_sale users need to have the correct tenantId
    if (
      session.user.role === "tenant_sale" &&
      existingBranch[0].tenantId !== effectiveTenantId
    ) {
      return NextResponse.json(
        { success: false, error: "Access denied for tenant_sale user" },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = branchUpdateSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: "Validation failed",
          details: validationResult.error.format(),
        },
        { status: 400 }
      );
    }

    const branchData = validationResult.data;

    // If this is set as main branch, unset any existing main branch
    if (branchData.isMain && !existingBranch[0].isMain) {
      await db
        .update(branches)
        .set({ isMain: false })
        .where(eq(branches.tenantId, existingBranch[0].tenantId));
    }

    // Prepare update data - only include fields that are provided
    const updateData: any = {
      updatedAt: new Date(),
    };

    // Only include fields that are defined in the request
    if (branchData.name !== undefined) updateData.name = branchData.name;
    if (branchData.code !== undefined) updateData.code = branchData.code;
    if (branchData.address !== undefined) updateData.address = branchData.address;
    if (branchData.phone !== undefined) updateData.phone = branchData.phone;
    if (branchData.email !== undefined) updateData.email = branchData.email;
    if (branchData.isMain !== undefined) updateData.isMain = branchData.isMain;
    if (branchData.isActive !== undefined) updateData.isActive = branchData.isActive;
    if (branchData.notes !== undefined) updateData.notes = branchData.notes;

    // Update the branch
    const [updatedBranch] = await db
      .update(branches)
      .set(updateData)
      .where(eq(branches.id, branchId))
      .returning();

    return NextResponse.json({
      success: true,
      branch: updatedBranch,
    });
  } catch (error) {
    console.error("Error updating branch:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update branch" },
      { status: 500 }
    );
  }
}

// DELETE /api/branches/[id] - Delete a branch
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    const branchId = (await params).id;

    // Get the branch to check if it exists and if user has access
    const existingBranch = await db
      .select()
      .from(branches)
      .where(eq(branches.id, branchId))
      .limit(1);

    if (existingBranch.length === 0) {
      return NextResponse.json(
        { success: false, error: "Branch not found" },
        { status: 404 }
      );
    }

    // Determine the effective tenant ID based on user role
    let effectiveTenantId = session.user.id;

    if (session.user.role === "tenant_sale" && session.user.tenantId) {
      // For tenant_sale users, use their tenant's ID
      effectiveTenantId = session.user.tenantId;
    } else if (session.user.role === "tenant") {
      // For tenant users, use their own ID
      effectiveTenantId = session.user.id;
    }

    // Check if user has access to this branch
    // Admin and tenant roles have full access
    // tenant_sale users need to have the correct tenantId
    if (
      session.user.role === "tenant_sale" &&
      existingBranch[0].tenantId !== effectiveTenantId
    ) {
      return NextResponse.json(
        { success: false, error: "Access denied for tenant_sale user" },
        { status: 403 }
      );
    }

    // Check if this is the main branch
    if (existingBranch[0].isMain) {
      return NextResponse.json(
        { success: false, error: "Cannot delete the main branch. Please set another branch as main first." },
        { status: 400 }
      );
    }

    // Check if there are any related records in other tables
    // This is a simplified check - in a real application, you would check all related tables
    try {
      // Check for branch users
      const branchUserCount = await db
        .select({ count: sql`count(*)` })
        .from(branchUsers)
        .where(eq(branchUsers.branchId, branchId));

      console.log('Related branch users count:', branchUserCount[0].count);

      if ((branchUserCount[0] as { count: number }).count > 0) {
        return NextResponse.json(
          { success: false, error: "Cannot delete branch because it has associated users. Please remove all users from this branch first." },
          { status: 400 }
        );
      }

      // Check for inventory transactions
      const inventoryCount = await db
        .select({ count: sql`count(*)` })
        .from(inventoryTransactions)
        .where(eq(inventoryTransactions.branchId, branchId));

      console.log('Related inventory transactions count:', inventoryCount[0].count);

      if ((inventoryCount[0] as { count: number }).count > 0) {
        return NextResponse.json(
          { success: false, error: "Cannot delete branch because it has associated inventory transactions." },
          { status: 400 }
        );
      }

      // Source and target branch references checks removed as these columns no longer exist

      // Check for orders
      const orderCount = await db
        .select({ count: sql`count(*)` })
        .from(orders)
        .where(eq(orders.branchId, branchId));

      console.log('Related orders count:', orderCount[0].count);

      if ((orderCount[0] as { count: number }).count > 0) {
        return NextResponse.json(
          { success: false, error: "Cannot delete branch because it has associated orders." },
          { status: 400 }
        );
      }

      // Check for payments
      const paymentCount = await db
        .select({ count: sql`count(*)` })
        .from(payments)
        .where(eq(payments.branchId, branchId));

      console.log('Related payments count:', paymentCount[0].count);

      if ((paymentCount[0] as { count: number }).count > 0) {
        return NextResponse.json(
          { success: false, error: "Cannot delete branch because it has associated payments." },
          { status: 400 }
        );
      }
    } catch (error) {
      console.error('Error checking related records:', error);
      // Continue with deletion even if check fails
    }

    // Delete the branch
    console.log(`Attempting to delete branch with ID: ${branchId}`);
    try {
      await db.delete(branches).where(eq(branches.id, branchId));
      console.log(`Successfully deleted branch with ID: ${branchId}`);

      return NextResponse.json({
        success: true,
        message: "Branch deleted successfully",
      });
    } catch (deleteError) {
      console.error("Error in delete operation:", deleteError);
      throw deleteError; // Re-throw to be caught by the outer try/catch
    }
  } catch (error) {
    console.error("Error deleting branch:", error);
    let errorMessage = "Failed to delete branch";

    if (error instanceof Error) {
      errorMessage = `Error deleting branch: ${error.message}`;
      console.error("Error details:", error.stack);
    }

    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}
