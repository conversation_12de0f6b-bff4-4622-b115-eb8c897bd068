'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { PlusIcon, SearchIcon, EditIcon, EyeIcon, TrashIcon } from 'lucide-react';
import { toast } from 'sonner';
import { usePermissionCheck } from '@/lib/check-permissions';

interface Dealer {
  id: string;
  name: string;
  dealerCode?: string;
  email?: string;
  phone?: string;
  address?: string;
  contactPerson?: string;
  currentBalance: number;
  creditLimit: number;
  paymentTerms?: string;
  isActive: boolean;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export default function DealersPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [dealers, setDealers] = useState<Dealer[]>([]);
  const [filteredDealers, setFilteredDealers] = useState<Dealer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  // Check permissions
  usePermissionCheck('/dashboard/dealers', 'view');

  useEffect(() => {
    fetchDealers();
  }, []);

  useEffect(() => {
    // Filter dealers based on search query
    if (searchQuery.trim() === '') {
      setFilteredDealers(dealers);
    } else {
      const filtered = dealers.filter(dealer =>
        dealer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        dealer.dealerCode?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        dealer.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        dealer.phone?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredDealers(filtered);
    }
  }, [searchQuery, dealers]);

  const fetchDealers = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/dealers');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch dealers: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setDealers(data.dealers || []);
        setFilteredDealers(data.dealers || []);
      } else {
        setError(data.error || 'Failed to load dealers');
        toast.error(data.error || 'Failed to load dealers');
      }
    } catch (err) {
      console.error('Error fetching dealers:', err);
      setError('An error occurred while fetching dealers');
      toast.error('An error occurred while fetching dealers');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateDealer = () => {
    router.push('/dashboard/dealers/new');
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleEditDealer = (dealerId: string) => {
    router.push(`/dashboard/dealers/edit/${dealerId}`);
  };

  const handleViewDealer = (dealerId: string) => {
    router.push(`/dashboard/dealers/${dealerId}`);
  };

  const handleDeleteDealer = async (dealer: Dealer) => {
    if (!confirm(`Are you sure you want to delete dealer "${dealer.name}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/dealers?id=${dealer.id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Dealer deleted successfully');
        fetchDealers(); // Refresh the list
      } else {
        toast.error(data.error || 'Failed to delete dealer');
      }
    } catch (error) {
      console.error('Error deleting dealer:', error);
      toast.error('An error occurred while deleting dealer');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BDT',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getBalanceColor = (balance: number) => {
    if (balance > 0) return 'text-red-600'; // We owe them money
    if (balance < 0) return 'text-green-600'; // They owe us money
    return 'text-gray-600'; // Balanced
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading dealers...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg text-red-600">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Dealer Management</h1>
        <Button onClick={handleCreateDealer} className="gap-1 cursor-pointer">
          <PlusIcon className="h-4 w-4" />
          Add New Dealer
        </Button>
      </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex gap-4 items-center">
            <div className="relative flex-1">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search dealers by name, code, email, or phone..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="pl-10"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dealers Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            Dealers ({filteredDealers.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredDealers.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">
                {searchQuery ? 'No dealers found matching your search.' : 'No dealers found.'}
              </p>
              {!searchQuery && (
                <Button onClick={handleCreateDealer}>
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add Your First Dealer
                </Button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Balance</TableHead>
                    <TableHead>Credit Limit</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredDealers.map((dealer) => (
                    <TableRow key={dealer.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <TableCell>
                        <div className="font-medium">{dealer.name}</div>
                        {dealer.dealerCode && (
                          <div className="text-sm text-gray-500">Code: {dealer.dealerCode}</div>
                        )}
                        {dealer.contactPerson && (
                          <div className="text-sm text-gray-500">Contact: {dealer.contactPerson}</div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div>{dealer.email || 'N/A'}</div>
                        <div className="text-sm text-gray-500">{dealer.phone || 'N/A'}</div>
                        {dealer.address && (
                          <div className="text-sm text-gray-500">{dealer.address}</div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className={`font-medium ${getBalanceColor(dealer.currentBalance)}`}>
                          {formatCurrency(Math.abs(dealer.currentBalance))}
                        </div>
                        <div className="text-xs text-gray-500">
                          {dealer.currentBalance > 0 ? 'We owe' : dealer.currentBalance < 0 ? 'They owe' : 'Balanced'}
                        </div>
                      </TableCell>
                      <TableCell>
                        {formatCurrency(dealer.creditLimit)}
                      </TableCell>
                      <TableCell>
                        <Badge variant={dealer.isActive ? 'default' : 'secondary'}>
                          {dealer.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewDealer(dealer.id)}
                            title="View Details"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditDealer(dealer.id)}
                            title="Edit Dealer"
                          >
                            <EditIcon className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteDealer(dealer)}
                            title="Delete Dealer"
                            className="text-red-600 hover:text-red-700"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
