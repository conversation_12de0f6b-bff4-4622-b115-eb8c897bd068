import { createClient } from '@libsql/client';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Database client
const client = createClient({
  url: process.env.TURSO_DATABASE_URL as string,
  authToken: process.env.TURSO_AUTH_TOKEN as string,
});

async function fixMigrations() {
  try {
    console.log('Checking if __drizzle_migrations table exists...');
    
    // Check if the __drizzle_migrations table exists
    const tableExists = await client.execute(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='__drizzle_migrations';
    `);
    
    if (tableExists.rows.length === 0) {
      console.log('Creating __drizzle_migrations table...');
      
      // Create the migrations table if it doesn't exist
      await client.execute(`
        CREATE TABLE __drizzle_migrations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          hash TEXT NOT NULL,
          created_at NUMERIC
        );
      `);
    }
    
    // Check if migrations are already recorded
    const existingMigrations = await client.execute(`
      SELECT hash FROM __drizzle_migrations;
    `);
    
    const existingHashes = existingMigrations.rows.map(row => row.hash);
    console.log('Existing migrations:', existingHashes);
    
    // Define the migrations we want to mark as applied
    const migrations = [
      { hash: '113a349d-86c7-43fd-b88a-cdfc3eaa953d', name: '0000_glossy_scarlet_spider' },
      { hash: '9a9df66d-6f11-450b-b43a-a1727d0586e7', name: '0001_tricky_supreme_intelligence' }
    ];
    
    // Insert migrations that don't already exist
    for (const migration of migrations) {
      if (!existingHashes.includes(migration.hash)) {
        console.log(`Marking migration ${migration.name} as applied...`);
        
        await client.execute({
          sql: `INSERT INTO __drizzle_migrations (hash, created_at) VALUES (?, ?)`,
          args: [migration.hash, Date.now()]
        });
      } else {
        console.log(`Migration ${migration.name} already marked as applied.`);
      }
    }
    
    console.log('Migration fix completed successfully!');
  } catch (error) {
    console.error('Error fixing migrations:', error);
  } finally {
    // Close the client connection
    await client.close();
  }
}

// Run the function
fixMigrations();
