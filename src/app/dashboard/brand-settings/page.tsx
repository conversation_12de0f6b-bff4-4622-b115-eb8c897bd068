import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { auth } from "../../../../auth";
import { db } from "@/lib/db";
import { tenants, users } from "@/db/schema";
import { eq } from "drizzle-orm";
import { redirect } from "next/navigation";
import BrandSettingsForm from "./BrandSettingsForm";

export default async function BrandSettingsPage() {
    const session = await auth();
    if (!session?.user?.id) {
        return <p>Please log in to view brand settings.</p>;
    }

    // Fetch user and tenant data
    const userData = await db.query.users.findFirst({
        where: eq(users.id, session.user.id),
    });

    const tenantData = await db.query.tenants.findFirst({
        where: eq(tenants.userId, session.user.id),
    });

    if (!userData || !tenantData) {
        return <p>Could not load user or tenant data.</p>;
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle>Brand Settings</CardTitle>
                <CardDescription>
                    Manage your business name, contact info, and address. This information will appear in your memo header.
                </CardDescription>
            </CardHeader>
            <CardContent>
                <BrandSettingsForm
                    tenantId={tenantData.id}
                    companyName={tenantData.companyName}
                    phone={userData.phone}
                    address={userData.address}
                />
            </CardContent>
        </Card>
    );
}
