import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { tenants, tenantUsers, users } from '@/db/schema';
import { auth } from '~/auth';
import { v4 as uuidv4 } from 'uuid';
import { and, eq } from 'drizzle-orm';
import { createMainBranchForTenant } from '@/lib/branch-utils';

// GET /api/tenants - List all tenants (admin only)
export async function GET(request: Request) {
  try {
    // Check authentication and authorization
    const session = await auth();

    // Only admin can access this endpoint
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Get all users with tenant role
    const tenantUsers = await db.query.users.findMany({
      where: eq(users.role, 'tenant'),
      with: {
        tenantOwned: true
      }
    });

    // Process users and add subscription info
    const usersWithSubscriptionInfo = tenantUsers.map(user => {
      const now = new Date();
      const tenant = user.tenantOwned[0]; // Get the first tenant owned by the user

      // Only process subscription info if tenant exists
      let subscriptionInfo = null;
      if (tenant) {
        const isActive = tenant.isActive &&
          (tenant.subscriptionEndDate ?
            new Date(tenant.subscriptionEndDate) > now :
            false);

        const daysRemaining = tenant.subscriptionEndDate ?
          Math.max(0, Math.ceil((new Date(tenant.subscriptionEndDate).getTime() - now.getTime()) /
            (1000 * 60 * 60 * 24))) :
          0;

        subscriptionInfo = {
          plan: tenant.subscriptionType,
          startDate: tenant.subscriptionStartDate,
          endDate: tenant.subscriptionEndDate,
          isActive,
          daysRemaining,
          paymentStatus: tenant.paymentStatus,
          lastPaymentDate: tenant.lastPaymentDate,
          paymentDueDate: tenant.paymentDueDate
        };
      }

      return {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.fullName,
        phone: user.phone,
        address: user.address,
        role: user.role,
        isActive: user.isActive,
        lastLogin: user.lastLogin,
        tenant: tenant ? {
          id: tenant.id,
          companyName: tenant.companyName,
          domain: tenant.domain,
          isActive: tenant.isActive,
          subscription: subscriptionInfo
        } : null
      };
    });

    return NextResponse.json({
      success: true,
      users: usersWithSubscriptionInfo
    });
  } catch (error) {
    console.error('Error fetching tenant users with subscription:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch tenant users with subscription details' },
      { status: 500 }
    );
  }
}

// POST /api/tenants - Create a new tenant (admin only)
export async function POST(request: Request) {
  try {
    // Check authentication and authorization
    const session = await auth();
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Parse request body
    const {
      userId,
      companyName,
      domain = null,
      subscriptionType = 'free-trial',
      isActive = true
    } = await request.json();

    // Validate required fields
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: userId is required' },
        { status: 400 }
      );
    }

    // Validate subscription type
    if (subscriptionType !== 'free-trial' && subscriptionType !== 'paid') {
      return NextResponse.json(
        { success: false, error: 'Invalid subscription type. Must be "free-trial" or "paid"' },
        { status: 400 }
      );
    }

    // Check if user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId)
    });

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if tenant already exists for this user
    const existingTenant = await db.query.tenants.findFirst({
      where: eq(tenants.userId, userId)
    });

    if (existingTenant) {
      return NextResponse.json(
        { success: false, error: 'User already has an associated tenant account' },
        { status: 400 }
      );
    }

    // Check if domain is unique if provided
    if (domain) {
      const domainExists = await db.query.tenants.findFirst({
        where: eq(tenants.domain, domain)
      });

      if (domainExists) {
        return NextResponse.json(
          { success: false, error: 'Domain is already in use' },
          { status: 400 }
        );
      }
    }

    // Calculate subscription dates
    const startDate = new Date();
    const endDate = new Date(startDate);
    let paymentStatus: 'pending' | 'paid' | 'overdue' = 'pending';
    let paymentDueDate = null;

    // Set subscription end date and payment info
    if (subscriptionType === 'free-trial') {
      // Free trial is 7 days
      endDate.setDate(endDate.getDate() + 7);
    } else {
      // Paid subscription is 30 days by default
      endDate.setDate(endDate.getDate() + 30);
      paymentDueDate = new Date(endDate);
    }

    // Generate tenant ID
    const tenantId = uuidv4();

    // Create new tenant
    await db.insert(tenants).values({
      id: tenantId,
      userId,
      companyName,
      domain,
      isActive,
      subscriptionType,
      subscriptionStartDate: startDate,
      subscriptionEndDate: endDate,
      paymentStatus,
      paymentDueDate,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Create tenant-user relationship with owner role
    await db.insert(tenantUsers).values({
      id: uuidv4(),
      tenantId,
      userId,
      role: 'owner',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Update user role if not already a tenant
    if (user.role !== 'tenant') {
      await db.update(users)
        .set({
          role: 'tenant',
          updatedAt: new Date()
        })
        .where(eq(users.id, userId));
    }

    // Create a main branch for the tenant
    const mainBranch = await createMainBranchForTenant(userId, companyName);

    if (!mainBranch) {
      console.error('Failed to create main branch for tenant:', userId);
    } else {
      console.log('Created main branch for tenant:', mainBranch.id);
    }

    // Get the created tenant with owner details
    const createdTenant = await db.query.tenants.findFirst({
      where: eq(tenants.id, tenantId),
      with: {
        owner: {
          columns: {
            id: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      tenant: createdTenant,
      mainBranch: mainBranch
    });
  } catch (error) {
    console.error('Error creating tenant:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create tenant' },
      { status: 500 }
    );
  }
}