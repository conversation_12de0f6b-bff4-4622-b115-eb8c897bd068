import { NextRequest, NextResponse } from "next/server";
import { auth } from "../../../../../auth";
import { db } from "@/lib/db";
import { branches, branchUsers } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { branchId } = body;

    if (!branchId) {
      return NextResponse.json(
        { success: false, error: "Branch ID is required" },
        { status: 400 }
      );
    }

    // Check if branch exists and belongs to user's tenant
    const branch = await db
      .select()
      .from(branches)
      .where(
        and(
          eq(branches.id, branchId),
          eq(branches.tenantId, session.user.tenantId || "")
        )
      )
      .limit(1);

    if (branch.length === 0) {
      return NextResponse.json(
        { success: false, error: "Branch not found or access denied" },
        { status: 404 }
      );
    }

    // Check if user is already associated with this branch
    const existingBranchUser = await db
      .select()
      .from(branchUsers)
      .where(
        and(
          eq(branchUsers.branchId, branchId),
          eq(branchUsers.userId, session.user.id)
        )
      )
      .limit(1);

    // If not, create a branch user association
    if (existingBranchUser.length === 0) {
      await db.insert(branchUsers).values({
        id: uuidv4(),
        branchId,
        userId: session.user.id,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    // Store branch ID in session - we'll assume the auth system can handle this
    // In a real implementation, you'd need to update the session or cookie with this info

    return NextResponse.json({
      success: true,
      message: "Branch selected successfully",
      branch: branch[0],
    });
  } catch (error) {
    console.error("Error selecting branch:", error);
    return NextResponse.json(
      { success: false, error: "Failed to select branch" },
      { status: 500 }
    );
  }
}
