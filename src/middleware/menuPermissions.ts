import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../auth';
import { pathRequiresPermission, getMenuByPath } from '../lib/menu-list';

// Middleware to check if tenant_sale user has permission to access a menu
export async function checkMenuPermission(req: NextRequest, menuPath: string) {
  console.log('Checking menu permission for path:', menuPath);
  try {
    const session = await auth();

    // No session means no access
    if (!session?.user) {
      return {
        success: false,
        error: 'Authentication required',
        status: 401
      };
    }

    // Check if the path requires permission
    const requiresPermission = pathRequiresPermission(menuPath);

    // If path doesn't require permission (like dashboard) or is not a menu path
    if (!requiresPermission) {
      console.log(`Path ${menuPath} doesn't require permission check`);
      return {
        success: true,
        permissions: [
          {
            canView: true,
            canCreate: true,
            canEdit: true,
            canDelete: true,
            menu: { path: menuPath }
          }
        ]
      };
    }

    // Admin and tenant users bypass menu permission check
    if (session.user.role === 'admin' || session.user.role === 'tenant') {
      // Return a permissions object with all permissions granted
      console.log(`Granting full permissions to ${session.user.role} user for path:`, menuPath);
      return {
        success: true,
        permissions: [
          {
            canView: true,
            canCreate: true,
            canEdit: true,
            canDelete: true,
            menu: { path: menuPath }
          }
        ]
      };
    }

    // For tenant_sale users, check permissions from session
    if (session.user.role === 'tenant_sale') {
      console.log(`Checking permissions for tenant_sale user for path: ${menuPath}`);

      // If no menu permissions in session, deny access
      if (!session.user.menuPermissions || session.user.menuPermissions.length === 0) {
        console.log(`No menu permissions found in session for tenant_sale user`);
        return {
          success: false,
          error: 'You do not have permission to access this menu',
          status: 403
        };
      }

      // Find the menu permission for this path
      const menuPermission = session.user.menuPermissions.find(
        (p: any) => p.path === menuPath && p.canView
      );

      if (!menuPermission) {
        console.log(`No permission found for path ${menuPath}`);
        return {
          success: false,
          error: 'You do not have permission to access this menu',
          status: 403
        };
      }

      // Return the user's permissions for this menu
      return {
        success: true,
        permissions: [
          {
            canView: menuPermission.canView,
            canCreate: menuPermission.canCreate,
            canEdit: menuPermission.canEdit,
            canDelete: menuPermission.canDelete,
            menu: { path: menuPath }
          }
        ]
      };
    }

    // Other roles don't have menu access
    return {
      success: false,
      error: 'Role not authorized for menu access',
      status: 403
    };
  } catch (error) {
    console.error('Menu permission check error:', error);
    return {
      success: false,
      error: 'Error checking menu permission',
      status: 500
    };
  }
}

// Route handler wrapper for menu permission check
export function withMenuPermission(handler: Function, menuPath: string) {
  return async (req: NextRequest, context: any) => {
    console.log(`withMenuPermission wrapper for path: ${menuPath}`);
    const permissionCheck = await checkMenuPermission(req, menuPath);

    if (!permissionCheck.success) {
      console.log(`Permission check failed for ${menuPath}:`, permissionCheck.error);
      return NextResponse.json(
        { success: false, error: permissionCheck.error },
        { status: permissionCheck.status }
      );
    }

    console.log(`Permission check passed for ${menuPath}, calling handler`);
    // Pass permission check result to the handler
    return handler(req, context, permissionCheck);
  };
}
