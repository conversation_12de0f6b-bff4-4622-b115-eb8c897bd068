import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { users } from '@/db/schema';
import { auth } from '~/auth';
import { eq, and } from 'drizzle-orm';

// GET /api/users - Get users, optionally filtered by role
export async function GET(request: Request) {
  try {
    // Check authentication
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const url = new URL(request.url);
    const roleParam = url.searchParams.get('role');
    
    let usersList;
    
    // If role is specified, filter users by that role
    if (roleParam) {
      usersList = await db.query.users.findMany({
        where: and(
          eq(users.role, roleParam as any),
          eq(users.isActive, true)
        ),
      });
    } else {
      // Otherwise fetch all users
      usersList = await db.query.users.findMany({
        where: eq(users.isActive, true),
      });
    }

    return NextResponse.json({
      success: true,
      users: usersList
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
} 