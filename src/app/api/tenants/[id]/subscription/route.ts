import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { tenants } from "@/db/schema";
import { auth } from "~/auth";
import { eq } from "drizzle-orm";

// GET /api/tenants/[id]/subscription - Get subscription details
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get tenant details - use tenant ID directly from the params
    const tenant = await db.query.tenants.findFirst({
      where: eq(tenants.userId, (await params).id),
    });

    if (!tenant) {
      return NextResponse.json(
        { success: false, error: "Tenant not found" },
        { status: 404 }
      );
    }

    // Check if user has access to this tenant
    if (session.user.role !== "admin" && tenant.userId !== session.user.id) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 403 }
      );
    }

    const now = new Date();
    const subscriptionEndDate = tenant.subscriptionEndDate
      ? new Date(tenant.subscriptionEndDate)
      : new Date();
    const isActive = tenant.isActive && subscriptionEndDate > now;
    const daysRemaining = Math.max(
      0,
      Math.ceil(
        (subscriptionEndDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
      )
    );

    return NextResponse.json({
      success: true,
      subscription: {
        plan: tenant.subscriptionType,
        startDate: tenant.subscriptionStartDate,
        endDate: tenant.subscriptionEndDate,
        isActive,
        daysRemaining,
        paymentStatus: tenant.paymentStatus,
        lastPaymentDate: tenant.lastPaymentDate,
        paymentDueDate: tenant.paymentDueDate,
      },
    });
  } catch (error) {
    console.error("Error fetching subscription details:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch subscription details" },
      { status: 500 }
    );
  }
}

// PATCH /api/tenants/[id]/subscription - Update subscription
export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user || session.user.role !== "admin") {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { subscriptionType, paymentStatus, subscriptionEndDate } = body;

    // Get tenant details
    const tenant = await db.query.tenants.findFirst({
      where: eq(tenants.userId, (await params).id),
    });

    if (!tenant) {
      return NextResponse.json(
        { success: false, error: "Tenant not found" },
        { status: 404 }
      );
    }

    // Update subscription details
    const updates: any = {};

    if (subscriptionType) {
      if (subscriptionType !== "free-trial" && subscriptionType !== "paid") {
        return NextResponse.json(
          { success: false, error: "Invalid subscription type" },
          { status: 400 }
        );
      }
      updates.subscriptionType = subscriptionType;

      // Update subscription dates based on type
      const now = new Date();
      updates.subscriptionStartDate = now;

      if (subscriptionType === "free-trial") {
        const endDate = new Date(now);
        endDate.setDate(endDate.getDate() + 7); // 7-day free trial
        updates.subscriptionEndDate = endDate;
        updates.paymentStatus = "pending";
      } else {
        const endDate = new Date(now);
        endDate.setMonth(endDate.getMonth() + 1); // 1-month paid subscription
        updates.subscriptionEndDate = endDate;
        updates.paymentDueDate = endDate;
      }
    }

    // Handle subscription extension with a specific end date
    if (subscriptionEndDate) {
      const newEndDate = new Date(subscriptionEndDate);
      const now = new Date();

      // Validate that the new end date is in the future
      if (newEndDate <= now) {
        return NextResponse.json(
          { success: false, error: "End date must be in the future" },
          { status: 400 }
        );
      }

      updates.subscriptionEndDate = newEndDate;
    }

    if (paymentStatus) {
      if (!["pending", "paid", "overdue"].includes(paymentStatus)) {
        return NextResponse.json(
          { success: false, error: "Invalid payment status" },
          { status: 400 }
        );
      }

      updates.paymentStatus = paymentStatus;

      if (paymentStatus === "paid") {
        updates.lastPaymentDate = new Date();

        // Update payment due date
        if (tenant.subscriptionType === "paid") {
          const dueDate = new Date();
          dueDate.setMonth(dueDate.getMonth() + 1);
          updates.paymentDueDate = dueDate;
        }
      }
    }

    // Ensure tenant is active when updating subscription
    updates.isActive = true;

    // Update tenant
    await db
      .update(tenants)
      .set({
        ...updates,
        updatedAt: new Date(),
      })
      .where(eq(tenants.userId, (await params).id));

    // Get updated tenant for response
    const updatedTenant = await db.query.tenants.findFirst({
      where: eq(tenants.userId, (await params).id),
    });

    if (!updatedTenant) {
      return NextResponse.json(
        { success: false, error: "Failed to retrieve updated tenant" },
        { status: 500 }
      );
    }

    // Calculate subscription information for response
    const now = new Date();
    const newSubscriptionEndDate = updatedTenant.subscriptionEndDate
      ? new Date(updatedTenant.subscriptionEndDate)
      : now;
    const isActive = updatedTenant.isActive && newSubscriptionEndDate > now;
    const daysRemaining = Math.max(
      0,
      Math.ceil(
        (newSubscriptionEndDate.getTime() - now.getTime()) /
          (1000 * 60 * 60 * 24)
      )
    );

    return NextResponse.json({
      success: true,
      subscription: {
        plan: updatedTenant.subscriptionType,
        startDate: updatedTenant.subscriptionStartDate,
        endDate: updatedTenant.subscriptionEndDate,
        isActive,
        daysRemaining,
        paymentStatus: updatedTenant.paymentStatus,
        lastPaymentDate: updatedTenant.lastPaymentDate,
        paymentDueDate: updatedTenant.paymentDueDate,
      },
    });
  } catch (error) {
    console.error("Error updating subscription:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update subscription" },
      { status: 500 }
    );
  }
}
