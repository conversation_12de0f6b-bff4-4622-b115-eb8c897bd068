import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { expenseCategories } from "@/db/schema";
import { auth } from "~/auth";
import { v4 as uuidv4 } from "uuid";
import { eq, and, desc } from "drizzle-orm";
import { withMenuPermission } from "@/middleware/menuPermissions";

// GET handler for expense categories
async function getHandler(request: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get tenant ID from session
    const tenantId = session.user.role === 'tenant'
      ? session.user.id
      : session.user.tenantId;

    if (!tenantId) {
      return NextResponse.json(
        { success: false, error: "Tenant ID not found" },
        { status: 400 }
      );
    }

    // Check if specific category ID is requested
    const url = new URL(request.url);
    const categoryId = url.searchParams.get('id');

    if (categoryId) {
      // Get specific category
      const category = await db.query.expenseCategories.findFirst({
        where: and(
          eq(expenseCategories.id, categoryId),
          eq(expenseCategories.tenantId, tenantId)
        )
      });

      if (!category) {
        return NextResponse.json(
          { success: false, error: "Expense category not found" },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        category
      });
    }

    // Get all categories for the tenant
    const categories = await db.query.expenseCategories.findMany({
      where: eq(expenseCategories.tenantId, tenantId),
      orderBy: [desc(expenseCategories.createdAt)]
    });

    return NextResponse.json({
      success: true,
      categories
    });
  } catch (error) {
    console.error("Error fetching expense categories:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch expense categories" },
      { status: 500 }
    );
  }
}

// POST handler for creating a new expense category
async function postHandler(request: NextRequest, context: any, permissionCheck: any) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Check if tenant_sale user has permission to create
    if (session.user.role === 'tenant_sale') {
      const menuPermission = permissionCheck.permissions.find(
        (p: any) => p.menu.path === '/dashboard/expenses/categories'
      );

      if (!menuPermission?.canCreate) {
        return NextResponse.json(
          { success: false, error: "You do not have permission to create expense categories" },
          { status: 403 }
        );
      }
    }

    // Get tenant ID from session
    const tenantId = session.user.role === 'tenant'
      ? session.user.id
      : session.user.tenantId;

    if (!tenantId) {
      return NextResponse.json(
        { success: false, error: "Tenant ID not found" },
        { status: 400 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { name, description } = body;

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { success: false, error: "Category name is required" },
        { status: 400 }
      );
    }

    // Create new expense category
    const newCategory = {
      id: uuidv4(),
      tenantId,
      name,
      description: description || null,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await db.insert(expenseCategories).values(newCategory);

    return NextResponse.json({
      success: true,
      message: "Expense category created successfully",
      category: newCategory
    });
  } catch (error) {
    console.error("Error creating expense category:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create expense category" },
      { status: 500 }
    );
  }
}

// PUT handler for updating an existing expense category
async function putHandler(request: NextRequest, context: any, permissionCheck: any) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Check if tenant_sale user has permission to edit
    if (session.user.role === 'tenant_sale') {
      const menuPermission = permissionCheck.permissions.find(
        (p: any) => p.menu.path === '/dashboard/expenses/categories'
      );

      if (!menuPermission?.canEdit) {
        return NextResponse.json(
          { success: false, error: "You do not have permission to update expense categories" },
          { status: 403 }
        );
      }
    }

    // Get tenant ID from session
    const tenantId = session.user.role === 'tenant'
      ? session.user.id
      : session.user.tenantId;

    if (!tenantId) {
      return NextResponse.json(
        { success: false, error: "Tenant ID not found" },
        { status: 400 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { id, name, description, isActive } = body;

    // Validate required fields
    if (!id || !name) {
      return NextResponse.json(
        { success: false, error: "Category ID and name are required" },
        { status: 400 }
      );
    }

    // Check if category exists and belongs to the tenant
    const existingCategory = await db.query.expenseCategories.findFirst({
      where: and(
        eq(expenseCategories.id, id),
        eq(expenseCategories.tenantId, tenantId)
      )
    });

    if (!existingCategory) {
      return NextResponse.json(
        { success: false, error: "Expense category not found or you don't have permission to update it" },
        { status: 404 }
      );
    }

    // Update category
    await db.update(expenseCategories)
      .set({
        name,
        description: description || null,
        isActive: isActive !== undefined ? isActive : existingCategory.isActive,
        updatedAt: new Date()
      })
      .where(eq(expenseCategories.id, id));

    return NextResponse.json({
      success: true,
      message: "Expense category updated successfully"
    });
  } catch (error) {
    console.error("Error updating expense category:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update expense category" },
      { status: 500 }
    );
  }
}

// DELETE handler for deleting an expense category
async function deleteHandler(request: NextRequest, context: any, permissionCheck: any) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Check if tenant_sale user has permission to delete
    if (session.user.role === 'tenant_sale') {
      const menuPermission = permissionCheck.permissions.find(
        (p: any) => p.menu.path === '/dashboard/expenses/categories'
      );

      if (!menuPermission?.canDelete) {
        return NextResponse.json(
          { success: false, error: "You do not have permission to delete expense categories" },
          { status: 403 }
        );
      }
    }

    // Get tenant ID from session
    const tenantId = session.user.role === 'tenant'
      ? session.user.id
      : session.user.tenantId;

    if (!tenantId) {
      return NextResponse.json(
        { success: false, error: "Tenant ID not found" },
        { status: 400 }
      );
    }

    // Get category ID from URL
    const url = new URL(request.url);
    const categoryId = url.searchParams.get('id');

    if (!categoryId) {
      return NextResponse.json(
        { success: false, error: "Category ID is required" },
        { status: 400 }
      );
    }

    // Check if category exists and belongs to the tenant
    const existingCategory = await db.query.expenseCategories.findFirst({
      where: and(
        eq(expenseCategories.id, categoryId),
        eq(expenseCategories.tenantId, tenantId)
      )
    });

    if (!existingCategory) {
      return NextResponse.json(
        { success: false, error: "Expense category not found or you don't have permission to delete it" },
        { status: 404 }
      );
    }

    // Delete category
    await db.delete(expenseCategories)
      .where(eq(expenseCategories.id, categoryId));

    return NextResponse.json({
      success: true,
      message: "Expense category deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting expense category:", error);
    return NextResponse.json(
      { success: false, error: "Failed to delete expense category" },
      { status: 500 }
    );
  }
}

// Export the route handlers with permission middleware
export const GET = withMenuPermission(getHandler, '/dashboard/expenses/categories');
export const POST = withMenuPermission(postHandler, '/dashboard/expenses/categories');
export const PUT = withMenuPermission(putHandler, '/dashboard/expenses/categories');
export const DELETE = withMenuPermission(deleteHandler, '/dashboard/expenses/categories');
