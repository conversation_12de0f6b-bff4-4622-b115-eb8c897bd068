import { db } from '../../../../lib/db';
import { sessions } from '../../../../db/schema';
import { eq } from 'drizzle-orm';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    // Get session cookie
    const cookieHeader = request.headers.get('cookie');
    if (cookieHeader) {
      // Parse cookies
      const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=');
        acc[key] = value;
        return acc;
      }, {} as Record<string, string>);
      
      const sessionId = cookies['sessionId'];
      
      if (sessionId) {
        await db.delete(sessions).where(eq(sessions.id, sessionId));
      }
    }
    
    // Clear the cookie
    const response = NextResponse.json({ success: true });
    
    response.cookies.set({
      name: 'sessionId',
      value: '',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 0, // Expire immediately
      path: '/',
    });
    
    return response;
  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json({ success: false, error: 'Failed to logout' }, { status: 500 });
  }
}