import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { userMenuPermissions } from "@/db/schema";
import { auth } from "~/auth";
import { v4 as uuidv4 } from 'uuid';
import { eq, and } from "drizzle-orm";
import { ALL_MENUS } from "@/lib/menu-list";

// POST /api/debug/add-user-permission - Add permission for the current user to access the order reports menu
export async function POST(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Admin and tenant users already have full permissions
    if (session.user.role === 'admin' || session.user.role === 'tenant') {
      return NextResponse.json({
        success: true,
        message: `${session.user.role} users already have full permissions`
      });
    }

    // For tenant_sale users, add permission for the order reports menu
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      // Get the order reports menu from file
      const orderReportsMenu = ALL_MENUS.find(menu => menu.path === '/dashboard/reports/orders');

      if (!orderReportsMenu) {
        return NextResponse.json(
          { success: false, error: "Order reports menu not found in file-based menu definitions" },
          { status: 404 }
        );
      }

      // Check if the user already has permission for this menu
      const existingPermission = await db.query.userMenuPermissions.findFirst({
        where: and(
          eq(userMenuPermissions.userId, session.user.id),
          eq(userMenuPermissions.menuId, orderReportsMenu.id),
          eq(userMenuPermissions.tenantId, session.user.tenantId)
        )
      });

      if (existingPermission) {
        // Update the existing permission to ensure view access
        await db
          .update(userMenuPermissions)
          .set({
            canView: true,
            updatedAt: new Date()
          })
          .where(and(
            eq(userMenuPermissions.userId, session.user.id),
            eq(userMenuPermissions.menuId, orderReportsMenu.id),
            eq(userMenuPermissions.tenantId, session.user.tenantId)
          ));

        return NextResponse.json({
          success: true,
          message: "Permission updated successfully",
          permission: {
            ...existingPermission,
            canView: true
          }
        });
      }

      // Create a new permission
      const newPermission = {
        id: uuidv4(),
        userId: session.user.id,
        menuId: orderReportsMenu.id,
        tenantId: session.user.tenantId,
        canView: true,
        canCreate: false,
        canEdit: false,
        canDelete: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Insert the permission
      await db.insert(userMenuPermissions).values(newPermission);

      return NextResponse.json({
        success: true,
        message: "Permission added successfully",
        permission: newPermission
      });
    }

    // Default response for other roles
    return NextResponse.json({
      success: false,
      error: `Cannot add permissions for user role: ${session.user.role}`
    });
  } catch (error) {
    console.error("Error adding user permission:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to add user permission",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
