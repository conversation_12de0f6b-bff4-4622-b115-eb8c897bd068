import { NextRequest, NextResponse } from "next/server";
import { auth } from "../../../../../auth";
import { db } from "@/lib/db";
import { refunds } from "@/db/schema";
import { eq, and } from "drizzle-orm";

// GET /api/refunds/[id] - Get a specific refund by ID
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Get tenant ID from session based on user role
    let tenantId: string;
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      // For sales users, use their associated tenant's ID
      tenantId = session.user.tenantId;
    } else {
      // For tenant users, use their own ID
      tenantId = session.user.id;
    }

    // Get the refund with related data
    const refund = await db.query.refunds.findFirst({
      where: and(
        eq(refunds.id, (await params).id),
        eq(refunds.tenantId, tenantId)
      ),
      with: {
        customer: true,
        order: true,
        reason: true,
        branch: true,
        performer: true,
        items: {
          with: {
            product: true,
          },
        },
      },
    });

    if (!refund) {
      return NextResponse.json(
        { success: false, error: "Refund not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      refund,
    });
  } catch (error) {
    console.error("Error fetching refund:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch refund" },
      { status: 500 }
    );
  }
}
