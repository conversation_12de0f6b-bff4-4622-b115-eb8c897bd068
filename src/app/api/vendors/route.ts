import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { vendors, users } from "@/db/schema";
import { auth } from "~/auth";
import { eq, desc, sql, isNotNull, ilike, and } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

// GET /api/vendors - List vendors

export async function GET(request: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const tenantId = session.user.id;
    const { searchParams } = new URL(request.url);
    const vendorId = searchParams.get("id");
    const search = searchParams.get("search");

    // Case 1: Fetch a vendor by ID
    if (vendorId) {
      const vendor = await db.query.vendors.findFirst({
        where: and(
          eq(vendors.id, vendorId),
          eq(vendors.tenantId, tenantId)
        ),
        with: {
          user: {
            columns: {
              id: true,
              username: true,
              fullName: true,
              email: true,
              role: true,
            },
          },
        },
      });

      if (!vendor) {
        return NextResponse.json(
          { success: false, error: "Vendor not found" },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        vendor,
      });
    }

    // Case 2: Search vendors by name (partial match)
    if (search) {
      const vendorsList = await db.query.vendors.findMany({
        where: and(
          sql`${vendors.name} LIKE ${'%' + search + '%'}`,
          eq(vendors.tenantId, tenantId)
        ),
        orderBy: [desc(vendors.createdAt)],
        with: {
          user: {
            columns: {
              id: true,
              username: true,
              fullName: true,
              email: true,
              role: true,
            },
          },
        },
      });

      return NextResponse.json({
        success: true,
        vendors: vendorsList,
      });
    }

    // Case 3: No param – fetch all vendors
    const vendorsList = await db.query.vendors.findMany({
      where: eq(vendors.tenantId, tenantId),
      orderBy: [desc(vendors.createdAt)],
      with: {
        user: {
          columns: {
            id: true,
            username: true,
            fullName: true,
            email: true,
            role: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      vendors: vendorsList,
    });
  } catch (error) {
    console.error("Error fetching vendors:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch vendors" },
      { status: 500 }
    );
  }
}
// POST /api/vendors - Create a new vendor
export async function POST(request: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    console.log("Session user:", JSON.stringify(session.user, null, 2));

    const data = await request.json();
    let {
      name,
      userId = null,
      vendorCode = null,
      email = null,
      phone = null,
      address = null,
      isActive = true,
    } = data;

    if (!name) {
      return NextResponse.json(
        { success: false, error: "Name is required" },
        { status: 400 }
      );
    }

    const tenantId = session.user.id;
    console.log("tenantId from session:", tenantId);

    // Generate vendor code if not provided (for all users)
    if (!vendorCode) {
      // Get all existing vendor codes for this tenant
      const existingVendors = await db
        .select({ vendorCode: vendors.vendorCode })
        .from(vendors)
        .where(eq(vendors.tenantId, tenantId));

      // Extract all existing vendor codes
      const existingCodes = existingVendors
        .map(v => v.vendorCode)
        .filter(Boolean) // Remove null values
        .map(code => {
          // Extract the numeric part of the code (e.g., "0001" from "VN0001")
          const match = code?.match(/VN(\d+)/);
          return match ? parseInt(match[1], 10) : 0;
        });

      // Find the highest existing code number
      const highestCode = existingCodes.length > 0 ? Math.max(...existingCodes) : 0;

      // Generate a new code that's one higher than the highest existing code
      vendorCode = `VN${String(highestCode + 1).padStart(4, "0")}`;

      // Double-check that this code doesn't already exist (just to be safe)
      let codeExists = true;
      let attemptCount = 0;
      let codeNumber = highestCode + 1;

      while (codeExists && attemptCount < 100) { // Limit attempts to prevent infinite loop
        const checkCode = `VN${String(codeNumber).padStart(4, "0")}`;

        const existingVendorWithCode = await db
          .select({ id: vendors.id })
          .from(vendors)
          .where(and(
            eq(vendors.tenantId, tenantId),
            eq(vendors.vendorCode, checkCode)
          ));

        if (existingVendorWithCode.length === 0) {
          vendorCode = checkCode;
          codeExists = false;
        } else {
          codeNumber++;
          attemptCount++;
        }
      }
    }

    // Verify tenantId is valid in database
    const tenantExists = await db.query.users.findFirst({
      where: eq(users.id, tenantId),
    });

    if (!tenantExists) {
      console.error("Tenant not found:", tenantId);
      return NextResponse.json(
        {
          success: false,
          error: "Invalid tenant ID - not found in users table",
        },
        { status: 400 }
      );
    }

    console.log("Tenant confirmed to exist:", tenantExists.id);

    // Check if userId exists in the users table if provided
    if (userId) {
      const userExists = await db.query.users.findFirst({
        where: eq(users.id, userId),
      });

      if (!userExists) {
        return NextResponse.json(
          { success: false, error: "User ID does not exist" },
          { status: 400 }
        );
      }
      console.log("User confirmed to exist:", userExists.id);
    }

    const vendorId = uuidv4();

    // Create values object with explicit handling of null userId
    // Only include email and phone if user is admin
    const vendorValues = {
      id: vendorId,
      tenantId,
      name,
      vendorCode: vendorCode || null,
      email: session.user.role === "admin" ? (email || null) : null,
      phone: session.user.role === "admin" ? (phone || null) : null,
      isActive: isActive === true,
      address: address || null,
      userId: userId || null,
    };

    try {
      // Use direct database operations for more control
      await db.insert(vendors).values(vendorValues);

      return NextResponse.json({
        success: true,
        message: "Vendor created successfully",
        vendorId: vendorId,
      });
    } catch (dbError) {
      console.error("Database insertion error:", dbError);

      return NextResponse.json(
        {
          success: false,
          error: "Failed to create vendor",
          details: dbError instanceof Error ? dbError.message : String(dbError),
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error creating vendor:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to create vendor",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

// PUT /api/vendors - Update an existing vendor
export async function PUT(request: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const tenantId = session.user.id;
    const {
      id,
      name,
      userId = null,
      vendorCode = null,
      email = null,
      phone = null,
      address = null,
      isActive = true,
    } = await request.json();

    if (!id) {
      return NextResponse.json(
        { success: false, error: "Vendor ID is required" },
        { status: 400 }
      );
    }

    // Verify vendor exists and belongs to the tenant
    const existingVendor = await db.query.vendors.findFirst({
      where: sql`${vendors.id} = ${id} AND ${vendors.tenantId} = ${tenantId}`,
    });

    if (!existingVendor) {
      return NextResponse.json(
        { success: false, error: "Vendor not found or unauthorized" },
        { status: 404 }
      );
    }

    // Check if userId exists if provided
    if (userId) {
      const userExists = await db.query.users.findFirst({
        where: eq(users.id, userId),
      });

      if (!userExists) {
        return NextResponse.json(
          { success: false, error: "User ID does not exist" },
          { status: 400 }
        );
      }
    }

    // Create update values with same pattern as insert
    // Only include email and phone if user is admin
    const updateValues = {
      name,
      tenantId,
      vendorCode: vendorCode || null,
      email: session.user.role === "admin" ? (email || null) : existingVendor.email,
      phone: session.user.role === "admin" ? (phone || null) : existingVendor.phone,
      address: address || null,
      isActive: isActive === true,
      userId: userId || null,
    };

    console.log(
      "Updating vendor with data:",
      JSON.stringify(updateValues, null, 2)
    );

    try {
      // Use direct database operations for more control
      await db
        .update(vendors)
        .set(updateValues)
        .where(eq(vendors.id, id));

      return NextResponse.json({
        success: true,
        message: "Vendor updated successfully",
      });
    } catch (dbError) {
      console.error("Database update error:", dbError);

      // Try to get more detailed error information
      if (dbError instanceof Error) {
        console.error("Error name:", dbError.name);
        console.error("Error message:", dbError.message);
        console.error("Error stack:", dbError.stack);
      }

      return NextResponse.json(
        {
          success: false,
          error: "Failed to update vendor",
          details: dbError instanceof Error ? dbError.message : String(dbError),
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error updating vendor:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to update vendor",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

// DELETE /api/vendors - Delete a vendor
export async function DELETE(request: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const tenantId = session.user.id;
    const { searchParams } = new URL(request.url);
    const vendorId = searchParams.get("id");

    if (!vendorId) {
      return NextResponse.json(
        { success: false, error: "Vendor ID is required" },
        { status: 400 }
      );
    }

    // Check if the vendor exists and belongs to the tenant
    const existingVendor = await db.query.vendors.findFirst({
      where: sql`${vendors.id} = ${vendorId} AND ${vendors.tenantId} = ${tenantId}`,
    });

    if (!existingVendor) {
      return NextResponse.json(
        { success: false, error: "Vendor not found" },
        { status: 404 }
      );
    }

    // Delete the vendor
    await db
      .delete(vendors)
      .where(
        sql`${vendors.id} = ${vendorId} AND ${vendors.tenantId} = ${tenantId}`
      );

    return NextResponse.json({
      success: true,
      message: "Vendor deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting vendor:", error);
    return NextResponse.json(
      { success: false, error: "Failed to delete vendor" },
      { status: 500 }
    );
  }
}
