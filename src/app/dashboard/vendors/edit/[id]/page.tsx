'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/components/ui/use-toast';

interface Vendor {
    id: string;
    name: string;
    email: string | null;
    phone: string | null;
    address: string | null;
    isActive: boolean;
    vendorCode: string | null;
    notes: string | null;
    createdAt: string;
    updatedAt: string;
    userId: string;
    tenantId: string | null;
    user?: {
        id: string;
        username: string;
        fullName: string;
        email: string;
    };
}

export default function VendorEditPage() {
    const router = useRouter();
    const params = useParams();
    const { toast } = useToast();
    const [vendor, setVendor] = useState<Vendor | null>(null);
    const [loading, setLoading] = useState(true);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState('');
    const [userRole, setUserRole] = useState<string | null>(null);

    // Form fields
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');
    const [phone, setPhone] = useState('');
    const [address, setAddress] = useState('');
    const [notes, setNotes] = useState('');
    const [isActive, setIsActive] = useState(true);

    useEffect(() => {
        fetchUserRole();
        fetchVendor();
    }, []);

    const fetchUserRole = async () => {
        try {
            const response = await fetch('/api/auth/me');
            if (response.ok) {
                const data = await response.json();
                setUserRole(data.user?.role || null);
            }
        } catch (error) {
            console.error('Error fetching user role:', error);
        }
    };
    const fetchVendor = async () => {
        if (!params || !params.id) return;

        try {
            setLoading(true);
            const response = await fetch(`/api/vendors?id=${params.id}`);

            if (!response.ok) {
                throw new Error('Failed to fetch vendor details');
            }

            const data = await response.json();

            if (data.success) {
                setVendor(data.vendor);

                // Initialize form fields
                setName(data.vendor.name || '');
                setEmail(data.vendor.email || '');
                setPhone(data.vendor.phone || '');
                setAddress(data.vendor.address || '');
                setNotes(data.vendor.notes || '');
                setIsActive(data.vendor.isActive);
            } else {
                setError(data.error || 'Failed to load vendor details');
            }
        } catch (err) {
            setError('An error occurred while fetching vendor details');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!name.trim()) {
            toast({
                title: "Validation Error",
                description: "Name is required",
                variant: "destructive",
            });
            return;
        }

        setSubmitting(true);

        try {
            const response = await fetch('/api/vendors', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: params?.id,
                    name,
                    email: email || null,
                    phone: phone || null,
                    address: address || null,
                    notes: notes || null,
                    isActive
                }),
            });

            const data = await response.json();

            if (data.success) {
                toast({
                    title: "Success",
                    description: "Vendor updated successfully",
                });
                router.push(`/dashboard/vendors/${params?.id}`);
            } else {
                toast({
                    title: "Error",
                    description: data.error || "Failed to update vendor",
                    variant: "destructive",
                });
            }
        } catch (error) {
            console.error('Error updating vendor:', error);
            toast({
                title: "Error",
                description: "An unexpected error occurred",
                variant: "destructive",
            });
        } finally {
            setSubmitting(false);
        }
    };

    const handleCancel = () => {
        router.push(`/dashboard/vendors/${params?.id}`);
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">Error: </strong>
                    <span className="block sm:inline">{error}</span>
                </div>
                <Button onClick={() => router.push('/dashboard/vendors')} variant="outline" className='cursor-pointer'>Back to Vendors</Button>
            </div>
        );
    }

    if (!vendor) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">Notice: </strong>
                    <span className="block sm:inline">Vendor not found</span>
                </div>
                <Button onClick={() => router.push('/dashboard/vendors')} variant="outline" className='cursor-pointer'>Back to Vendors</Button>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold">Edit Vendor</h1>
                <Button onClick={() => router.push('/dashboard/vendors')} variant="outline" className='cursor-pointer'>
                    Back to Vendors
                </Button>
            </div>

            <form onSubmit={handleSubmit}>
                <div className="grid grid-cols-1 gap-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>General Information</CardTitle>
                            <CardDescription>Basic details about the vendor</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Name *</Label>
                                    <Input
                                        id="name"
                                        value={name}
                                        onChange={(e) => setName(e.target.value)}
                                        placeholder="Enter vendor name"
                                        required
                                    />
                                </div>
                                <div className="space-y-2 flex items-center">
                                    <div className="flex items-center space-x-2">
                                        <Switch
                                            id="isActive"
                                            checked={isActive}
                                            onCheckedChange={setIsActive}
                                        />
                                        <Label htmlFor="isActive">Active</Label>
                                    </div>
                                </div>
                            </div>

                            {userRole === 'admin' && (
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="email">Email</Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={email}
                                            onChange={(e) => setEmail(e.target.value)}
                                            placeholder="Enter vendor email"
                                        />
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="phone">Phone Number</Label>
                                        <Input
                                            id="phone"
                                            value={phone}
                                            onChange={(e) => setPhone(e.target.value)}
                                            placeholder="Enter vendor phone number"
                                        />
                                    </div>
                                </div>
                            )}

                            <div className="space-y-2">
                                <Label htmlFor="address">Address</Label>
                                <Textarea
                                    id="address"
                                    value={address}
                                    onChange={(e) => setAddress(e.target.value)}
                                    placeholder="Enter vendor address"
                                    rows={3}
                                />
                            </div>
                        </CardContent>
                    </Card>

                    {/* <Card>
                        <CardHeader>
                            <CardTitle>Additional Information</CardTitle>
                            <CardDescription>Other details about this vendor</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2">
                                <Label htmlFor="notes">Notes</Label>
                                <Textarea
                                    id="notes"
                                    value={notes}
                                    onChange={(e) => setNotes(e.target.value)}
                                    placeholder="Enter any additional notes"
                                    rows={4}
                                />
                            </div>
                        </CardContent>
                    </Card> */}

                    <Card>
                        <CardHeader>
                            <CardTitle>System Information</CardTitle>
                            <CardDescription>Information managed by the system</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label className="text-sm font-medium text-gray-500 dark:text-gray-400">Vendor Code</Label>
                                    <p className="mt-1">{vendor.vendorCode || 'Not assigned'}</p>
                                </div>
                                {vendor.user ? (
                                    <div>
                                        <Label className="text-sm font-medium text-gray-500 dark:text-gray-400">Associated User</Label>
                                        <p className="mt-1">{vendor.user.fullName} ({vendor.user.email})</p>
                                    </div>
                                ) : (
                                    <div>
                                        <Label className="text-sm font-medium text-gray-500 dark:text-gray-400">Associated User</Label>
                                        <p className="mt-1">No user associated</p>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    <div className="flex justify-end space-x-2">
                        <Button variant="outline" onClick={handleCancel} type="button" className='cursor-pointer'>
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            disabled={submitting}
                            className="bg-blue-600 hover:bg-blue-700 text-white cursor-pointer"
                        >
                            {submitting ? 'Saving...' : 'Save Changes'}
                        </Button>
                    </div>
                </div>
            </form>
        </div>
    );
}