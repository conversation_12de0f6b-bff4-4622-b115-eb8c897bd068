{"name": "hisabmanager", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "analyze": "node scripts/analyze-bundle.js", "db:generate": "drizzle-kit generate --config ./drizzle.config.ts", "db:migrate": "drizzle-kit migrate --config ./drizzle.config.ts", "db:studio": "drizzle-kit studio --config ./drizzle.config.ts", "db:push": "drizzle-kit push --config ./drizzle.config.ts", "db:reset": "rm -rf ./migrations", "db:update-menu-schema": "npx tsx src/scripts/update-menu-permissions-schema.ts", "seed:menus": "npx tsx src/scripts/seed-menus.ts", "seed:menus:help": "node src/scripts/seed-menus-helper.js", "seed:refund-reasons": "npx tsx src/scripts/seed-refund-reasons.ts"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@libsql/client": "^0.14.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "better-sqlite3": "^11.9.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "drizzle-orm": "^0.40.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.483.0", "next": "15.2.1", "next-auth": "^5.0.0-beta.25", "number-to-words-in-bengali": "^1.0.4", "react": "^19.0.0", "react-day-picker": "^9.6.3", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.4", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.1", "@swc/register": "^0.1.10", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.5.0", "drizzle-kit": "^0.30.5", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}}