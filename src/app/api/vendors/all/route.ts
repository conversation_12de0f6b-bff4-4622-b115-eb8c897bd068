import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { vendors, users } from "@/db/schema";
import { auth } from "~/auth";
import { eq, desc, sql } from "drizzle-orm";

// GET /api/vendors/all - Get all vendors for the current tenant
export async function GET() {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the tenant ID from the session
    // For tenant users, the tenantId is their own user ID
    // For tenant_sale users, the tenantId is the ID of the tenant they belong to
    const tenantId = session.user.tenantId || session.user.id;

    console.log("Session user:", JSON.stringify(session.user, null, 2));
    console.log("Fetching all vendors for tenantId:", tenantId);

    // First check if any vendors exist in the database at all
    const allVendorsCount = await db
      .select({ count: sql`count(*)` })
      .from(vendors);
    
    console.log("Total vendors in database:", allVendorsCount[0]?.count);

    // Then check if any vendors exist for this tenant
    const tenantVendorsCount = await db
      .select({ count: sql`count(*)` })
      .from(vendors)
      .where(eq(vendors.tenantId, tenantId));
    
    console.log("Vendors for this tenant:", tenantVendorsCount[0]?.count);

    // Fetch all vendors without any tenant filter first (for debugging)
    const allVendors = await db.query.vendors.findMany({
      limit: 10,
      orderBy: [desc(vendors.createdAt)],
    });

    console.log("Sample of all vendors:", allVendors.map(v => ({ id: v.id, name: v.name, tenantId: v.tenantId })));

    // Now fetch all vendors for this tenant
    const vendorsList = await db.query.vendors.findMany({
      where: eq(vendors.tenantId, tenantId),
      orderBy: [desc(vendors.createdAt)],
      with: {
        user: {
          columns: {
            id: true,
            username: true,
            fullName: true,
            email: true,
            role: true,
          },
        },
      },
    });

    console.log(`Found ${vendorsList.length} vendors for tenant ${tenantId}`);

    return NextResponse.json({
      success: true,
      vendors: vendorsList,
      debug: {
        tenantId,
        totalVendors: allVendorsCount[0]?.count,
        tenantVendors: tenantVendorsCount[0]?.count,
      }
    });
  } catch (error) {
    console.error("Error fetching all vendors:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch vendors" },
      { status: 500 }
    );
  }
}
