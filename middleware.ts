import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { auth } from './auth';
import { getMenuByPath } from './src/lib/menu-list';

export async function middleware(request: NextRequest) {
  const session = await auth();
  const isLoggedIn = !!session?.user;
  const pathname = request.nextUrl.pathname;
  const isDashboardRoute = pathname.startsWith('/dashboard');

  // If trying to access dashboard without being logged in, redirect to login page
  if (isDashboardRoute && !isLoggedIn) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  // If already logged in and trying to access login page, redirect to dashboard
  if (isLoggedIn && pathname === '/') {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // Skip permission check for these routes
  const excludedRoutes = [
    '/dashboard',
    '/dashboard/profile',
    '/dashboard/settings',
    '/api/user/menus',
    '/api/user/permissions',
    '/api/user/profile'
  ];

  if (isDashboardRoute && isLoggedIn && !excludedRoutes.includes(pathname)) {
    // Admin and tenant users have access to all routes
    if (session.user.role === 'admin' || session.user.role === 'tenant') {
      return NextResponse.next();
    }

    // For tenant_sale users, check permissions
    if (session.user.role === 'tenant_sale' && session.user.tenantId) {
      // Skip permission check for API routes (they have their own permission checks)
      if (pathname.startsWith('/api/')) {
        return NextResponse.next();
      }

      // Get the menu path from the pathname
      // Remove any dynamic segments (e.g., /dashboard/customers/123 -> /dashboard/customers)
      let menuPath = pathname;
      const pathParts = pathname.split('/');

      // Handle special cases for nested routes
      if (pathParts.length > 3) {
        // Special nested routes that should keep their full path
        const specialNestedRoutes = [
          '/dashboard/products/categories',
          '/dashboard/reports/orders',
          '/dashboard/reports/sales',
          '/dashboard/reports/inventory',
          '/dashboard/settings/users',
          '/dashboard/settings/branches'
        ];

        if (specialNestedRoutes.includes(pathname)) {
          menuPath = pathname;
        }
        // For routes with dynamic segments like [id] or specific actions like 'add', 'edit', etc.
        else if (pathParts[3] && (pathParts[3].includes('[') || ['add', 'edit', 'new', 'view', 'details'].includes(pathParts[3]))) {
          menuPath = `/${pathParts[1]}/${pathParts[2]}`;
        }
        // For other nested routes, check if there's a parent permission
        else if (pathParts[3]) {
          menuPath = `/${pathParts[1]}/${pathParts[2]}`;
        }
      }

      // Get user's permissions from session
      const menuPermissions = session.user.menuPermissions || [];

      console.log(`Checking permission for path: ${menuPath}`);
      console.log(`User has ${menuPermissions.length} permissions in session`);

      // Log all permissions for debugging
      if (menuPermissions.length > 0) {
        console.log('Available permissions:');
        menuPermissions.forEach((p: any) => {
          console.log(`- ${p.path}: view=${p.canView}, create=${p.canCreate}, edit=${p.canEdit}, delete=${p.canDelete}`);
        });
      }

      // Check if user has permission to access this menu
      const hasPermission = menuPermissions.some((permission: any) =>
        permission.path === menuPath && permission.canView
      );

      if (!hasPermission) {
        console.log(`User ${session.user.id} does not have permission to access ${menuPath}`);
        // Redirect to dashboard without any notification
        return NextResponse.redirect(new URL('/dashboard', request.url));
      } else {
        console.log(`User ${session.user.id} has permission to access ${menuPath}`);
      }
    }
  }

  return NextResponse.next();
}

// Configure which paths this middleware will run on
export const config = {
  matcher: ['/', '/dashboard/:path*'],
};