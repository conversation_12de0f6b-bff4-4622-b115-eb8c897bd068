import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { db } from '@/lib/db';
import { users, tenants, tenantUsers, userMenuPermissions, menus } from '@/db/schema';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcryptjs';
import { eq, and } from 'drizzle-orm';

// Mock fetch for testing login
global.fetch = vi.fn();

describe('Tenant Permissions System', () => {
  // Test data
  const testTenantId = uuidv4();
  const testTenantSaleId = uuidv4();
  const testMenuId = uuidv4();
  
  // Setup test data
  beforeAll(async () => {
    // Create test menu
    await db.insert(menus).values({
      id: testMenuId,
      name: 'test-menu',
      displayName: 'Test Menu',
      path: '/dashboard/test',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    // Create tenant user
    await db.insert(users).values({
      id: testTenantId,
      username: 'test-tenant',
      email: '<EMAIL>',
      password: await bcrypt.hash('password123', 10),
      fullName: 'Test Tenant',
      role: 'tenant',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    // Create tenant record with active subscription
    const activeEndDate = new Date();
    activeEndDate.setDate(activeEndDate.getDate() + 30); // 30 days from now
    
    await db.insert(tenants).values({
      id: uuidv4(),
      userId: testTenantId,
      companyName: 'Test Company',
      subscriptionType: 'paid',
      subscriptionStartDate: new Date(),
      subscriptionEndDate: activeEndDate,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    // Create tenant_sale user
    await db.insert(users).values({
      id: testTenantSaleId,
      username: 'test-sale',
      email: '<EMAIL>',
      password: await bcrypt.hash('password123', 10),
      fullName: 'Test Sale User',
      role: 'tenant_sale',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    // Create tenant-user relationship
    await db.insert(tenantUsers).values({
      id: uuidv4(),
      tenantId: testTenantId,
      userId: testTenantSaleId,
      role: 'staff',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    // Add menu permission
    await db.insert(userMenuPermissions).values({
      id: uuidv4(),
      userId: testTenantSaleId,
      menuId: testMenuId,
      tenantId: testTenantId,
      canView: true,
      canCreate: false,
      canEdit: false,
      canDelete: false,
      createdAt: new Date(),
      updatedAt: new Date()
    });
  });
  
  // Clean up test data
  afterAll(async () => {
    await db.delete(userMenuPermissions).where(eq(userMenuPermissions.userId, testTenantSaleId));
    await db.delete(tenantUsers).where(eq(tenantUsers.userId, testTenantSaleId));
    await db.delete(users).where(eq(users.id, testTenantSaleId));
    await db.delete(tenants).where(eq(tenants.userId, testTenantId));
    await db.delete(users).where(eq(users.id, testTenantId));
    await db.delete(menus).where(eq(menus.id, testMenuId));
  });
  
  it('tenant_sale user can login when tenant has active subscription', async () => {
    // Mock successful login response
    (fetch as any).mockResolvedValueOnce({
      json: async () => ({ success: true, user: { role: 'tenant_sale' } }),
    });
    
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: '<EMAIL>', password: 'password123' }),
    });
    
    const data = await response.json();
    expect(data.success).toBe(true);
  });
  
  it('tenant_sale user cannot login when tenant subscription expires', async () => {
    // Update tenant subscription to expired
    const expiredDate = new Date();
    expiredDate.setDate(expiredDate.getDate() - 1); // 1 day ago
    
    await db.update(tenants)
      .set({ 
        subscriptionEndDate: expiredDate,
        updatedAt: new Date()
      })
      .where(eq(tenants.userId, testTenantId));
    
    // Mock failed login response
    (fetch as any).mockResolvedValueOnce({
      json: async () => ({ 
        success: false, 
        error: 'Tenant subscription has expired',
        status: 401
      }),
    });
    
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: '<EMAIL>', password: 'password123' }),
    });
    
    const data = await response.json();
    expect(data.success).toBe(false);
    expect(data.error).toBe('Tenant subscription has expired');
  });
  
  it('tenant_sale user can only access menus they have permission for', async () => {
    // Reset tenant subscription to active
    const activeEndDate = new Date();
    activeEndDate.setDate(activeEndDate.getDate() + 30); // 30 days from now
    
    await db.update(tenants)
      .set({ 
        subscriptionEndDate: activeEndDate,
        updatedAt: new Date()
      })
      .where(eq(tenants.userId, testTenantId));
    
    // Check if user has permission for the test menu
    const permissions = await db.query.userMenuPermissions.findMany({
      where: and(
        eq(userMenuPermissions.userId, testTenantSaleId),
        eq(userMenuPermissions.menuId, testMenuId)
      )
    });
    
    expect(permissions.length).toBeGreaterThan(0);
    expect(permissions[0].canView).toBe(true);
    
    // Create a new menu without permissions
    const newMenuId = uuidv4();
    await db.insert(menus).values({
      id: newMenuId,
      name: 'no-access-menu',
      displayName: 'No Access Menu',
      path: '/dashboard/no-access',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    // Check that user doesn't have permission for the new menu
    const noPermissions = await db.query.userMenuPermissions.findMany({
      where: and(
        eq(userMenuPermissions.userId, testTenantSaleId),
        eq(userMenuPermissions.menuId, newMenuId)
      )
    });
    
    expect(noPermissions.length).toBe(0);
    
    // Clean up new menu
    await db.delete(menus).where(eq(menus.id, newMenuId));
  });
});
