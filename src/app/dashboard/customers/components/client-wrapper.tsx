'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Plus, Eye, Edit, DollarSign, Trash2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useSession } from 'next-auth/react';
import { toast as sonnerToast } from 'sonner';
import { Badge } from '@/components/ui/badge';
import {
    Table,
    TableHeader,
    TableBody,
    TableCell,
    TableRow,
    TableHead,
} from '@/components/ui/table';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { formatCurrency } from '@/lib/utils';

interface Customer {
    id: string;
    name: string;
    code: string | null;
    email: string | null;
    phone: string | null;
    type: string;
    address: string | null;
    creditLimit: number | null;
    currentBalance: number | null;
}

interface ClientWrapperProps {
    customers: Customer[];
    filterDue: boolean;
    searchQuery: string | undefined;
}

export default function ClientWrapper({ customers, filterDue, searchQuery }: ClientWrapperProps) {
    const { toast } = useToast();
    const { data: session } = useSession();
    const [userPermissions, setUserPermissions] = useState<{
        canView: boolean;
        canCreate: boolean;
        canEdit: boolean;
        canDelete: boolean;
    }>({ canView: false, canCreate: false, canEdit: false, canDelete: false });
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null);

    useEffect(() => {
        if (session) {
            fetchUserPermissions();
        }
    }, [session]);

    // Fetch user permissions
    const fetchUserPermissions = async () => {
        try {
            if (!session) {
                return; // Wait for session to be loaded
            }

            if (session?.user?.role === 'admin' || session?.user?.role === 'tenant') {
                setUserPermissions({
                    canView: true,
                    canCreate: true,
                    canEdit: true,
                    canDelete: true
                });
                return;
            }

            // For tenant_sale users, fetch their specific permissions from the API
            const response = await fetch(`/api/user/permissions?menuPath=/dashboard/customers`);
            const data = await response.json();

            if (data.success) {
                setUserPermissions(data.permissions);
            } else {
                console.error('Error fetching permissions:', data.error);
                sonnerToast.error('Failed to load permissions');
            }
        } catch (error) {
            console.error('Error fetching user permissions:', error);
            sonnerToast.error('Failed to load user permissions');
        }
    };

    // Handle delete customer
    const handleDeleteClick = (customer: Customer) => {
        if (!!customer.currentBalance && customer.currentBalance > 0) {
            sonnerToast.error("Cannot delete customer with outstanding balance");
            return;
        }
        setCustomerToDelete(customer);
        setIsDeleteDialogOpen(true);
    };

    const confirmDelete = async () => {
        if (!customerToDelete) return;

        try {
            const response = await fetch(`/api/customers/${customerToDelete.id}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (response.ok) {
                sonnerToast.success("Customer deleted successfully");
                // Refresh the page to update the customer list
                window.location.reload();
            } else {
                const data = await response.json();
                sonnerToast.error(data.message || "Failed to delete customer");
            }
        } catch (error) {
            console.error('Error deleting customer:', error);
            sonnerToast.error("An error occurred while deleting the customer");
        } finally {
            setIsDeleteDialogOpen(false);
            setCustomerToDelete(null);
        }
    };

    return (
        <>
            <div className="flex space-x-2">
                {filterDue ? (
                    <Link href="/dashboard/customers">
                        <Button variant="outline" className="cursor-pointer">
                            View All Customers
                        </Button>
                    </Link>
                ) : (
                    <Link href="/dashboard/customers?filter=due">
                        <Button variant="outline" className="cursor-pointer">
                            <span className="text-2xl relative bottom-1">৳</span>
                            View With Dues
                        </Button>
                    </Link>
                )}
                {userPermissions.canCreate && (
                    <Link href="/dashboard/customers/new">
                        <Button className="cursor-pointer">
                            <Plus className="w-4 h-4" />
                            Add Customer
                        </Button>
                    </Link>
                )}
            </div>

            <div className="overflow-x-auto">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Customer Name</TableHead>
                            <TableHead>Code</TableHead>
                            <TableHead>Phone</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead className="text-right">Credit Limit</TableHead>
                            <TableHead className="text-right">Current Balance</TableHead>
                            <TableHead className="text-center">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {customers.length > 0 ? (
                            customers.map((customer) => (
                                <TableRow key={customer.id}>
                                    <TableCell className="font-medium">{customer.name}</TableCell>
                                    <TableCell>{customer.code || "—"}</TableCell>
                                    <TableCell>{customer.phone || "—"}</TableCell>
                                    <TableCell>{customer.email || "—"}</TableCell>
                                    <TableCell>
                                        <Badge variant="outline" className={
                                            customer.type === "wholesale"
                                                ? "bg-blue-50 text-blue-700 border-blue-200"
                                                : "bg-green-50 text-green-700 border-green-200"
                                        }>
                                            {customer.type === "wholesale" ? "Wholesale" : "Retail"}
                                        </Badge>
                                    </TableCell>
                                    <TableCell className="text-right">
                                        {formatCurrency(customer.creditLimit || 0)}
                                    </TableCell>
                                    <TableCell className="text-right">
                                        {!!customer.currentBalance && customer.currentBalance > 0 ? (
                                            <span className="text-red-600 font-medium">{formatCurrency(customer.currentBalance)}</span>
                                        ) : (
                                            <span className="text-green-600 font-medium">{formatCurrency(customer.currentBalance || 0)}</span>
                                        )}
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex justify-center space-x-2">
                                            {userPermissions.canView && (
                                                <Link href={`/dashboard/customers/${customer.id}`}>
                                                    <Button variant="outline" size="sm" className="h-8 w-8 p-0 cursor-pointer">
                                                        <Eye className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                            )}
                                            {userPermissions.canEdit && (
                                                <Link href={`/dashboard/customers/${customer.id}/edit`}>
                                                    <Button variant="outline" size="sm" className="h-8 w-8 p-0 cursor-pointer">
                                                        <Edit className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                            )}
                                            {userPermissions.canCreate && !!customer.currentBalance && customer.currentBalance > 0 ? (
                                                <Link href={`/dashboard/payments/new?customerId=${customer.id}`}>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        className="h-8 w-8 p-0 bg-green-50 text-green-600 border-green-200 hover:bg-green-100 cursor-pointer"
                                                    >
                                                        <DollarSign className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                            ) : null}
                                            {userPermissions.canDelete && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="h-8 w-8 p-0 bg-red-50 text-red-600 border-red-200 hover:bg-red-100 cursor-pointer"
                                                    onClick={() => handleDeleteClick(customer)}
                                                    disabled={!!customer.currentBalance && customer.currentBalance > 0}
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            )}
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan={8} className="text-center py-4 text-gray-500">
                                    {searchQuery
                                        ? "No customers found matching your search criteria."
                                        : filterDue
                                            ? "No customers with outstanding balances found."
                                            : "No customers found. Add your first customer to get started."}
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure you want to delete this customer?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action will remove the customer from view but will not delete their data from the database.
                            This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={confirmDelete} className="bg-red-600 hover:bg-red-700">
                            Delete
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
}
