import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { customers, payments, branches } from "@/db/schema";
import { auth } from "~/auth";
import { eq, and } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";
import { revalidatePath } from "next/cache";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const { id: customerId } = await params;
    const tenantId = session.user.tenantId || session.user.id;
    const data = await request.json();

    // Validate required fields
    if (
      !data.amount ||
      data.amount <= 0 ||
      !data.adjustmentType
    ) {
      return NextResponse.json(
        { message: "Missing required fields or invalid amount" },
        { status: 400 }
      );
    }

    // Check if customer exists and belongs to tenant
    const customer = await db.query.customers.findFirst({
      where: (customers, { eq, and }) =>
        and(eq(customers.id, customerId), eq(customers.tenantId, tenantId)),
    });

    if (!customer) {
      return NextResponse.json(
        { message: "Customer not found" },
        { status: 404 }
      );
    }

    // Get a valid branch ID
    let branchId = data.branchId;
    if (!branchId) {
      // Try to find a branch for this tenant
      const tenantBranches = await db
        .select()
        .from(branches)
        .where(eq(branches.tenantId, tenantId));

      if (tenantBranches.length > 0) {
        // Use the first branch or a main branch if available
        const mainBranch = tenantBranches.find(b => b.isMain);
        branchId = mainBranch ? mainBranch.id : tenantBranches[0].id;
      } else {
        // Create a default branch if none exists
        const newBranchId = uuidv4();
        await db.insert(branches).values({
          id: newBranchId,
          tenantId,
          name: "Main Store",
          code: "MAIN",
          isMain: true,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
        branchId = newBranchId;
      }
    }

    // Calculate the new balance based on adjustment type
    const currentBalance = customer.currentBalance || 0;
    let newBalance = currentBalance;
    let paymentType = "";

    if (data.adjustmentType === "increase_due") {
      // Increase due amount (add to current balance)
      newBalance = currentBalance + data.amount;
      paymentType = "due_adjustment";
    } else if (data.adjustmentType === "decrease_due") {
      // Decrease due amount (subtract from current balance)
      if (data.amount > currentBalance) {
        return NextResponse.json(
          { message: "Adjustment amount exceeds current due balance" },
          { status: 400 }
        );
      }
      newBalance = currentBalance - data.amount;
      paymentType = "due_payment";
    } else if (data.adjustmentType === "add_advance") {
      // Add advanced payment (subtract from current balance)
      newBalance = currentBalance - data.amount;
      paymentType = "advance_payment";
    } else if (data.adjustmentType === "use_advance") {
      // Use advanced payment (add to current balance)
      if (currentBalance >= 0) {
        return NextResponse.json(
          { message: "Customer has no advanced payment to use" },
          { status: 400 }
        );
      }

      const advancedAmount = Math.abs(currentBalance);
      if (data.amount > advancedAmount) {
        return NextResponse.json(
          { message: "Adjustment amount exceeds available advanced payment" },
          { status: 400 }
        );
      }

      newBalance = currentBalance + data.amount;
      paymentType = "advance_used";
    } else {
      return NextResponse.json(
        { message: "Invalid adjustment type" },
        { status: 400 }
      );
    }

    await db.transaction(async (tx) => {
      // Update customer balance
      await tx
        .update(customers)
        .set({
          currentBalance: newBalance,
          updatedAt: new Date(),
        })
        .where(
          and(eq(customers.id, customerId), eq(customers.tenantId, tenantId))
        );

      // Create a payment record to track this adjustment
      await tx.insert(payments).values({
        tenantId,
        branchId,
        customerId,
        orderId: null,
        amount: data.amount,
        paymentMethod: (data.paymentMethod as "cash" | "card" | "bank_transfer") || "cash",
        paymentReference: data.paymentReference || null,
        paymentType: paymentType as "sale" | "due_payment" | "due_adjustment" | "advance_payment" | "advance_used",
        notes: data.notes || `Balance adjustment: ${data.adjustmentType.replace('_', ' ')}`,
        performedBy: session.user.id,
        date: new Date(),
      });
    });

    // Revalidate the customer page to reflect the changes
    revalidatePath(`/dashboard/customers/${customerId}`);
    revalidatePath("/dashboard/customers");

    return NextResponse.json(
      {
        message: "Customer balance adjusted successfully",
        newBalance
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error adjusting customer balance:", error);
    return NextResponse.json(
      { message: "Failed to adjust customer balance", error: (error as Error).message },
      { status: 500 }
    );
  }
}
