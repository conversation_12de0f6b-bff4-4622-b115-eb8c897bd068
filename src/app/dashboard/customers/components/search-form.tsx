"use client";

import React from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Search, X } from "lucide-react";
import { Button } from "@/components/ui/button";

export function CustomersSearchForm({ defaultValue }: { defaultValue?: string }) {
    const router = useRouter();
    const searchParams = useSearchParams();
    const [searchValue, setSearchValue] = React.useState(defaultValue || "");

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        // Create new URL with search parameter
        const params = new URLSearchParams(searchParams?.toString() || "");

        if (searchValue) {
            params.set("search", searchValue);
        } else {
            params.delete("search");
        }

        // Preserve the filter parameter if it exists
        router.push(`/dashboard/customers?${params.toString()}`);
    };

    const handleReset = () => {
        // Remove search parameter and refresh the page
        setSearchValue("");
        const params = new URLSearchParams(searchParams?.toString() || "");
        params.delete("search");
        router.push(`/dashboard/customers?${params.toString()}`);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchValue(e.target.value);
    };

    return (
        <div className="w-full md:w-80">
            <form onSubmit={handleSubmit} className="relative flex gap-2">
                <div className="relative flex-1">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                    <Input
                        type="search"
                        name="search"
                        placeholder="Search by name, phone or code..."
                        className="pl-9"
                        value={searchValue}
                        onChange={handleChange}
                    />
                </div>
                {searchValue && (
                    <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={handleReset}
                        className="shrink-0"
                    >
                        <X className="h-4 w-4" />
                    </Button>
                )}
            </form>
        </div>
    );
}