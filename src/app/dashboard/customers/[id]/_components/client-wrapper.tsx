'use client';

import { usePermissionCheck } from '@/lib/check-permissions';
import Link from "next/link";
import { formatCurrency } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
    ChevronLeft,
    Edit,
    DollarSign,
    Phone,
    Mail,
    MapPin,
    CreditCard,
    ClipboardList,
    Calendar,
    Percent,
    Wallet,
} from "lucide-react";
import { BalanceAdjustmentDialog } from "./balance-adjustment-dialog";

interface CustomerData {
    id: string;
    name: string;
    code: string | null;
    email: string | null;
    phone: string | null;
    type: string | null;
    address: string | null;
    creditLimit: number | null;
    currentBalance: number | null;
    extraCommission: number | null;
    notes: string | null;
    createdAt: Date | null;
}

interface ClientWrapperProps {
    customer: CustomerData;
    formattedCreatedAt: string;
}

export default function ClientWrapper({ customer, formattedCreatedAt }: ClientWrapperProps) {
    // Check if user has permission to view customer details
    const { isLoading } = usePermissionCheck('/dashboard/customers', 'view');

    if (isLoading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    return (
        <div className="flex flex-col space-y-6">
            <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                    <Link href="/dashboard/customers">
                        <Button variant="outline" size="icon" className="cursor-pointer">
                            <ChevronLeft className="h-4 w-4" />
                        </Button>
                    </Link>
                    <h1 className="text-3xl font-bold">Customer Details</h1>
                </div>
                <div className="flex space-x-2">
                    {customer.currentBalance && customer.currentBalance > 0 ? (
                        <Link href={`/dashboard/payments/new?customerId=${customer.id}`}>
                            <Button variant="outline" className="flex items-center gap-2">
                                <DollarSign className="h-4 w-4" />
                                <span>Collect Payment</span>
                            </Button>
                        </Link>
                    ) : null}
                    <BalanceAdjustmentDialog
                        customerId={customer.id}
                        customerName={customer.name}
                        currentBalance={customer.currentBalance || 0}
                    />
                    <Link href={`/dashboard/customers/${customer.id}/edit`}>
                        <Button className="cursor-pointer flex items-center gap-2">
                            <Edit className="h-4 w-4" />
                            <span>Edit Customer</span>
                        </Button>
                    </Link>
                </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Main Customer Info Card */}
                <Card className="lg:col-span-2">
                    <CardHeader>
                        <CardTitle>Customer Information</CardTitle>
                        <CardDescription>Basic details about this customer</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <div>
                            <h2 className="text-2xl font-semibold mb-2">{customer.name}</h2>
                            <div className="flex flex-wrap gap-2">
                                <Badge
                                    variant="outline"
                                    className={
                                        customer.type === "wholesale"
                                            ? "bg-blue-50 text-blue-700 border-blue-200"
                                            : "bg-green-50 text-green-700 border-green-200"
                                    }
                                >
                                    {customer.type === "wholesale" ? "Wholesale" : "Retail"}
                                </Badge>
                                {customer.code && (
                                    <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
                                        Code: {customer.code}
                                    </Badge>
                                )}
                            </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="flex items-start gap-2">
                                <Phone className="h-5 w-5 text-gray-500 mt-0.5" />
                                <div>
                                    <p className="text-sm text-gray-500">Phone Number</p>
                                    <p className="font-medium">{customer.phone || "—"}</p>
                                </div>
                            </div>

                            <div className="flex items-start gap-2">
                                <Mail className="h-5 w-5 text-gray-500 mt-0.5" />
                                <div>
                                    <p className="text-sm text-gray-500">Email</p>
                                    <p className="font-medium">{customer.email || "—"}</p>
                                </div>
                            </div>

                            <div className="flex items-start gap-2">
                                <MapPin className="h-5 w-5 text-gray-500 mt-0.5" />
                                <div>
                                    <p className="text-sm text-gray-500">Address</p>
                                    <p className="font-medium">{customer.address || "—"}</p>
                                </div>
                            </div>

                            <div className="flex items-start gap-2">
                                <Calendar className="h-5 w-5 text-gray-500 mt-0.5" />
                                <div>
                                    <p className="text-sm text-gray-500">Customer Since</p>
                                    <p className="font-medium">{formattedCreatedAt}</p>
                                </div>
                            </div>
                        </div>

                        {customer.notes && (
                            <div className="mt-4">
                                <div className="flex items-start gap-2">
                                    <ClipboardList className="h-5 w-5 text-gray-500 mt-0.5" />
                                    <div>
                                        <p className="text-sm text-gray-500">Notes</p>
                                        <p className="font-medium">{customer.notes}</p>
                                    </div>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Financial Info Card */}
                <Card>
                    <CardHeader>
                        <CardTitle>Financial Information</CardTitle>
                        <CardDescription>Balance and credit details</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <div className="flex flex-col p-6 border rounded-lg bg-gray-50">
                            <p className="text-sm text-gray-500 mb-1">
                                {customer.currentBalance && customer.currentBalance < 0
                                    ? "Advanced Payment"
                                    : "Current Balance"}
                            </p>
                            <div className="flex items-center">
                                <h3
                                    className={`text-3xl font-bold ${customer.currentBalance && customer.currentBalance > 0
                                        ? "text-red-600"
                                        : customer.currentBalance && customer.currentBalance < 0
                                            ? "text-blue-600"
                                            : "text-green-600"
                                        }`}
                                >
                                    {customer.currentBalance && customer.currentBalance < 0
                                        ? formatCurrency(Math.abs(customer.currentBalance))
                                        : formatCurrency(customer.currentBalance || 0)}
                                </h3>
                                {customer.currentBalance && customer.currentBalance > 0 ? (
                                    <Badge variant="outline" className="ml-2 bg-red-50 text-red-600 border-red-200">
                                        Due
                                    </Badge>
                                ) : customer.currentBalance && customer.currentBalance < 0 ? (
                                    <Badge variant="outline" className="ml-2 bg-blue-50 text-blue-600 border-blue-200">
                                        Advanced
                                    </Badge>
                                ) : (
                                    <Badge variant="outline" className="ml-2 bg-green-50 text-green-600 border-green-200">
                                        Paid
                                    </Badge>
                                )}
                            </div>
                        </div>

                        <div className="grid grid-cols-1 gap-4">
                            <div className="flex items-start gap-2">
                                <CreditCard className="h-5 w-5 text-gray-500 mt-0.5" />
                                <div>
                                    <p className="text-sm text-gray-500">Credit Limit</p>
                                    <p className="font-medium">{formatCurrency(customer.creditLimit || 0)}</p>
                                </div>
                            </div>

                            <div className="flex items-start gap-2">
                                <Percent className="h-5 w-5 text-gray-500 mt-0.5" />
                                <div>
                                    <p className="text-sm text-gray-500">Extra Commission</p>
                                    <p className="font-medium">
                                        {customer.extraCommission || 0}%
                                    </p>
                                </div>
                            </div>

                            {customer.currentBalance && customer.creditLimit && customer.currentBalance > 0 ? (
                                <div className="flex items-start gap-2">
                                    <div className="h-5 w-5 flex items-center justify-center text-gray-500 mt-0.5">
                                        %
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-500">Credit Used</p>
                                        <p className="font-medium">
                                            {Math.min(
                                                Math.round(((customer.currentBalance || 0) / (customer.creditLimit || 1)) * 100),
                                                100
                                            )}%
                                        </p>
                                    </div>
                                </div>
                            ) : null}
                        </div>

                        <div className="flex flex-col space-y-2">
                            {customer.currentBalance && customer.currentBalance > 0 ? (
                                <Link href={`/dashboard/payments/new?customerId=${customer.id}`} className="w-full">
                                    <Button className="w-full flex items-center justify-center gap-2">
                                        <DollarSign className="h-4 w-4" />
                                        <span>Collect Payment</span>
                                    </Button>
                                </Link>
                            ) : null}

                            <Button
                                variant="outline"
                                className="w-full flex items-center justify-center gap-2"
                                onClick={() => {
                                    // Find and click the balance adjustment dialog trigger
                                    const adjustButton = document.querySelector('[data-balance-adjust-trigger]');
                                    if (adjustButton instanceof HTMLElement) {
                                        adjustButton.click();
                                    }
                                }}
                            >
                                <Wallet className="h-4 w-4" />
                                <span>Manage Due/Advanced Payment</span>
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
