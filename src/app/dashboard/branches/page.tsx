'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { EyeIcon } from 'lucide-react';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';
import { MagnifyingGlassIcon, ReloadIcon } from '@radix-ui/react-icons';

interface Branch {
    id: string;
    name: string;
    code: string;
    address: string | null;
    phone: string | null;
    email: string | null;
    isMain: boolean;
    isActive: boolean;
    notes: string | null;
    createdAt: string;
}

const formSchema = z.object({
    name: z.string().min(2, { message: 'Branch name is required' }),
    code: z.string().min(2, { message: 'Branch code is required' }),
    address: z.string().min(1, { message: 'Address is required' }),
    phone: z.string().min(1, { message: 'Phone number is required' }),
    email: z.string().email({ message: 'Invalid email' }),
    isMain: z.boolean(),
    notes: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

export default function BranchesPage() {
    const router = useRouter();
    const [branches, setBranches] = useState<Branch[]>([]);
    const [loading, setLoading] = useState(true);
    const [dialogOpen, setDialogOpen] = useState(false);
    const [deletingBranchId, setDeletingBranchId] = useState<string | null>(null);
    const [searchQuery, setSearchQuery] = useState('');

    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: '',
            code: '',
            address: '',
            phone: '',
            email: '',
            isMain: false,
            notes: '',
        },
    });

    const fetchBranches = async (search?: string) => {
        try {
            const url = new URL('/api/branches', window.location.origin);
            if (search) {
                url.searchParams.set('search', search);
            }
            const response = await fetch(url.toString());
            const data = await response.json();

            if (data.success) {
                setBranches(data.branches);
            } else {
                toast.error(data.error || 'Failed to load branches');
            }
        } catch (error) {
            console.error('Error fetching branches:', error);
            toast.error('An error occurred while fetching branches');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchBranches(searchQuery);
    }, [searchQuery]);

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        fetchBranches(searchQuery);
    };

    const handleReset = () => {
        setSearchQuery('');
        fetchBranches();
    };

    const onSubmit = async (values: FormValues) => {
        try {
            const response = await fetch('/api/branches', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(values),
            });

            const data = await response.json();

            if (data.success) {
                toast.success('Branch created successfully');
                setDialogOpen(false);
                form.reset();
                fetchBranches(searchQuery);
            } else {
                toast.error(data.error || 'Failed to create branch');
            }
        } catch (error) {
            console.error('Error creating branch:', error);
            toast.error('An error occurred while creating the branch');
        }
    };

    const handleDeleteBranch = async (id: string) => {
        try {
            setDeletingBranchId(id);

            const response = await fetch(`/api/branches/${id}`, {
                method: 'DELETE',
            });

            const data = await response.json();

            if (data.success) {
                toast.success('Branch deleted successfully');
                fetchBranches(searchQuery);
            } else {
                toast.error(data.error || 'Failed to delete branch');
            }
        } catch (error) {
            console.error('Error deleting branch:', error);
            toast.error('An error occurred while deleting the branch');
        } finally {
            setDeletingBranchId(null);
        }
    };

    return (
        <div className="container mx-auto py-6 space-y-6">
            <div className="flex justify-between items-center">
                <h1 className="text-2xl font-bold">Branch Management</h1>

                <div className='flex items-center gap-2'>
                    <form onSubmit={(e) => {
                        e.preventDefault();
                        handleSearch(e);
                    }} className="flex gap-2">
                        <div className="flex items-center space-x-2">
                            <Input
                                type="text"
                                placeholder="Search branches..."
                                className="w-64"
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />
                            <Button
                                type="button"
                                variant="outline"
                                size="icon"
                                onClick={() => handleReset()}
                                className="cursor-pointer"
                            >
                                <ReloadIcon className="h-4 w-4" />
                            </Button>
                        </div>
                    </form>
                    <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                        <DialogTrigger asChild>
                            <Button className='cursor-pointer'>Add New Branch</Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-[500px]">
                            <DialogHeader>
                                <DialogTitle>Create New Branch</DialogTitle>
                                {/* <DialogDescription>
                                    Add a new branch to your organization. Fill in the details below.
                                </DialogDescription> */}
                            </DialogHeader>

                            <Form {...form}>
                                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 pt-4">
                                    <FormField
                                        control={form.control}
                                        name="name"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Branch Name</FormLabel>
                                                <FormControl>
                                                    <Input placeholder="Main Branch" {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="code"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Branch Code</FormLabel>
                                                <FormControl>
                                                    <Input placeholder="MB001" {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="address"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Address</FormLabel>
                                                <FormControl>
                                                    <Textarea
                                                        placeholder="Enter branch address"
                                                        {...field}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="phone"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Phone</FormLabel>
                                                <FormControl>
                                                    <Input
                                                        type="tel"
                                                        placeholder="Enter phone number"
                                                        {...field}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="email"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Email</FormLabel>
                                                <FormControl>
                                                    <Input
                                                        type="email"
                                                        placeholder="Enter email address"
                                                        {...field}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="isMain"
                                        render={({ field }) => (
                                            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                                                <FormControl>
                                                    <Checkbox
                                                        checked={field.value}
                                                        onCheckedChange={field.onChange}
                                                    />
                                                </FormControl>
                                                <div className="space-y-1 leading-none">
                                                    <FormLabel>
                                                        Set as Main Branch
                                                    </FormLabel>
                                                </div>
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="notes"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Notes</FormLabel>
                                                <FormControl>
                                                    <Textarea
                                                        placeholder="Enter additional notes"
                                                        {...field}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <DialogFooter>
                                        <Button type="submit" className='cursor-pointer'>Create Branch</Button>
                                    </DialogFooter>
                                </form>
                            </Form>
                        </DialogContent>
                    </Dialog>
                </div>
            </div>

            {loading ? (
                <div className="flex justify-center items-center py-8">
                    <ReloadIcon className="h-6 w-6 animate-spin" />
                </div>
            ) : branches.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                    No branches found
                </div>
            ) : (
                <div className="border rounded-lg">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Name</TableHead>
                                <TableHead>Code</TableHead>
                                <TableHead>Address</TableHead>
                                <TableHead>Phone</TableHead>
                                <TableHead>Email</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {branches.map((branch) => (
                                <TableRow key={branch.id}>
                                    <TableCell className="font-medium">
                                        {branch.name}
                                        {branch.isMain && (
                                            <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                                Main
                                            </span>
                                        )}
                                    </TableCell>
                                    <TableCell>{branch.code}</TableCell>
                                    <TableCell>{branch.address || '-'}</TableCell>
                                    <TableCell>{branch.phone || '-'}</TableCell>
                                    <TableCell>{branch.email || '-'}</TableCell>
                                    <TableCell>
                                        <span
                                            className={`px-2 py-1 rounded text-xs ${branch.isActive
                                                ? 'bg-green-100 text-green-800'
                                                : 'bg-red-100 text-red-800'
                                                }`}
                                        >
                                            {branch.isActive ? 'Active' : 'Inactive'}
                                        </span>
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex items-center gap-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => router.push(`/dashboard/branches/${branch.id}?view=true`)}
                                                className='cursor-pointer'
                                                title="View branch details"
                                            >
                                                <EyeIcon className="h-4 w-4 mr-1" />
                                                View
                                            </Button>
                                            {branch.isMain ? (
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    disabled={true}
                                                    title="Main branch cannot be deleted"
                                                    className='cursor-not-allowed opacity-50'
                                                >
                                                    Delete
                                                </Button>
                                            ) : (
                                                <AlertDialog>
                                                    <AlertDialogTrigger asChild>
                                                        <Button
                                                            variant="destructive"
                                                            size="sm"
                                                            className='cursor-pointer'
                                                        >
                                                            Delete
                                                        </Button>
                                                    </AlertDialogTrigger>
                                                    <AlertDialogContent>
                                                        <AlertDialogHeader>
                                                            <AlertDialogTitle>Delete Branch</AlertDialogTitle>
                                                            <AlertDialogDescription>
                                                                Are you sure you want to delete this branch? This action cannot be undone.
                                                            </AlertDialogDescription>
                                                        </AlertDialogHeader>
                                                        <AlertDialogFooter>
                                                            <AlertDialogCancel className='cursor-pointer' >Cancel</AlertDialogCancel>
                                                            <AlertDialogAction
                                                                onClick={() => handleDeleteBranch(branch.id)}
                                                                className="bg-red-600 hover:bg-red-700 cursor-pointer"
                                                            >
                                                                {deletingBranchId === branch.id ? (
                                                                    <ReloadIcon className="h-4 w-4 animate-spin" />
                                                                ) : (
                                                                    'Delete'
                                                                )}
                                                            </AlertDialogAction>
                                                        </AlertDialogFooter>
                                                    </AlertDialogContent>
                                                </AlertDialog>
                                            )}
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
            )}
        </div>
    );
}