"use client";

import { useState } from "react";

export default function PrintButton() {
    const [loading, setLoading] = useState(false);

    const handlePrint = (e: React.MouseEvent) => {
        // Prevent any default navigation behavior
        e.preventDefault();

        setLoading(true);

        // Short delay to ensure the page is fully rendered
        setTimeout(() => {
            // Open the print dialog
            window.print();
            setLoading(false);
        }, 100);
    };

    return (
        <button
            onClick={handlePrint}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-blue-300 cursor-pointer"
            disabled={loading}
        >
            {loading ? "Printing..." : "Print Invoice"}
        </button>
    );
} 