import { db } from "@/lib/db";
import { tenants, users } from "@/db/schema";
import { auth } from "../../auth";
import { eq, and } from "drizzle-orm";

/**
 * Gets the tenant ID for the currently logged-in user
 *
 * For tenant owners, it returns their tenant ID
 * For users with tenant memberships, it returns the first active tenant they belong to
 */
export async function getTenantId(): Promise<string | null> {
  const session = await auth();

  if (!session?.user?.id) {
    return null;
  }

  const userId = session.user.id;

  // First check if user is a tenant owner
  const userTenant = await db.query.users.findFirst({
    where: eq(users.id, userId),
    columns: {
      id: true,
    },
  });

  if (userTenant) {
    return userTenant.id;
  }

  // If not an owner, check for tenant memberships
  const userMembership = await db.query.tenants.findFirst({
    where: and(eq(tenants.userId, userId), eq(tenants.isActive, true)),
    columns: {
      id: true,
    },
  });

  return userMembership?.id || null;
}
